﻿using System;
using System.Data.SqlClient;
using System.IO;
using System.IO.Compression;
using System.Windows.Forms;
using EmployeeManagementSystem;

public static class BackupHelper
{
    public static void BackupDatabaseAndFiles(string backupFolderPath)
    {
        try
        {
            if (!Directory.Exists(backupFolderPath))
                Directory.CreateDirectory(backupFolderPath);

            // مسار مجلد البرنامج
            string basePath = AppDomain.CurrentDomain.BaseDirectory;

            // مجلد النسخ المؤقت
            string tempBackupFolder = Path.Combine(backupFolderPath, "TempBackup");
            if (Directory.Exists(tempBackupFolder))
                Directory.Delete(tempBackupFolder, true);
            Directory.CreateDirectory(tempBackupFolder);

            // نسخ ملفات قاعدة البيانات (مثلاً ملف MDF و LDF) - عيّن المسارات حسب مشروعك
            string dbMdfPath = Path.Combine(basePath, "YourDatabaseFile.mdf");
            string dbLdfPath = Path.Combine(basePath, "YourDatabaseFile_log.ldf");

            if (File.Exists(dbMdfPath))
                File.Copy(dbMdfPath, Path.Combine(tempBackupFolder, Path.GetFileName(dbMdfPath)));
            if (File.Exists(dbLdfPath))
                File.Copy(dbLdfPath, Path.Combine(tempBackupFolder, Path.GetFileName(dbLdfPath)));

            // نسخ مجلد documents
            string documentsSource = Path.Combine(basePath, "documents");
            string documentsDest = Path.Combine(tempBackupFolder, "documents");

            if (Directory.Exists(documentsSource))
                CopyDirectory(documentsSource, documentsDest);

            // إنشاء ملف ZIP للنسخة الاحتياطية
            string zipFileName = $"Backup_{DateTime.Now:yyyyMMddHHmmss}.zip";
            string zipFilePath = Path.Combine(backupFolderPath, zipFileName);

            if (File.Exists(zipFilePath))
                File.Delete(zipFilePath);

            ZipFile.CreateFromDirectory(tempBackupFolder, zipFilePath);

            // حذف المجلد المؤقت
            Directory.Delete(tempBackupFolder, true);

            MessageBox.Show("تم أخذ النسخة الاحتياطية بنجاح.", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء النسخ الاحتياطي: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private static void CopyDirectory(string sourceDir, string destinationDir)
    {
        Directory.CreateDirectory(destinationDir);

        foreach (var file in Directory.GetFiles(sourceDir))
        {
            string destFile = Path.Combine(destinationDir, Path.GetFileName(file));
            File.Copy(file, destFile, true);
        }

        foreach (var directory in Directory.GetDirectories(sourceDir))
        {
            string destDir = Path.Combine(destinationDir, Path.GetFileName(directory));
            CopyDirectory(directory, destDir);
        }
    }
}
