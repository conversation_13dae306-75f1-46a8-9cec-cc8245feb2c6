namespace EmployeeManagementSystem
{
    partial class ContactForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            lblTitle = new Label();
            txtSearch = new TextBox();
            lblSearch = new Label();
            dataGridViewContacts = new DataGridView();
            btnWhatsApp = new Button();
            btnCall = new Button();
            btnRefresh = new Button();
            groupBoxEmployees = new GroupBox();
            groupBoxActions = new GroupBox();
            lblSelectedPhone = new Label();
            lblSelectedEmployee = new Label();
            toolTip1 = new ToolTip(components);
            btnClearSearch = new Button();
            statusStrip1 = new StatusStrip();
            toolStripStatusLabel1 = new ToolStripStatusLabel();
            lblEmployeeCount = new ToolStripStatusLabel();
            ((System.ComponentModel.ISupportInitialize)dataGridViewContacts).BeginInit();
            groupBoxEmployees.SuspendLayout();
            groupBoxActions.SuspendLayout();
            statusStrip1.SuspendLayout();
            SuspendLayout();
            // 
            // lblTitle
            // 
            lblTitle.Font = new Font("Cairo", 16F, FontStyle.Bold);
            lblTitle.ForeColor = Color.DarkBlue;
            lblTitle.Location = new Point(145, 10);
            lblTitle.Margin = new Padding(4, 0, 4, 0);
            lblTitle.Name = "lblTitle";
            lblTitle.Size = new Size(887, 46);
            lblTitle.TabIndex = 0;
            lblTitle.Text = "دليل الاتصال - الموظفين";
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // txtSearch
            // 
            txtSearch.Font = new Font("Cairo", 12F);
            txtSearch.Location = new Point(321, 99);
            txtSearch.Margin = new Padding(4, 3, 4, 3);
            txtSearch.Name = "txtSearch";
            txtSearch.PlaceholderText = "ابحث بالاسم أو رقم الهاتف";
            txtSearch.RightToLeft = RightToLeft.Yes;
            txtSearch.Size = new Size(349, 37);
            txtSearch.TabIndex = 1;
            toolTip1.SetToolTip(txtSearch, "ابحث بالاسم أو رقم الهاتف");
            txtSearch.TextChanged += txtSearch_TextChanged;
            // 
            // lblSearch
            // 
            lblSearch.AutoSize = true;
            lblSearch.Font = new Font("Cairo", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lblSearch.Location = new Point(261, 99);
            lblSearch.Margin = new Padding(4, 0, 4, 0);
            lblSearch.Name = "lblSearch";
            lblSearch.RightToLeft = RightToLeft.Yes;
            lblSearch.Size = new Size(56, 36);
            lblSearch.TabIndex = 2;
            lblSearch.Text = "بحث:";
            // 
            // dataGridViewContacts
            // 
            dataGridViewContacts.AllowUserToAddRows = false;
            dataGridViewContacts.AllowUserToDeleteRows = false;
            dataGridViewContacts.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridViewContacts.BackgroundColor = Color.White;
            dataGridViewCellStyle4.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = Color.FromArgb(45, 66, 91);
            dataGridViewCellStyle4.Font = new Font("Cairo", 12F, FontStyle.Bold);
            dataGridViewCellStyle4.ForeColor = Color.White;
            dataGridViewCellStyle4.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle4.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle4.WrapMode = DataGridViewTriState.True;
            dataGridViewContacts.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle4;
            dataGridViewContacts.ColumnHeadersHeight = 40;
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle5.BackColor = SystemColors.Window;
            dataGridViewCellStyle5.Font = new Font("Cairo", 11F);
            dataGridViewCellStyle5.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(87, 115, 153);
            dataGridViewCellStyle5.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            dataGridViewContacts.DefaultCellStyle = dataGridViewCellStyle5;
            dataGridViewContacts.Dock = DockStyle.Fill;
            dataGridViewContacts.EnableHeadersVisualStyles = false;
            dataGridViewContacts.Location = new Point(4, 33);
            dataGridViewContacts.Margin = new Padding(4, 3, 4, 3);
            dataGridViewContacts.MultiSelect = false;
            dataGridViewContacts.Name = "dataGridViewContacts";
            dataGridViewContacts.ReadOnly = true;
            dataGridViewContacts.RightToLeft = RightToLeft.Yes;
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle6.BackColor = SystemColors.Control;
            dataGridViewCellStyle6.Font = new Font("Cairo", 12F, FontStyle.Bold);
            dataGridViewCellStyle6.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle6.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle6.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            dataGridViewContacts.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            dataGridViewContacts.RowHeadersVisible = false;
            dataGridViewContacts.RowTemplate.Height = 35;
            dataGridViewContacts.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridViewContacts.Size = new Size(1142, 409);
            dataGridViewContacts.TabIndex = 3;
            dataGridViewContacts.CellDoubleClick += dataGridViewContacts_CellDoubleClick;
            dataGridViewContacts.SelectionChanged += dataGridViewContacts_SelectionChanged;
            // 
            // btnWhatsApp
            // 
            btnWhatsApp.BackColor = Color.FromArgb(37, 211, 102);
            btnWhatsApp.Enabled = false;
            btnWhatsApp.FlatStyle = FlatStyle.Flat;
            btnWhatsApp.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnWhatsApp.ForeColor = Color.White;
            btnWhatsApp.Image = Properties.Resources.whatsapp_32px;
            btnWhatsApp.ImageAlign = ContentAlignment.MiddleRight;
            btnWhatsApp.Location = new Point(166, 43);
            btnWhatsApp.Margin = new Padding(4, 3, 4, 3);
            btnWhatsApp.Name = "btnWhatsApp";
            btnWhatsApp.RightToLeft = RightToLeft.Yes;
            btnWhatsApp.Size = new Size(175, 52);
            btnWhatsApp.TabIndex = 4;
            btnWhatsApp.Text = "واتساب";
            btnWhatsApp.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnWhatsApp, "فتح محادثة واتساب مع الموظف المحدد");
            btnWhatsApp.UseVisualStyleBackColor = false;
            btnWhatsApp.Click += btnWhatsApp_Click;
            // 
            // btnCall
            // 
            btnCall.BackColor = Color.FromArgb(52, 152, 219);
            btnCall.Enabled = false;
            btnCall.FlatStyle = FlatStyle.Flat;
            btnCall.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnCall.ForeColor = Color.White;
            btnCall.Image = Properties.Resources.phone_32px;
            btnCall.ImageAlign = ContentAlignment.MiddleRight;
            btnCall.Location = new Point(365, 43);
            btnCall.Margin = new Padding(4, 3, 4, 3);
            btnCall.Name = "btnCall";
            btnCall.RightToLeft = RightToLeft.Yes;
            btnCall.Size = new Size(175, 52);
            btnCall.TabIndex = 5;
            btnCall.Text = "اتصال";
            btnCall.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnCall, "إجراء مكالمة هاتفية للموظف المحدد");
            btnCall.UseVisualStyleBackColor = false;
            btnCall.Click += btnCall_Click;
            // 
            // btnRefresh
            // 
            btnRefresh.BackColor = Color.FromArgb(45, 66, 91);
            btnRefresh.FlatStyle = FlatStyle.Flat;
            btnRefresh.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnRefresh.ForeColor = Color.White;
            btnRefresh.Image = Properties.Resources.update_32px;
            btnRefresh.ImageAlign = ContentAlignment.MiddleRight;
            btnRefresh.Location = new Point(803, 92);
            btnRefresh.Margin = new Padding(4, 3, 4, 3);
            btnRefresh.Name = "btnRefresh";
            btnRefresh.RightToLeft = RightToLeft.Yes;
            btnRefresh.Size = new Size(103, 52);
            btnRefresh.TabIndex = 6;
            btnRefresh.Text = "تحديث";
            btnRefresh.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnRefresh, "تحديث قائمة الموظفين");
            btnRefresh.UseVisualStyleBackColor = false;
            btnRefresh.Click += btnRefresh_Click;
            // 
            // groupBoxEmployees
            // 
            groupBoxEmployees.Controls.Add(dataGridViewContacts);
            groupBoxEmployees.Font = new Font("Cairo", 12F, FontStyle.Bold);
            groupBoxEmployees.Location = new Point(14, 150);
            groupBoxEmployees.Margin = new Padding(4, 3, 4, 3);
            groupBoxEmployees.Name = "groupBoxEmployees";
            groupBoxEmployees.Padding = new Padding(4, 3, 4, 3);
            groupBoxEmployees.RightToLeft = RightToLeft.Yes;
            groupBoxEmployees.Size = new Size(1150, 445);
            groupBoxEmployees.TabIndex = 8;
            groupBoxEmployees.TabStop = false;
            groupBoxEmployees.Text = "قائمة الموظفين";
            // 
            // groupBoxActions
            // 
            groupBoxActions.Controls.Add(lblSelectedPhone);
            groupBoxActions.Controls.Add(lblSelectedEmployee);
            groupBoxActions.Controls.Add(btnWhatsApp);
            groupBoxActions.Controls.Add(btnCall);
            groupBoxActions.Font = new Font("Cairo", 12F, FontStyle.Bold);
            groupBoxActions.Location = new Point(18, 612);
            groupBoxActions.Margin = new Padding(4, 3, 4, 3);
            groupBoxActions.Name = "groupBoxActions";
            groupBoxActions.Padding = new Padding(4, 3, 4, 3);
            groupBoxActions.RightToLeft = RightToLeft.Yes;
            groupBoxActions.Size = new Size(1142, 138);
            groupBoxActions.TabIndex = 9;
            groupBoxActions.TabStop = false;
            groupBoxActions.Text = "إجراءات الاتصال";
            // 
            // lblSelectedPhone
            // 
            lblSelectedPhone.AutoSize = true;
            lblSelectedPhone.Font = new Font("Cairo", 11F);
            lblSelectedPhone.ForeColor = Color.DarkGreen;
            lblSelectedPhone.Location = new Point(600, 73);
            lblSelectedPhone.Margin = new Padding(4, 0, 4, 0);
            lblSelectedPhone.Name = "lblSelectedPhone";
            lblSelectedPhone.RightToLeft = RightToLeft.Yes;
            lblSelectedPhone.Size = new Size(97, 29);
            lblSelectedPhone.TabIndex = 11;
            lblSelectedPhone.Text = "رقم الهاتف: -";
            // 
            // lblSelectedEmployee
            // 
            lblSelectedEmployee.AutoSize = true;
            lblSelectedEmployee.Font = new Font("Cairo", 11F);
            lblSelectedEmployee.ForeColor = Color.DarkBlue;
            lblSelectedEmployee.Location = new Point(600, 36);
            lblSelectedEmployee.Margin = new Padding(4, 0, 4, 0);
            lblSelectedEmployee.Name = "lblSelectedEmployee";
            lblSelectedEmployee.RightToLeft = RightToLeft.Yes;
            lblSelectedEmployee.Size = new Size(135, 29);
            lblSelectedEmployee.TabIndex = 10;
            lblSelectedEmployee.Text = "لم يتم اختيار موظف";
            // 
            // btnClearSearch
            // 
            btnClearSearch.BackColor = Color.FromArgb(149, 165, 166);
            btnClearSearch.FlatStyle = FlatStyle.Flat;
            btnClearSearch.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnClearSearch.ForeColor = Color.White;
            btnClearSearch.Image = Properties.Resources.clear_formatting_32px;
            btnClearSearch.ImageAlign = ContentAlignment.MiddleRight;
            btnClearSearch.Location = new Point(678, 92);
            btnClearSearch.Margin = new Padding(4, 3, 4, 3);
            btnClearSearch.Name = "btnClearSearch";
            btnClearSearch.RightToLeft = RightToLeft.Yes;
            btnClearSearch.Size = new Size(117, 52);
            btnClearSearch.TabIndex = 12;
            btnClearSearch.Text = "مسح البحث";
            btnClearSearch.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnClearSearch, "مسح نص البحث وإظهار جميع الموظفين");
            btnClearSearch.UseVisualStyleBackColor = false;
            btnClearSearch.Click += btnClearSearch_Click;
            // 
            // statusStrip1
            // 
            statusStrip1.Items.AddRange(new ToolStripItem[] { toolStripStatusLabel1, lblEmployeeCount });
            statusStrip1.Location = new Point(0, 771);
            statusStrip1.Name = "statusStrip1";
            statusStrip1.Padding = new Padding(16, 0, 1, 0);
            statusStrip1.RightToLeft = RightToLeft.Yes;
            statusStrip1.Size = new Size(1177, 22);
            statusStrip1.TabIndex = 13;
            statusStrip1.Text = "statusStrip1";
            // 
            // toolStripStatusLabel1
            // 
            toolStripStatusLabel1.Name = "toolStripStatusLabel1";
            toolStripStatusLabel1.Size = new Size(80, 17);
            toolStripStatusLabel1.Text = "عدد الموظفين:";
            // 
            // lblEmployeeCount
            // 
            lblEmployeeCount.Name = "lblEmployeeCount";
            lblEmployeeCount.Size = new Size(13, 17);
            lblEmployeeCount.Text = "0";
            // 
            // ContactForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(236, 240, 241);
            ClientSize = new Size(1177, 793);
            Controls.Add(statusStrip1);
            Controls.Add(btnClearSearch);
            Controls.Add(groupBoxActions);
            Controls.Add(groupBoxEmployees);
            Controls.Add(btnRefresh);
            Controls.Add(lblSearch);
            Controls.Add(txtSearch);
            Controls.Add(lblTitle);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Margin = new Padding(4, 3, 4, 3);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "ContactForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "دليل الاتصال - الموظفين";
            Load += ContactForm_Load;
            ((System.ComponentModel.ISupportInitialize)dataGridViewContacts).EndInit();
            groupBoxEmployees.ResumeLayout(false);
            groupBoxActions.ResumeLayout(false);
            groupBoxActions.PerformLayout();
            statusStrip1.ResumeLayout(false);
            statusStrip1.PerformLayout();
            ResumeLayout(false);
            PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.TextBox txtSearch;
        private System.Windows.Forms.Label lblSearch;
        private System.Windows.Forms.DataGridView dataGridViewContacts;
        private System.Windows.Forms.Button btnWhatsApp;
        private System.Windows.Forms.Button btnCall;
        private System.Windows.Forms.Button btnRefresh;
        private System.Windows.Forms.GroupBox groupBoxEmployees;
        private System.Windows.Forms.GroupBox groupBoxActions;
        private System.Windows.Forms.Label lblSelectedEmployee;
        private System.Windows.Forms.Label lblSelectedPhone;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.Button btnClearSearch;
        private System.Windows.Forms.StatusStrip statusStrip1;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel1;
        private System.Windows.Forms.ToolStripStatusLabel lblEmployeeCount;
    }
}
