using System;
using System.Data.SqlClient;
using System.Drawing;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public static class ThemeManager
    {
        public static Color PrimaryColor { get; private set; } = Color.FromArgb(45, 66, 91);
        public static Color SecondaryColor { get; private set; } = Color.White;
        public static Color TextColor { get; private set; } = Color.Black;
        public static Color ChartBackgroundColor { get; private set; } = Color.White;
        public static Color ChartForegroundColor { get; private set; } = Color.Black;
        public static Color StatisticCardColor { get; private set; } = Color.FromArgb(45, 66, 91);

        private static Color GetMainColor(string theme)
        {
            switch (theme.ToLower())
            {
                case "dark":
                    return Color.FromArgb(45, 66, 99);
                case "light":
                    return Color.FromArgb(69, 103, 138);
                default:
                    return Color.FromArgb(69, 103, 138);
            }
        }

        public static Color GetBackgroundColor(string theme)
        {
            return theme.ToLower() switch
            {
                "light" => Color.FromArgb(240, 240, 240),
                "dark" => Color.FromArgb(30, 30, 30),
                _ => Color.FromArgb(240, 240, 240)
            };
        }

        private static Color GetTextColor(string theme)
        {
            switch (theme.ToLower())
            {
                case "dark":
                    return Color.FromArgb(220, 220, 220);
                case "light":
                    return Color.FromArgb(51, 51, 51);
                default:
                    return Color.FromArgb(51, 51, 51);
            }
        }

        public static Color GetChartBackgroundColor(string theme)
        {
            return theme.ToLower() switch
            {
                "light" => Color.White,
                "dark" => Color.FromArgb(45, 45, 45),
                _ => Color.White
            };
        }

        public static Color GetChartForegroundColor(string theme)
        {
            return theme.ToLower() switch
            {
                "light" => Color.Black,
                "dark" => Color.White,
                _ => Color.Black
            };
        }

        public static Color GetStatisticCardColor(string theme)
        {
            return theme.ToLower() switch
            {
                "light" => Color.FromArgb(69, 103, 138),
                "dark" => Color.FromArgb(45, 66, 91),
                _ => Color.FromArgb(45, 66, 91)
            };
        }

        public static void ApplyTheme(string themeName)
        {
            switch (themeName.ToLower())
            {
                case "dark":
                    PrimaryColor = Color.FromArgb(45, 66, 91);
                    SecondaryColor = Color.FromArgb(35, 50, 70);
                    TextColor = Color.White;
                    ChartBackgroundColor = Color.FromArgb(45, 45, 45);
                    ChartForegroundColor = Color.White;
                    StatisticCardColor = Color.FromArgb(45, 66, 91);
                    break;
                case "light":
                    PrimaryColor = Color.FromArgb(45, 66, 91);
                    SecondaryColor = Color.White;
                    TextColor = Color.Black;
                    ChartBackgroundColor = Color.White;
                    ChartForegroundColor = Color.Black;
                    StatisticCardColor = Color.FromArgb(69, 103, 138);
                    break;
                default:
                    PrimaryColor = Color.FromArgb(45, 66, 91);
                    SecondaryColor = Color.White;
                    TextColor = Color.Black;
                    ChartBackgroundColor = Color.White;
                    ChartForegroundColor = Color.Black;
                    StatisticCardColor = Color.FromArgb(69, 103, 138);
                    break;
            }
        }

        public static void ApplyThemeToForm(Form form)
        {
            if (form == null) return;

            form.BackColor = SecondaryColor;
            form.ForeColor = TextColor;

            foreach (Control control in form.Controls)
            {
                if (!(control is ToolStrip))
                {
                    ApplyThemeToControl(control);
                }
            }
        }

        private static void ApplyThemeToControl(Control control)
        {
            if (control is Button btn && !(control.Parent is ToolStrip))
            {
                btn.BackColor = PrimaryColor;
                btn.ForeColor = Color.White;
                btn.FlatStyle = FlatStyle.Flat;
                btn.FlatAppearance.BorderSize = 0;
            }
            else if (control is Panel panel)
            {
                // Special handling for dashboard statistic cards
                if (panel.Tag?.ToString() == "StatisticCard")
                {
                    panel.BackColor = StatisticCardColor;
                    panel.ForeColor = Color.White;
                }
                else
                {
                    panel.BackColor = SecondaryColor;
                }
                foreach (Control childControl in panel.Controls)
                {
                    ApplyThemeToControl(childControl);
                }
            }
            else if (control is GroupBox groupBox)
            {
                groupBox.ForeColor = TextColor;
                foreach (Control childControl in groupBox.Controls)
                {
                    ApplyThemeToControl(childControl);
                }
            }
            else if (control is TextBox || control is ComboBox)
            {
                control.BackColor = Color.White;
                control.ForeColor = Color.Black;
            }
            else if (control is Label)
            {
                // Special handling for dashboard statistic labels
                if (control.Tag?.ToString() == "StatisticLabel")
                {
                    control.ForeColor = Color.White;
                }
                else
                {
                    control.ForeColor = TextColor;
                }
            }
            else if (control is DataGridView dgv)
            {
                dgv.BackgroundColor = SecondaryColor;
                dgv.ForeColor = Color.Black;
                dgv.GridColor = PrimaryColor;
                dgv.EnableHeadersVisualStyles = false;
                if (dgv.ColumnHeadersDefaultCellStyle != null)
                {
                    dgv.ColumnHeadersDefaultCellStyle.BackColor = PrimaryColor;
                    dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
                }
            }
            // Handle ScottPlot if it exists in the form
            else if (control.GetType().Name == "FormsPlot")
            {
                control.BackColor = ChartBackgroundColor;
                // The actual chart colors will be set in the DashboardForm when updating the plots
            }
            else if (!(control is ToolStrip))
            {
                control.BackColor = SecondaryColor;
                control.ForeColor = TextColor;
            }

            if (control.HasChildren)
            {
                foreach (Control child in control.Controls)
                {
                    if (!(child is ToolStrip))
                    {
                        ApplyThemeToControl(child);
                    }
                }
            }
        }

        public static bool ContinueToIterate()
        {
            Form confirmForm = new Form
            {
                Text = "تأكيد",
                Size = new Size(300, 150),
                StartPosition = FormStartPosition.CenterScreen,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            Label messageLabel = new Label
            {
                Text = "هل تريد الاستمرار؟",
                AutoSize = true,
                Location = new Point(60, 20),
                Font = new Font("Cairo", 10, FontStyle.Regular)
            };

            Button yesButton = new Button
            {
                Text = "نعم",
                DialogResult = DialogResult.Yes,
                Size = new Size(80, 35),
                Location = new Point(180, 60),
                Font = new Font("Cairo", 10, FontStyle.Regular)
            };

            Button noButton = new Button
            {
                Text = "لا",
                DialogResult = DialogResult.No,
                Size = new Size(80, 35),
                Location = new Point(80, 60),
                Font = new Font("Cairo", 10, FontStyle.Regular)
            };

            confirmForm.Controls.AddRange(new Control[] { messageLabel, yesButton, noButton });
            ApplyThemeToForm(confirmForm);
            
            return confirmForm.ShowDialog() == DialogResult.Yes;
        }

        public static void ApplyThemeToDashboard(Form form)
        {
            try
            {
                string theme = "default";
                try
                {
                    using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                    {
                        connection.Open();
                        string sql = "SELECT TOP 1 Theme FROM Settings";
                        using (var command = new SqlCommand(sql, connection))
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                theme = reader["Theme"]?.ToString() ?? "default";
                            }
                        }
                    }
                }
                catch
                {
                    theme = "default";
                }

                Color mainColor = GetMainColor(theme);
                Color bgColor = GetBackgroundColor(theme);
                Color textColor = GetTextColor(theme);

                form.BackColor = bgColor;

                foreach (Control control in form.Controls)
                {
                    if (control is Panel panel)
                    {
                        panel.BackColor = Color.White;
                        if (panel.Tag?.ToString() == "header")
                        {
                            panel.BackColor = mainColor;
                            foreach (Control c in panel.Controls)
                            {
                                if (c is Label)
                                {
                                    c.ForeColor = Color.White;
                                }
                            }
                        }
                    }
                    else if (control is Label label)
                    {
                        if (label.Tag?.ToString() == "title")
                        {
                            label.ForeColor = mainColor;
                        }
                        else
                        {
                            label.ForeColor = textColor;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                DatabaseHelper.LogMessage($"خطأ في تطبيق النمط على لوحة المعلومات: {ex.Message}");
            }
        }

    }
}