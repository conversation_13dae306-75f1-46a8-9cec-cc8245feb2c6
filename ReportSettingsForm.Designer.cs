namespace EmployeeManagementSystem
{
    partial class ReportSettingsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            lblTitle = new Label();
            lblArabicHeader = new Label();
            lblArabicHint = new Label();
            txtArabicHeader = new TextBox();
            lblEnglishHeader = new Label();
            lblEnglishHint = new Label();
            txtEnglishHeader = new TextBox();
            lblReportTitle = new Label();
            txtReportTitle = new TextBox();
            lblLogo = new Label();
            picLogo = new PictureBox();
            btnBrowseLogo = new Button();
            lblLogoPath = new Label();
            lblManagerName = new Label();
            txtManagerName = new TextBox();
            lblSecurityOfficerName = new Label();
            txtSecurityOfficerName = new TextBox();
            lblCommanderName = new Label();
            txtCommanderName = new TextBox();
            btnSave = new Button();
            btnCancel = new Button();
            btnResetToDefault = new Button();
            txtDirectorate = new TextBox();
            label1 = new Label();
            ((System.ComponentModel.ISupportInitialize)picLogo).BeginInit();
            SuspendLayout();
            // 
            // lblTitle
            // 
            lblTitle.Font = new Font("Cairo", 14F, FontStyle.Bold);
            lblTitle.ForeColor = Color.DarkBlue;
            lblTitle.Location = new Point(175, 12);
            lblTitle.Margin = new Padding(4, 0, 4, 0);
            lblTitle.Name = "lblTitle";
            lblTitle.Size = new Size(467, 35);
            lblTitle.TabIndex = 0;
            lblTitle.Text = "تخصيص إعدادات التقرير";
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblArabicHeader
            // 
            lblArabicHeader.Font = new Font("Cairo", 10F, FontStyle.Bold);
            lblArabicHeader.ForeColor = Color.DarkBlue;
            lblArabicHeader.Location = new Point(14, 92);
            lblArabicHeader.Margin = new Padding(4, 0, 4, 0);
            lblArabicHeader.Name = "lblArabicHeader";
            lblArabicHeader.Size = new Size(144, 27);
            lblArabicHeader.TabIndex = 1;
            lblArabicHeader.Text = "النص العربي (أعلى اليمين):";
            // 
            // lblArabicHint
            // 
            lblArabicHint.Font = new Font("Cairo", 8F, FontStyle.Italic);
            lblArabicHint.ForeColor = Color.Gray;
            lblArabicHint.Location = new Point(184, 63);
            lblArabicHint.Margin = new Padding(4, 0, 4, 0);
            lblArabicHint.Name = "lblArabicHint";
            lblArabicHint.Size = new Size(566, 25);
            lblArabicHint.TabIndex = 2;
            lblArabicHint.Text = "يمكنك كتابة عدة أسطر منفصلة";
            // 
            // txtArabicHeader
            // 
            txtArabicHeader.BorderStyle = BorderStyle.FixedSingle;
            txtArabicHeader.Font = new Font("Cairo", 9F);
            txtArabicHeader.Location = new Point(184, 91);
            txtArabicHeader.Margin = new Padding(4, 3, 4, 3);
            txtArabicHeader.Multiline = true;
            txtArabicHeader.Name = "txtArabicHeader";
            txtArabicHeader.ScrollBars = ScrollBars.Vertical;
            txtArabicHeader.Size = new Size(583, 69);
            txtArabicHeader.TabIndex = 3;
            txtArabicHeader.Text = "جـمهوية العـراق\r\nرئـاسـة الـوزراء\r\nهـيأة الحـشــد الشــعبـي\r\nالـدائرة الادارية و الـمـاليـة الـعـامـة";
            // 
            // lblEnglishHeader
            // 
            lblEnglishHeader.Font = new Font("Cairo", 10F, FontStyle.Bold);
            lblEnglishHeader.ForeColor = Color.DarkBlue;
            lblEnglishHeader.Location = new Point(14, 202);
            lblEnglishHeader.Margin = new Padding(4, 0, 4, 0);
            lblEnglishHeader.Name = "lblEnglishHeader";
            lblEnglishHeader.Size = new Size(144, 27);
            lblEnglishHeader.TabIndex = 4;
            lblEnglishHeader.Text = "النص الإنجليزي (أعلى اليسار):";
            // 
            // lblEnglishHint
            // 
            lblEnglishHint.Font = new Font("Cairo", 8F, FontStyle.Italic);
            lblEnglishHint.ForeColor = Color.Gray;
            lblEnglishHint.Location = new Point(184, 174);
            lblEnglishHint.Margin = new Padding(4, 0, 4, 0);
            lblEnglishHint.Name = "lblEnglishHint";
            lblEnglishHint.Size = new Size(534, 25);
            lblEnglishHint.TabIndex = 5;
            lblEnglishHint.Text = "كل سطر منفصل سيظهر في سطر منفصل";
            // 
            // txtEnglishHeader
            // 
            txtEnglishHeader.BorderStyle = BorderStyle.FixedSingle;
            txtEnglishHeader.Font = new Font("Arial", 9F);
            txtEnglishHeader.Location = new Point(184, 202);
            txtEnglishHeader.Margin = new Padding(4, 3, 4, 3);
            txtEnglishHeader.Multiline = true;
            txtEnglishHeader.Name = "txtEnglishHeader";
            txtEnglishHeader.ScrollBars = ScrollBars.Vertical;
            txtEnglishHeader.Size = new Size(583, 69);
            txtEnglishHeader.TabIndex = 6;
            txtEnglishHeader.Text = "Republic Of Iraq\r\nPrime Minister\r\nPopular Mobilization Commission\r\nAdministrative & Public Finance Department";
            // 
            // lblReportTitle
            // 
            lblReportTitle.Font = new Font("Cairo", 10F, FontStyle.Bold);
            lblReportTitle.ForeColor = Color.DarkBlue;
            lblReportTitle.Location = new Point(76, 336);
            lblReportTitle.Margin = new Padding(4, 0, 4, 0);
            lblReportTitle.Name = "lblReportTitle";
            lblReportTitle.Size = new Size(102, 27);
            lblReportTitle.TabIndex = 7;
            lblReportTitle.Text = "عنوان التقرير:";
            // 
            // txtReportTitle
            // 
            txtReportTitle.Font = new Font("Cairo", 9F);
            txtReportTitle.Location = new Point(186, 334);
            txtReportTitle.Margin = new Padding(4, 3, 4, 3);
            txtReportTitle.Name = "txtReportTitle";
            txtReportTitle.Size = new Size(440, 30);
            txtReportTitle.TabIndex = 8;
            txtReportTitle.Text = "جدول الحضور والغياب";
            // 
            // lblLogo
            // 
            lblLogo.Font = new Font("Cairo", 10F, FontStyle.Bold);
            lblLogo.Location = new Point(681, 338);
            lblLogo.Margin = new Padding(4, 0, 4, 0);
            lblLogo.Name = "lblLogo";
            lblLogo.Size = new Size(117, 27);
            lblLogo.TabIndex = 9;
            lblLogo.Text = "شعار المؤسسة:";
            // 
            // picLogo
            // 
            picLogo.BackColor = Color.LightGray;
            picLogo.BorderStyle = BorderStyle.FixedSingle;
            picLogo.Location = new Point(649, 370);
            picLogo.Margin = new Padding(4, 3, 4, 3);
            picLogo.Name = "picLogo";
            picLogo.Size = new Size(175, 198);
            picLogo.SizeMode = PictureBoxSizeMode.Zoom;
            picLogo.TabIndex = 10;
            picLogo.TabStop = false;
            // 
            // btnBrowseLogo
            // 
            btnBrowseLogo.Font = new Font("Cairo", 9F);
            btnBrowseLogo.Location = new Point(649, 605);
            btnBrowseLogo.Margin = new Padding(4, 3, 4, 3);
            btnBrowseLogo.Name = "btnBrowseLogo";
            btnBrowseLogo.Size = new Size(175, 40);
            btnBrowseLogo.TabIndex = 11;
            btnBrowseLogo.Text = "اضافة الشعار";
            btnBrowseLogo.UseVisualStyleBackColor = true;
            btnBrowseLogo.Click += BtnBrowseLogo_Click;
            // 
            // lblLogoPath
            // 
            lblLogoPath.Font = new Font("Cairo", 8F);
            lblLogoPath.ForeColor = Color.Gray;
            lblLogoPath.Location = new Point(649, 574);
            lblLogoPath.Margin = new Padding(4, 0, 4, 0);
            lblLogoPath.Name = "lblLogoPath";
            lblLogoPath.Size = new Size(175, 28);
            lblLogoPath.TabIndex = 12;
            lblLogoPath.Text = "لم يتم اختيار شعار";
            // 
            // lblManagerName
            // 
            lblManagerName.Font = new Font("Cairo", 10F, FontStyle.Bold);
            lblManagerName.Location = new Point(61, 541);
            lblManagerName.Margin = new Padding(4, 0, 4, 0);
            lblManagerName.Name = "lblManagerName";
            lblManagerName.Size = new Size(117, 27);
            lblManagerName.TabIndex = 13;
            lblManagerName.Text = "اسم مدير الإدارة:";
            // 
            // txtManagerName
            // 
            txtManagerName.Font = new Font("Cairo", 9F);
            txtManagerName.Location = new Point(186, 527);
            txtManagerName.Margin = new Padding(4, 3, 4, 3);
            txtManagerName.Multiline = true;
            txtManagerName.Name = "txtManagerName";
            txtManagerName.ScrollBars = ScrollBars.Vertical;
            txtManagerName.Size = new Size(440, 61);
            txtManagerName.TabIndex = 14;
            txtManagerName.Text = "مدير الادارة";
            // 
            // lblSecurityOfficerName
            // 
            lblSecurityOfficerName.Font = new Font("Cairo", 10F, FontStyle.Bold);
            lblSecurityOfficerName.Location = new Point(60, 468);
            lblSecurityOfficerName.Margin = new Padding(4, 0, 4, 0);
            lblSecurityOfficerName.Name = "lblSecurityOfficerName";
            lblSecurityOfficerName.Size = new Size(118, 27);
            lblSecurityOfficerName.TabIndex = 15;
            lblSecurityOfficerName.Text = "اسم ضابط الأمن:";
            // 
            // txtSecurityOfficerName
            // 
            txtSecurityOfficerName.Font = new Font("Cairo", 9F);
            txtSecurityOfficerName.Location = new Point(186, 449);
            txtSecurityOfficerName.Margin = new Padding(4, 3, 4, 3);
            txtSecurityOfficerName.Multiline = true;
            txtSecurityOfficerName.Name = "txtSecurityOfficerName";
            txtSecurityOfficerName.ScrollBars = ScrollBars.Vertical;
            txtSecurityOfficerName.Size = new Size(440, 72);
            txtSecurityOfficerName.TabIndex = 16;
            txtSecurityOfficerName.Text = "ضابط الامن";
            // 
            // lblCommanderName
            // 
            lblCommanderName.Font = new Font("Cairo", 10F, FontStyle.Bold);
            lblCommanderName.Location = new Point(57, 386);
            lblCommanderName.Margin = new Padding(4, 0, 4, 0);
            lblCommanderName.Name = "lblCommanderName";
            lblCommanderName.Size = new Size(121, 27);
            lblCommanderName.TabIndex = 17;
            lblCommanderName.Text = "اسم آمر التشكيل:";
            // 
            // txtCommanderName
            // 
            txtCommanderName.Font = new Font("Cairo", 9F);
            txtCommanderName.Location = new Point(186, 370);
            txtCommanderName.Margin = new Padding(4, 3, 4, 3);
            txtCommanderName.Multiline = true;
            txtCommanderName.Name = "txtCommanderName";
            txtCommanderName.ScrollBars = ScrollBars.Vertical;
            txtCommanderName.Size = new Size(440, 73);
            txtCommanderName.TabIndex = 18;
            txtCommanderName.Text = "امر التشكيل";
            // 
            // btnSave
            // 
            btnSave.BackColor = Color.FromArgb(0, 122, 204);
            btnSave.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnSave.ForeColor = Color.White;
            btnSave.Location = new Point(186, 605);
            btnSave.Margin = new Padding(4, 3, 4, 3);
            btnSave.Name = "btnSave";
            btnSave.Size = new Size(109, 40);
            btnSave.TabIndex = 19;
            btnSave.Text = "حفظ";
            btnSave.UseVisualStyleBackColor = false;
            btnSave.Click += BtnSave_Click;
            // 
            // btnCancel
            // 
            btnCancel.Font = new Font("Cairo", 10F);
            btnCancel.Location = new Point(528, 605);
            btnCancel.Margin = new Padding(4, 3, 4, 3);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(98, 40);
            btnCancel.TabIndex = 20;
            btnCancel.Text = "إلغاء";
            btnCancel.UseVisualStyleBackColor = true;
            btnCancel.Click += BtnCancel_Click;
            // 
            // btnResetToDefault
            // 
            btnResetToDefault.BackColor = Color.Orange;
            btnResetToDefault.Font = new Font("Cairo", 9F);
            btnResetToDefault.ForeColor = Color.White;
            btnResetToDefault.Location = new Point(303, 605);
            btnResetToDefault.Margin = new Padding(4, 3, 4, 3);
            btnResetToDefault.Name = "btnResetToDefault";
            btnResetToDefault.Size = new Size(217, 40);
            btnResetToDefault.TabIndex = 21;
            btnResetToDefault.Text = "استعادة الافتراضي";
            btnResetToDefault.UseVisualStyleBackColor = false;
            btnResetToDefault.Click += BtnResetToDefault_Click;
            // 
            // txtDirectorate
            // 
            txtDirectorate.Font = new Font("Cairo", 9F);
            txtDirectorate.Location = new Point(186, 298);
            txtDirectorate.Margin = new Padding(4, 3, 4, 3);
            txtDirectorate.Name = "txtDirectorate";
            txtDirectorate.Size = new Size(440, 30);
            txtDirectorate.TabIndex = 8;
            txtDirectorate.Text = "اسم المديرية او التشكيل";
            // 
            // label1
            // 
            label1.Font = new Font("Cairo", 10F, FontStyle.Bold);
            label1.ForeColor = Color.DarkBlue;
            label1.Location = new Point(7, 298);
            label1.Margin = new Padding(4, 0, 4, 0);
            label1.Name = "label1";
            label1.Size = new Size(171, 27);
            label1.TabIndex = 7;
            label1.Text = "اسم المديرية او التشكيل: ";
            // 
            // ReportSettingsForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(845, 662);
            Controls.Add(btnResetToDefault);
            Controls.Add(btnCancel);
            Controls.Add(btnSave);
            Controls.Add(txtCommanderName);
            Controls.Add(lblCommanderName);
            Controls.Add(txtSecurityOfficerName);
            Controls.Add(lblSecurityOfficerName);
            Controls.Add(txtManagerName);
            Controls.Add(lblManagerName);
            Controls.Add(lblLogoPath);
            Controls.Add(btnBrowseLogo);
            Controls.Add(picLogo);
            Controls.Add(lblLogo);
            Controls.Add(txtDirectorate);
            Controls.Add(txtReportTitle);
            Controls.Add(label1);
            Controls.Add(lblReportTitle);
            Controls.Add(txtEnglishHeader);
            Controls.Add(lblEnglishHint);
            Controls.Add(lblEnglishHeader);
            Controls.Add(txtArabicHeader);
            Controls.Add(lblArabicHint);
            Controls.Add(lblArabicHeader);
            Controls.Add(lblTitle);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Margin = new Padding(4, 3, 4, 3);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "ReportSettingsForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterParent;
            Text = "إعدادات التقرير";
            ((System.ComponentModel.ISupportInitialize)picLogo).EndInit();
            ResumeLayout(false);
            PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.Label lblArabicHeader;
        private System.Windows.Forms.Label lblArabicHint;
        private System.Windows.Forms.TextBox txtArabicHeader;
        private System.Windows.Forms.Label lblEnglishHeader;
        private System.Windows.Forms.Label lblEnglishHint;
        private System.Windows.Forms.TextBox txtEnglishHeader;
        private System.Windows.Forms.Label lblReportTitle;
        private System.Windows.Forms.TextBox txtReportTitle;
        private System.Windows.Forms.Label lblLogo;
        private System.Windows.Forms.PictureBox picLogo;
        private System.Windows.Forms.Button btnBrowseLogo;
        private System.Windows.Forms.Label lblLogoPath;
        private System.Windows.Forms.Label lblManagerName;
        private System.Windows.Forms.TextBox txtManagerName;
        private System.Windows.Forms.Label lblSecurityOfficerName;
        private System.Windows.Forms.TextBox txtSecurityOfficerName;
        private System.Windows.Forms.Label lblCommanderName;
        private System.Windows.Forms.TextBox txtCommanderName;
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnResetToDefault;
        private TextBox txtDirectorate;
        private Label label1;
    }
}