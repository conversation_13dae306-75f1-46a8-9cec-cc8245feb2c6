using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class DailyDetailsForm : Form
    {
        private int workPeriodId;
        private int employeeCode;
        private string employeeName;
        private DateTime startDate;
        private DateTime endDate;

        public DailyDetailsForm(int workPeriodId, int employeeCode, string employeeName, DateTime startDate, DateTime endDate)
        {
            this.workPeriodId = workPeriodId;
            this.employeeCode = employeeCode;
            this.employeeName = employeeName;
            this.startDate = startDate;
            this.endDate = endDate;

            InitializeComponent();
            SetupForm();
            LoadDailyDetails();
            ThemeManager.ApplyThemeToForm(this);
        }

        private void SetupForm()
        {
            this.Text = "التفاصيل اليومية للموظف";
            lblTitle.Text = $"التفاصيل اليومية - {employeeName} (كود: {employeeCode})";
            lblPeriod.Text = $"الفترة: {startDate:dd/MM/yyyy} - {endDate:dd/MM/yyyy}";
        }

        private void LoadDailyDetails()
        {
            try
            {
                var detailsTable = DatabaseHelper.GetEmployeeDailyWorkDetails(workPeriodId, employeeCode);

                if (detailsTable == null || detailsTable.Rows.Count == 0)
                {
                    MessageBox.Show($"لا توجد تفاصيل يومية لهذا الموظف\n\n" +
                                   $"الموظف: {employeeName} (كود: {employeeCode})\n" +
                                   $"فترة العمل: {workPeriodId}\n" +
                                   $"الفترة: {startDate:dd/MM/yyyy} - {endDate:dd/MM/yyyy}\n\n" +
                                   $"يمكنك إنشاء السجلات اليومية من زر إنشاء السجلات أدناه.", 
                                   "لا توجد بيانات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // ربط البيانات مباشرة بدون تنسيق معقد
                dataGridViewDetails.DataSource = detailsTable;
                dataGridViewDetails.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

                // تلوين بسيط للصفوف
                foreach (DataGridViewRow row in dataGridViewDetails.Rows)
                {
                    if (row.IsNewRow) continue;
                    
                    try
                    {
                        string status = row.Cells["Status"]?.Value?.ToString() ?? "";
                        switch (status)
                        {
                            case "حضور":
                                row.DefaultCellStyle.BackColor = Color.LightGreen;
                                break;
                            case "غياب":
                                row.DefaultCellStyle.BackColor = Color.LightCoral;
                                break;
                            case "إجازة":
                                row.DefaultCellStyle.BackColor = Color.LightBlue;
                                break;
                        }
                    }
                    catch { /* تجاهل الأخطاء */ }
                }

                // حساب إحصائيات بسيطة
                int total = detailsTable.Rows.Count;
                int present = 0, absent = 0, vacation = 0;

                foreach (DataRow row in detailsTable.Rows)
                {
                    string status = row["Status"]?.ToString() ?? "";
                    switch (status)
                    {
                        case "حضور": present++; break;
                        case "غياب": absent++; break;
                        case "إجازة": vacation++; break;
                    }
                }

                double rate = total > 0 ? (double)present / total * 100 : 0;
                lblStats.Text = $"المجموع: {total} | الحضور: {present} | الغياب: {absent} | الإجازة: {vacation} | معدل الحضور: {rate:F1}%";

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        //private void BtnCreateRecords_Click(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        DialogResult result = MessageBox.Show(
        //            $"إنشاء السجلات اليومية للموظف {employeeName}؟",
        //            "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

        //        if (result == DialogResult.Yes)
        //        {
        //            DatabaseHelper.CreateDailyWorkStatusRecords(workPeriodId, employeeCode, employeeName, 
        //                startDate, endDate, "غياب");
        //            MessageBox.Show("تم إنشاء السجلات بنجاح!", "نجاح", 
        //                MessageBoxButtons.OK, MessageBoxIcon.Information);
        //            LoadDailyDetails();
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        //    }
        //}
        private void BtnCreateRecords_Click(object sender, EventArgs e)
        {
            try
            {
                string message = $"سيتم تعيين حالة الموظف ({employeeName}) إلى 'غياب' في جميع الأيام من {startDate:yyyy/MM/dd} إلى {endDate:yyyy/MM/dd}.\n\n" +
                                 "هل أنت متأكد أنك تريد المتابعة؟\n\n" +
                                 "إذا كنت تريد تعيين حالات مختلفة حسب الأيام، يرجى استخدام نموذج التتبع اليومي.";

                DialogResult result = MessageBox.Show(
                    message, "تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    DatabaseHelper.CreateDailyWorkStatusRecords(
                        workPeriodId, employeeCode, employeeName, startDate, endDate, "غياب");

                    MessageBox.Show("تم إنشاء السجلات بنجاح!", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadDailyDetails();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewDetails.Rows.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات للطباعة!", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // إنشاء تقرير HTML بسيط
                var html = GenerateSimpleReport();
                
                // حفظ التقرير في ملف مؤقت
                string tempFile = System.IO.Path.GetTempFileName() + ".html";
                System.IO.File.WriteAllText(tempFile, html, System.Text.Encoding.UTF8);
                
                // فتح التقرير في المتصفح للطباعة
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempFile,
                    UseShellExecute = true
                });

                MessageBox.Show("تم فتح التقرير في المتصفح للطباعة", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GenerateSimpleReport()
        {
            var html = new System.Text.StringBuilder();
            html.Append("<!DOCTYPE html>");
            html.Append("<html dir='rtl' lang='ar'>");
            html.Append("<head><meta charset='UTF-8'>");
            html.Append("<title>تقرير التفاصيل اليومية</title>");
            html.Append("<style>");
            html.Append("body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }");
            html.Append(".header { text-align: center; margin-bottom: 20px; }");
            html.Append(".title { font-size: 18px; font-weight: bold; color: #333; }");
            html.Append(".info { font-size: 14px; margin: 10px 0; }");
            html.Append("table { width: 100%; border-collapse: collapse; margin: 20px 0; }");
            html.Append("th, td { padding: 8px; text-align: center; border: 1px solid #ddd; }");
            html.Append("th { background-color: #f8f9fa; font-weight: bold; }");
            html.Append(".present { background-color: #d4edda; color: #155724; }");
            html.Append(".absent { background-color: #f8d7da; color: #721c24; }");
            html.Append(".vacation { background-color: #d1ecf1; color: #0c5460; }");
            html.Append(".stats { margin: 20px 0; padding: 10px; background: #f8f9fa; border: 1px solid #ddd; }");
            html.Append("</style></head><body>");
            
            html.Append("<div class='header'>");
            html.Append("<div class='title'>تقرير التفاصيل اليومية للموظف</div>");
            html.Append($"<div class='info'>الموظف: {employeeName} (كود: {employeeCode})</div>");
            html.Append($"<div class='info'>الفترة: {startDate:dd/MM/yyyy} - {endDate:dd/MM/yyyy}</div>");
            html.Append("</div>");
            
            // الإحصائيات
            html.Append($"<div class='stats'>{lblStats.Text}</div>");
            
            // الجدول
            html.Append("<table><thead><tr>");
            foreach (DataGridViewColumn col in dataGridViewDetails.Columns)
            {
                html.Append($"<th>{col.HeaderText}</th>");
            }
            html.Append("</tr></thead><tbody>");
            
            foreach (DataGridViewRow row in dataGridViewDetails.Rows)
            {
                if (row.IsNewRow) continue;
                html.Append("<tr>");
                
                for (int i = 0; i < dataGridViewDetails.Columns.Count; i++)
                {
                    string value = row.Cells[i].Value?.ToString() ?? "";
                    string cssClass = "";
                    
                    if (dataGridViewDetails.Columns[i].Name == "Status" || value == "حضور" || value == "غياب" || value == "إجازة")
                    {
                        switch (value)
                        {
                            case "حضور": cssClass = "present"; break;
                            case "غياب": cssClass = "absent"; break;
                            case "إجازة": cssClass = "vacation"; break;
                        }
                    }
                    
                    html.Append($"<td class='{cssClass}'>{value}</td>");
                }
                html.Append("</tr>");
            }
            
            html.Append("</tbody></table>");
            html.Append($"<div style='text-align: center; margin-top: 30px; color: #666;'>تم إنشاء التقرير في {DateTime.Now:dd/MM/yyyy HH:mm}</div>");
            html.Append("</body></html>");
            
            return html.ToString();
        }
    }
}