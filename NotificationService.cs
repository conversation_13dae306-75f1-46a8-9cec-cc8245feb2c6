using System;
using System.Data;
using System.Data.SqlClient;

namespace EmployeeManagementSystem
{
    public class NotificationService
    {
        public static void CreateVacationEndNotification(int employeeId, string employeeName, DateTime endDate)
        {
            var message = $"تنبيه: تنتهي غداً إجازة الموظف {employeeName} (بتاريخ {endDate.ToString("dd/MM/yyyy")})";
            // لا يتم إنشاء الإشعار هنا - سيتم إنشاؤه عند فحص الإشعارات
        }

        public static void CreateTrainingEndNotification(int employeeId, string employeeName, DateTime endDate)
        {
            var message = $"تنبيه: تنتهي غداً الدورة التدريبية للموظف {employeeName} (بتاريخ {endDate.ToString("dd/MM/yyyy")})";
            // لا يتم إنشاء الإشعار هنا - سيتم إنشاؤه عند فحص الإشعارات
        }

        private static void CreateNotification(int employeeId, string message, string type, DateTime targetDate)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string query = @"INSERT INTO Notifications 
                                   (EmployeeID, Message, Type, IsRead, DateCreated, TargetDate) 
                                   VALUES 
                                   (@EmployeeID, @Message, @Type, 0, @DateCreated, @TargetDate)";

                    using (var command = new SqlCommand(query, connection))
                    {
                        var dateCreated = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                        var targetDateStr = targetDate.ToString("yyyy-MM-dd");

                        command.Parameters.AddWithValue("@EmployeeID", employeeId);
                        command.Parameters.AddWithValue("@Message", message);
                        command.Parameters.AddWithValue("@Type", type);
                        command.Parameters.AddWithValue("@DateCreated", dateCreated);
                        command.Parameters.AddWithValue("@TargetDate", targetDateStr);

                        // طباعة تشخيصية قبل إنشاء الإشعار
                        System.Diagnostics.Debug.WriteLine($"محاولة إنشاء إشعار جديد:");
                        System.Diagnostics.Debug.WriteLine($"employeeId: {employeeId}");
                        System.Diagnostics.Debug.WriteLine($"message: {message}");
                        System.Diagnostics.Debug.WriteLine($"type: {type}");
                        System.Diagnostics.Debug.WriteLine($"dateCreated: {dateCreated}");
                        System.Diagnostics.Debug.WriteLine($"targetDate: {targetDateStr}");

                        command.ExecuteNonQuery();
                        System.Diagnostics.Debug.WriteLine("تم إنشاء الإشعار بنجاح");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء الإشعار: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
            }
        }

        public static void CheckForUpcomingEvents()
        {
            try
            {
                DatabaseHelper.LogMessage("بدء فحص الأحداث القادمة");

                // التحقق من الإجازات
                var vacations = DatabaseHelper.GetVacationsEndingTomorrow();
                foreach (DataRow row in vacations.Rows)
                {
                    // التحقق من وجود الأعمدة المطلوبة
                    if (!row.Table.Columns.Contains("EmployeeID") || !row.Table.Columns.Contains("EndDate"))
                        continue;

                    if (row["EmployeeID"] == DBNull.Value || row["EndDate"] == DBNull.Value)
                        continue;

                    var employeeId = Convert.ToInt32(row["EmployeeID"]);
                    var employeeName = row.Table.Columns.Contains("EmployeeName")
                        ? row["EmployeeName"]?.ToString() ?? "موظف غير معروف"
                        : "موظف غير معروف";
                    var endDate = Convert.ToDateTime(row["EndDate"]).Date;

                    // التحقق من الإشعارات الموجودة
                    using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                    {
                        connection.Open();
                        string checkQuery = @"
                    SELECT COUNT(*) FROM Notifications 
                    WHERE EmployeeID = @EmployeeID 
                    AND Type = 'vacation' 
                    AND CAST(TargetDate AS DATE) = CAST(@EndDate AS DATE)";

                        using (var command = new SqlCommand(checkQuery, connection))
                        {
                            command.Parameters.AddWithValue("@EmployeeID", employeeId);
                            command.Parameters.AddWithValue("@EndDate", endDate);
                            int existingCount = Convert.ToInt32(command.ExecuteScalar());

                            if (existingCount == 0)
                            {
                                var message = $"تنبيه: تنتهي غداً إجازة الموظف {employeeName} (بتاريخ {endDate:dd/MM/yyyy})";
                                DatabaseHelper.LogMessage($"إنشاء إشعار جديد للإجازة: {message}");
                                CreateNotification(employeeId, message, "vacation", endDate);
                            }
                        }
                    }
                }

                // التحقق من الدورات التدريبية
                var courses = DatabaseHelper.GetCoursesEndingTomorrow();
                foreach (DataRow row in courses.Rows)
                {
                    // التحقق من وجود الأعمدة المطلوبة
                    if (!row.Table.Columns.Contains("EmployeeCode") || !row.Table.Columns.Contains("EndDate"))
                        continue;

                    if (row["EmployeeCode"] == DBNull.Value || row["EndDate"] == DBNull.Value)
                        continue;

                    var employeeId = Convert.ToInt32(row["EmployeeCode"]);
                    var employeeName = row.Table.Columns.Contains("EmployeeName")
                        ? row["EmployeeName"]?.ToString() ?? "موظف غير معروف"
                        : "موظف غير معروف";
                    var endDate = Convert.ToDateTime(row["EndDate"]).Date;

                    // التحقق من الإشعارات الموجودة
                    using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                    {
                        connection.Open();
                        string checkQuery = @"
                    SELECT COUNT(*) FROM Notifications 
                    WHERE EmployeeID = @EmployeeID 
                    AND Type = 'training' 
                    AND CAST(TargetDate AS DATE) = CAST(@EndDate AS DATE)";

                        using (var command = new SqlCommand(checkQuery, connection))
                        {
                            command.Parameters.AddWithValue("@EmployeeID", employeeId);
                            command.Parameters.AddWithValue("@EndDate", endDate);
                            int existingCount = Convert.ToInt32(command.ExecuteScalar());

                            if (existingCount == 0)
                            {
                                var message = $"تنبيه: تنتهي غداً الدورة التدريبية للموظف {employeeName} (بتاريخ {endDate:dd/MM/yyyy})";
                                DatabaseHelper.LogMessage($"إنشاء إشعار جديد للدورة: {message}");
                                CreateNotification(employeeId, message, "training", endDate);
                            }
                        }
                    }
                }

                DatabaseHelper.LogMessage("اكتمل فحص الأحداث القادمة");
            }
            catch (Exception ex)
            {
                string errorDetails = $"خطأ في فحص الأحداث القادمة: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorDetails += $"\nتفاصيل الخطأ الداخلي: {ex.InnerException.Message}";
                }

                DatabaseHelper.LogMessage(errorDetails);
                System.Diagnostics.Debug.WriteLine($"Error checking for upcoming events: {errorDetails}");
                MessageBox.Show($"حدث خطأ أثناء فحص الإشعارات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public static int GetUnreadNotificationsCount()
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Notifications WHERE IsRead = 0";
                    using (var command = new SqlCommand(query, connection))
                    {
                        return Convert.ToInt32(command.ExecuteScalar());
                    }
                }
            }
            catch (Exception ex)
            {
                string errorDetails = $"Error getting unread notifications count: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorDetails += $"\nInner Exception: {ex.InnerException.Message}";
                }
                System.Diagnostics.Debug.WriteLine(errorDetails);
                return 0;
            }
        }
    }
}
