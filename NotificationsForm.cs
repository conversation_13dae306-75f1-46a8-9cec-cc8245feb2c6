using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using System.Data.SqlClient;
using EmployeeManagementSystem.LoadingGui;


namespace EmployeeManagementSystem
{
    public partial class NotificationsForm : Form
    {
        private User? currentUser = null; // إضافة المستخدم الحالي

        public NotificationsForm()
        {
            InitializeComponent();
            CustomizeDataGridView();
            LoadNotifications();
        }

        // Constructor جديد يستقبل المستخدم الحالي
        public NotificationsForm(User currentUser) : this()
        {
            this.currentUser = currentUser;

            // تطبيق فلترة الإشعارات حسب المستخدم
             LoadNotifications();
        }

        private void CustomizeDataGridView()
        {
            // تهيئة DataGridView
            notificationsDataGridView.AutoGenerateColumns = false;
            notificationsDataGridView.RightToLeft = RightToLeft.Yes;

            // إزالة الأعمدة الموجودة
            notificationsDataGridView.Columns.Clear();

            // إنشاء الأعمدة
            notificationsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ID",
                DataPropertyName = "ID",
                HeaderText = "المعرف",
                Visible = false
            });

            notificationsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Message",
                DataPropertyName = "Message",
                HeaderText = "الرسالة",
                Width = 300,
                AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            });

            notificationsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Type",
                DataPropertyName = "Type",
                HeaderText = "النوع",
                Width = 100
            });

            notificationsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DateCreated",
                DataPropertyName = "DateCreated",
                HeaderText = "تاريخ الإنشاء",
                Width = 150,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy HH:mm" }
            });

            notificationsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TargetDate",
                DataPropertyName = "TargetDate",
                HeaderText = "تاريخ الانتهاء",
                Width = 150,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy" }
            });

            notificationsDataGridView.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsRead",
                DataPropertyName = "IsRead",
                HeaderText = "مقروء",
                Width = 70
            });

            notificationsDataGridView.Columns.Add(new DataGridViewButtonColumn
            {
                Name = "MarkAsReadButton",
                HeaderText = "تحديد كمقروء",
                Text = "تحديد كمقروء",
                UseColumnTextForButtonValue = true,
                Width = 100
            });

            notificationsDataGridView.Columns.Add(new DataGridViewButtonColumn
            {
                Name = "DeleteButton",
                HeaderText = "حذف",
                Text = "حذف",
                UseColumnTextForButtonValue = true,
                Width = 70
            });

            // تنسيق الجدول
            notificationsDataGridView.EnableHeadersVisualStyles = false;
            notificationsDataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(45, 66, 91);
            notificationsDataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            notificationsDataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Cairo", 12F, FontStyle.Bold);
            notificationsDataGridView.DefaultCellStyle.Font = new Font("Cairo", 10F);
            notificationsDataGridView.RowTemplate.Height = 35;

            // تمييز الإشعارات غير المقروءة
            notificationsDataGridView.CellFormatting += (sender, e) =>
            {
                if (e.RowIndex >= 0)
                {
                    var isRead = Convert.ToBoolean(notificationsDataGridView.Rows[e.RowIndex].Cells["IsRead"].Value);
                    if (!isRead)
                    {
                        notificationsDataGridView.Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(255, 249, 230);
                        notificationsDataGridView.Rows[e.RowIndex].DefaultCellStyle.Font = new Font("Cairo", 10F, FontStyle.Bold);
                    }
                }
            };
        }

        private void LoadNotifications()
        {
            try
            {
                string query;

                // تطبيق الفلترة حسب نوع المستخدم
                if (currentUser != null && EmployeeDepartmentHelper.IsDepartmentManager(currentUser))
                {
                    // مدير القسم يرى إشعارات موظفي قسمه فقط
                    int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                    if (managedDepartmentId.HasValue)
                    {
                        query = @"SELECT
                                    n.ID,
                                    n.Message,
                                    n.Type,
                                    e.Name as EmployeeName,
                                    n.DateCreated,
                                    n.TargetDate,
                                    n.IsRead
                                   FROM Notifications n
                                   LEFT JOIN Employees e ON n.EmployeeID = e.EmployeeCode
                                   LEFT JOIN Users u ON e.EmployeeCode = u.UserId
                                   WHERE u.DepartmentId = @DepartmentId AND u.IsActive = 1
                                   ORDER BY n.DateCreated DESC";
                    }
                    else
                    {
                        // مدير قسم بدون قسم - لا يرى أي إشعارات
                        query = @"SELECT
                                    n.ID,
                                    n.Message,
                                    n.Type,
                                    e.Name as EmployeeName,
                                    n.DateCreated,
                                    n.TargetDate,
                                    n.IsRead
                                   FROM Notifications n
                                   LEFT JOIN Employees e ON n.EmployeeID = e.EmployeeCode
                                   WHERE 1 = 0";
                    }
                }
                else
                {
                    // المدير العام أو المستخدم العادي يرى جميع الإشعارات
                    query = @"SELECT
                                    n.ID,
                                    n.Message,
                                    n.Type,
                                    e.Name as EmployeeName,
                                    n.DateCreated,
                                    n.TargetDate,
                                    n.IsRead
                                   FROM Notifications n
                                   LEFT JOIN Employees e ON n.EmployeeID = e.EmployeeCode
                                   ORDER BY n.DateCreated DESC";
                }

                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        // إضافة معامل DepartmentId إذا كان مدير قسم
                        if (currentUser != null && EmployeeDepartmentHelper.IsDepartmentManager(currentUser))
                        {
                            int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                            if (managedDepartmentId.HasValue)
                            {
                                command.Parameters.AddWithValue("@DepartmentId", managedDepartmentId.Value);
                            }
                        }

                        using (var adapter = new SqlDataAdapter(command))
                        {
                            var dt = new DataTable();
                            adapter.Fill(dt);
                            notificationsDataGridView.DataSource = dt;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإشعارات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void MarkAsRead(int notificationId)
        {
            try
            {
                string query = "UPDATE Notifications SET IsRead = 1 WHERE ID = @ID";
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@ID", notificationId);
                        command.ExecuteNonQuery();
                    }
                }
                LoadNotifications();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث حالة الإشعار: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DeleteNotification(int notificationId)
        {
            try
            {
                string query = "DELETE FROM Notifications WHERE ID = @ID";
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@ID", notificationId);
                        command.ExecuteNonQuery();
                    }
                }
                LoadNotifications();
                ToastHelper.ShowDeleteNotificationsToast();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الإشعار: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void notificationsDataGridView_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex < 0)
                return;

            var grid = (DataGridView)sender;

            // التحقق من النقر على زر "تحديد كمقروء"
            if (grid.Columns[e.ColumnIndex] is DataGridViewButtonColumn &&
                grid.Columns[e.ColumnIndex].Name == "MarkAsReadButton")
            {
                var notificationId = Convert.ToInt32(grid.Rows[e.RowIndex].Cells["ID"].Value);
                MarkAsRead(notificationId);
            }
            // التحقق من النقر على زر "حذف"
            else if (grid.Columns[e.ColumnIndex] is DataGridViewButtonColumn &&
                     grid.Columns[e.ColumnIndex].Name == "DeleteButton")
            {
                if (MessageBox.Show("هل أنت متأكد من حذف هذا الإشعار؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    var notificationId = Convert.ToInt32(grid.Rows[e.RowIndex].Cells["ID"].Value);
                    DeleteNotification(notificationId);
                }
            }
        }

        private void markAllReadButton_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد تحديد جميع الإشعارات كمقروءة؟", "تأكيد",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                try
                {
                    string query = "UPDATE Notifications SET IsRead = 1";
                    using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                    {
                        connection.Open();
                        using (var command = new SqlCommand(query, connection))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                    LoadNotifications();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحديث الإشعارات: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void NotificationsForm_Load(object sender, EventArgs e)
        {
            UIHelper.ShowEmptyMessage(notificationsDataGridView, lbl_NoNotifications, "لا توجد إشعارات");
        }

        private async void BtnRefreshNotifications_Click(object sender, EventArgs e)
        {
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.BringToFront();

            // إخفاء التسمية مؤقتًا قبل التحميل
            lbl_NoNotifications.Visible = false;

            // أضف تأخير صغير يسمح للواجهة برسم الفورم
            await Task.Delay(150);

            try
            {
                LoadNotifications();
                UIHelper.ShowEmptyMessage(notificationsDataGridView, lbl_NoNotifications, "لا توجد إشعارات");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإشعارات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            loadingForm.Close();
        }


    }
}
