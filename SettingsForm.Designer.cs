namespace EmployeeManagementSystem
{
    partial class SettingsForm
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(SettingsForm));
            pictureBoxLogo = new PictureBox();
            btnSelectLogo = new Button();
            btnBackup = new Button();
            btnRestore = new Button();
            btnSave = new Button();
            btnCancel = new Button();
            btnClear = new Button();
            btn_update = new Button();
            groupBox2 = new GroupBox();
            cmbTheme = new ComboBox();
            lblTheme = new Label();
            txtDescription = new TextBox();
            lblDescription = new Label();
            txtOrganizationName = new TextBox();
            lblOrganizationName = new Label();
            toolTip1 = new ToolTip(components);
            groupBox3 = new GroupBox();
            radioButtonNetwork = new RadioButton();
            radioButtonLocal = new RadioButton();
            edt_password = new TextBox();
            edt_username = new TextBox();
            edt_timeout = new TextBox();
            edt_database = new TextBox();
            btn_saveconstring = new Button();
            edt_servername = new TextBox();
            label5 = new Label();
            label4 = new Label();
            label3 = new Label();
            label2 = new Label();
            label1 = new Label();
            ((System.ComponentModel.ISupportInitialize)pictureBoxLogo).BeginInit();
            groupBox2.SuspendLayout();
            groupBox3.SuspendLayout();
            SuspendLayout();
            // 
            // pictureBoxLogo
            // 
            pictureBoxLogo.BorderStyle = BorderStyle.FixedSingle;
            pictureBoxLogo.Image = Properties.Resources.picture;
            pictureBoxLogo.Location = new Point(7, 65);
            pictureBoxLogo.Margin = new Padding(4, 3, 4, 3);
            pictureBoxLogo.Name = "pictureBoxLogo";
            pictureBoxLogo.Size = new Size(200, 190);
            pictureBoxLogo.SizeMode = PictureBoxSizeMode.Zoom;
            pictureBoxLogo.TabIndex = 8;
            pictureBoxLogo.TabStop = false;
            // 
            // btnSelectLogo
            // 
            btnSelectLogo.BackColor = Color.FromArgb(45, 66, 91);
            btnSelectLogo.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnSelectLogo.ForeColor = Color.White;
            btnSelectLogo.Image = Properties.Resources.image_file_add_32px;
            btnSelectLogo.ImageAlign = ContentAlignment.MiddleRight;
            btnSelectLogo.Location = new Point(7, 266);
            btnSelectLogo.Margin = new Padding(4, 3, 4, 3);
            btnSelectLogo.Name = "btnSelectLogo";
            btnSelectLogo.Size = new Size(200, 46);
            btnSelectLogo.TabIndex = 7;
            btnSelectLogo.Text = "اختيار الشعار";
            btnSelectLogo.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnSelectLogo, "اختيار الشعار");
            btnSelectLogo.UseVisualStyleBackColor = false;
            btnSelectLogo.Click += btnSelectLogo_Click;
            // 
            // btnBackup
            // 
            btnBackup.BackColor = Color.FromArgb(45, 66, 91);
            btnBackup.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnBackup.ForeColor = Color.White;
            btnBackup.Image = Properties.Resources.data_backup_32px;
            btnBackup.ImageAlign = ContentAlignment.MiddleRight;
            btnBackup.Location = new Point(284, 356);
            btnBackup.Margin = new Padding(4, 3, 4, 3);
            btnBackup.Name = "btnBackup";
            btnBackup.Size = new Size(270, 46);
            btnBackup.TabIndex = 4;
            btnBackup.Text = "اخذ نسخة احتياطية";
            toolTip1.SetToolTip(btnBackup, "نسخة احتياطية من قاعدة البيانات");
            btnBackup.UseVisualStyleBackColor = false;
            btnBackup.Click += btnBackup_Click;
            // 
            // btnRestore
            // 
            btnRestore.BackColor = Color.FromArgb(45, 66, 91);
            btnRestore.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnRestore.ForeColor = Color.White;
            btnRestore.Image = Properties.Resources.database_restore_32px;
            btnRestore.ImageAlign = ContentAlignment.MiddleRight;
            btnRestore.Location = new Point(7, 356);
            btnRestore.Margin = new Padding(4, 3, 4, 3);
            btnRestore.Name = "btnRestore";
            btnRestore.Size = new Size(270, 46);
            btnRestore.TabIndex = 3;
            btnRestore.Text = "استعادة النسخة الاحتياطية";
            toolTip1.SetToolTip(btnRestore, "استعادة نسخة قاعدة البيانات");
            btnRestore.UseVisualStyleBackColor = false;
            btnRestore.Click += btnRestore_Click;
            // 
            // btnSave
            // 
            btnSave.BackColor = Color.FromArgb(45, 66, 91);
            btnSave.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnSave.ForeColor = Color.White;
            btnSave.Image = Properties.Resources.ok_32px;
            btnSave.ImageAlign = ContentAlignment.MiddleRight;
            btnSave.Location = new Point(502, 357);
            btnSave.Margin = new Padding(4, 3, 4, 3);
            btnSave.Name = "btnSave";
            btnSave.Size = new Size(145, 46);
            btnSave.TabIndex = 2;
            btnSave.Text = "حفظ";
            toolTip1.SetToolTip(btnSave, "حفظ الاعدادات");
            btnSave.UseVisualStyleBackColor = false;
            btnSave.Click += btnSave_Click;
            // 
            // btnCancel
            // 
            btnCancel.BackColor = Color.FromArgb(45, 66, 91);
            btnCancel.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnCancel.ForeColor = Color.White;
            btnCancel.Image = Properties.Resources.delete_32px;
            btnCancel.ImageAlign = ContentAlignment.MiddleRight;
            btnCancel.Location = new Point(195, 357);
            btnCancel.Margin = new Padding(4, 3, 4, 3);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(145, 46);
            btnCancel.TabIndex = 1;
            btnCancel.Text = "إلغاء";
            toolTip1.SetToolTip(btnCancel, "إلغاء");
            btnCancel.UseVisualStyleBackColor = false;
            btnCancel.Click += btnCancel_Click;
            // 
            // btnClear
            // 
            btnClear.BackColor = Color.FromArgb(45, 66, 91);
            btnClear.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnClear.ForeColor = Color.White;
            btnClear.Image = Properties.Resources.clear_formatting_32px;
            btnClear.ImageAlign = ContentAlignment.MiddleRight;
            btnClear.Location = new Point(348, 357);
            btnClear.Margin = new Padding(4, 3, 4, 3);
            btnClear.Name = "btnClear";
            btnClear.Size = new Size(146, 46);
            btnClear.TabIndex = 0;
            btnClear.Text = "إفراغ الحقول";
            btnClear.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnClear, "إفراغ الحقول");
            btnClear.UseVisualStyleBackColor = false;
            btnClear.Click += btnClear_Click;
            // 
            // btn_update
            // 
            btn_update.BackColor = Color.FromArgb(45, 66, 91);
            btn_update.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btn_update.ForeColor = Color.White;
            btn_update.Image = Properties.Resources.available_updates_32px;
            btn_update.ImageAlign = ContentAlignment.MiddleRight;
            btn_update.Location = new Point(7, 304);
            btn_update.Margin = new Padding(4, 3, 4, 3);
            btn_update.Name = "btn_update";
            btn_update.Size = new Size(269, 46);
            btn_update.TabIndex = 1;
            btn_update.Text = "تحديث البرنامج";
            toolTip1.SetToolTip(btn_update, "تحديث البرنامج");
            btn_update.UseVisualStyleBackColor = false;
            btn_update.Click += btn_update_Click;
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(cmbTheme);
            groupBox2.Controls.Add(lblTheme);
            groupBox2.Controls.Add(btnClear);
            groupBox2.Controls.Add(txtDescription);
            groupBox2.Controls.Add(btnSelectLogo);
            groupBox2.Controls.Add(lblDescription);
            groupBox2.Controls.Add(pictureBoxLogo);
            groupBox2.Controls.Add(txtOrganizationName);
            groupBox2.Controls.Add(btnCancel);
            groupBox2.Controls.Add(lblOrganizationName);
            groupBox2.Controls.Add(btnSave);
            groupBox2.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBox2.Location = new Point(12, 31);
            groupBox2.Name = "groupBox2";
            groupBox2.RightToLeft = RightToLeft.Yes;
            groupBox2.Size = new Size(663, 416);
            groupBox2.TabIndex = 14;
            groupBox2.TabStop = false;
            groupBox2.Text = "معلومات المؤسسة";
            // 
            // cmbTheme
            // 
            cmbTheme.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbTheme.Font = new Font("Cairo", 12F, FontStyle.Bold);
            cmbTheme.FormattingEnabled = true;
            cmbTheme.Items.AddRange(new object[] { "افتراضي", "فاتح", "مظلم" });
            cmbTheme.Location = new Point(226, 271);
            cmbTheme.Margin = new Padding(4, 3, 4, 3);
            cmbTheme.Name = "cmbTheme";
            cmbTheme.RightToLeft = RightToLeft.No;
            cmbTheme.Size = new Size(344, 38);
            cmbTheme.TabIndex = 13;
            toolTip1.SetToolTip(cmbTheme, "اختيار المظهر");
            // 
            // lblTheme
            // 
            lblTheme.AutoSize = true;
            lblTheme.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblTheme.Location = new Point(578, 274);
            lblTheme.Margin = new Padding(4, 0, 4, 0);
            lblTheme.Name = "lblTheme";
            lblTheme.Size = new Size(68, 30);
            lblTheme.TabIndex = 14;
            lblTheme.Text = "المظهر";
            // 
            // txtDescription
            // 
            txtDescription.Font = new Font("Cairo", 11.249999F, FontStyle.Bold, GraphicsUnit.Point, 0);
            txtDescription.Location = new Point(226, 144);
            txtDescription.Margin = new Padding(4, 3, 4, 3);
            txtDescription.Multiline = true;
            txtDescription.Name = "txtDescription";
            txtDescription.RightToLeft = RightToLeft.No;
            txtDescription.Size = new Size(420, 115);
            txtDescription.TabIndex = 15;
            txtDescription.Text = "وصف المؤسسة";
            txtDescription.TextAlign = HorizontalAlignment.Right;
            // 
            // lblDescription
            // 
            lblDescription.AutoSize = true;
            lblDescription.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lblDescription.Location = new Point(544, 117);
            lblDescription.Margin = new Padding(4, 0, 4, 0);
            lblDescription.Name = "lblDescription";
            lblDescription.Size = new Size(102, 24);
            lblDescription.TabIndex = 16;
            lblDescription.Text = "وصف المؤسسة";
            // 
            // txtOrganizationName
            // 
            txtOrganizationName.Font = new Font("Cairo", 11.249999F, FontStyle.Bold, GraphicsUnit.Point, 0);
            txtOrganizationName.Location = new Point(226, 65);
            txtOrganizationName.Margin = new Padding(4, 3, 4, 3);
            txtOrganizationName.Name = "txtOrganizationName";
            txtOrganizationName.RightToLeft = RightToLeft.No;
            txtOrganizationName.Size = new Size(420, 36);
            txtOrganizationName.TabIndex = 17;
            txtOrganizationName.Text = "اسم المؤسسة";
            txtOrganizationName.TextAlign = HorizontalAlignment.Right;
            // 
            // lblOrganizationName
            // 
            lblOrganizationName.AutoSize = true;
            lblOrganizationName.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lblOrganizationName.Location = new Point(553, 38);
            lblOrganizationName.Margin = new Padding(4, 0, 4, 0);
            lblOrganizationName.Name = "lblOrganizationName";
            lblOrganizationName.Size = new Size(94, 24);
            lblOrganizationName.TabIndex = 18;
            lblOrganizationName.Text = "اسم المؤسسة";
            // 
            // groupBox3
            // 
            groupBox3.Controls.Add(btnRestore);
            groupBox3.Controls.Add(radioButtonNetwork);
            groupBox3.Controls.Add(btnBackup);
            groupBox3.Controls.Add(btn_update);
            groupBox3.Controls.Add(radioButtonLocal);
            groupBox3.Controls.Add(edt_password);
            groupBox3.Controls.Add(edt_username);
            groupBox3.Controls.Add(edt_timeout);
            groupBox3.Controls.Add(edt_database);
            groupBox3.Controls.Add(btn_saveconstring);
            groupBox3.Controls.Add(edt_servername);
            groupBox3.Controls.Add(label5);
            groupBox3.Controls.Add(label4);
            groupBox3.Controls.Add(label3);
            groupBox3.Controls.Add(label2);
            groupBox3.Controls.Add(label1);
            groupBox3.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBox3.Location = new Point(681, 31);
            groupBox3.Name = "groupBox3";
            groupBox3.RightToLeft = RightToLeft.Yes;
            groupBox3.Size = new Size(564, 416);
            groupBox3.TabIndex = 15;
            groupBox3.TabStop = false;
            groupBox3.Text = "ضبط الاتصال";
            // 
            // radioButtonNetwork
            // 
            radioButtonNetwork.AutoSize = true;
            radioButtonNetwork.Location = new Point(126, 31);
            radioButtonNetwork.Name = "radioButtonNetwork";
            radioButtonNetwork.RightToLeft = RightToLeft.No;
            radioButtonNetwork.Size = new Size(123, 34);
            radioButtonNetwork.TabIndex = 19;
            radioButtonNetwork.TabStop = true;
            radioButtonNetwork.Text = "اتصال شبكي";
            radioButtonNetwork.UseVisualStyleBackColor = true;
            radioButtonNetwork.CheckedChanged += radioButtonNetwork_CheckedChanged_1;
            // 
            // radioButtonLocal
            // 
            radioButtonLocal.AutoSize = true;
            radioButtonLocal.Location = new Point(283, 31);
            radioButtonLocal.Name = "radioButtonLocal";
            radioButtonLocal.RightToLeft = RightToLeft.No;
            radioButtonLocal.Size = new Size(118, 34);
            radioButtonLocal.TabIndex = 19;
            radioButtonLocal.TabStop = true;
            radioButtonLocal.Text = "اتصال محلي";
            radioButtonLocal.UseVisualStyleBackColor = true;
            radioButtonLocal.CheckedChanged += radioButtonLocal_CheckedChanged_1;
            // 
            // edt_password
            // 
            edt_password.Font = new Font("Cairo", 12F, FontStyle.Bold);
            edt_password.Location = new Point(7, 256);
            edt_password.Margin = new Padding(4, 3, 4, 3);
            edt_password.Name = "edt_password";
            edt_password.PasswordChar = '*';
            edt_password.RightToLeft = RightToLeft.No;
            edt_password.Size = new Size(394, 37);
            edt_password.TabIndex = 17;
            edt_password.Text = "1984";
            // 
            // edt_username
            // 
            edt_username.Font = new Font("Cairo", 12F, FontStyle.Bold);
            edt_username.Location = new Point(7, 213);
            edt_username.Margin = new Padding(4, 3, 4, 3);
            edt_username.Name = "edt_username";
            edt_username.PasswordChar = '*';
            edt_username.RightToLeft = RightToLeft.No;
            edt_username.Size = new Size(394, 37);
            edt_username.TabIndex = 17;
            edt_username.Text = "sa";
            // 
            // edt_timeout
            // 
            edt_timeout.Font = new Font("Cairo", 12F, FontStyle.Bold);
            edt_timeout.Location = new Point(7, 170);
            edt_timeout.Margin = new Padding(4, 3, 4, 3);
            edt_timeout.Name = "edt_timeout";
            edt_timeout.RightToLeft = RightToLeft.No;
            edt_timeout.Size = new Size(394, 37);
            edt_timeout.TabIndex = 17;
            edt_timeout.Text = "120";
            // 
            // edt_database
            // 
            edt_database.Font = new Font("Cairo", 12F, FontStyle.Bold);
            edt_database.Location = new Point(7, 127);
            edt_database.Margin = new Padding(4, 3, 4, 3);
            edt_database.Name = "edt_database";
            edt_database.RightToLeft = RightToLeft.No;
            edt_database.Size = new Size(394, 37);
            edt_database.TabIndex = 17;
            edt_database.Text = "HRMSDB";
            // 
            // btn_saveconstring
            // 
            btn_saveconstring.BackColor = Color.FromArgb(45, 66, 91);
            btn_saveconstring.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btn_saveconstring.ForeColor = Color.White;
            btn_saveconstring.Image = Properties.Resources.ok_32px;
            btn_saveconstring.ImageAlign = ContentAlignment.MiddleRight;
            btn_saveconstring.Location = new Point(284, 304);
            btn_saveconstring.Margin = new Padding(4, 3, 4, 3);
            btn_saveconstring.Name = "btn_saveconstring";
            btn_saveconstring.Size = new Size(270, 46);
            btn_saveconstring.TabIndex = 2;
            btn_saveconstring.Text = "حفظ الاتصال";
            btn_saveconstring.UseVisualStyleBackColor = false;
            btn_saveconstring.Click += btn_saveconstring_Click;
            // 
            // edt_servername
            // 
            edt_servername.Font = new Font("Cairo", 12F, FontStyle.Bold);
            edt_servername.Location = new Point(7, 84);
            edt_servername.Margin = new Padding(4, 3, 4, 3);
            edt_servername.Name = "edt_servername";
            edt_servername.RightToLeft = RightToLeft.No;
            edt_servername.Size = new Size(394, 37);
            edt_servername.TabIndex = 17;
            edt_servername.Text = ".\\SQLEXPRESS";
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Font = new Font("Cairo", 12F, FontStyle.Bold);
            label5.Location = new Point(409, 259);
            label5.Margin = new Padding(4, 0, 4, 0);
            label5.Name = "label5";
            label5.Size = new Size(87, 30);
            label5.TabIndex = 18;
            label5.Text = "كلمة السر";
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Font = new Font("Cairo", 12F, FontStyle.Bold);
            label4.Location = new Point(409, 216);
            label4.Margin = new Padding(4, 0, 4, 0);
            label4.Name = "label4";
            label4.Size = new Size(120, 30);
            label4.TabIndex = 18;
            label4.Text = "اسم المستخدم";
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Font = new Font("Cairo", 12F, FontStyle.Bold);
            label3.Location = new Point(409, 177);
            label3.Margin = new Padding(4, 0, 4, 0);
            label3.Name = "label3";
            label3.Size = new Size(144, 30);
            label3.TabIndex = 18;
            label3.Text = "زمن الاتصال (ثانية)";
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Font = new Font("Cairo", 12F, FontStyle.Bold);
            label2.Location = new Point(409, 130);
            label2.Margin = new Padding(4, 0, 4, 0);
            label2.Name = "label2";
            label2.Size = new Size(114, 30);
            label2.TabIndex = 18;
            label2.Text = "قاعدة البيانات";
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Font = new Font("Cairo", 12F, FontStyle.Bold);
            label1.Location = new Point(409, 84);
            label1.Margin = new Padding(4, 0, 4, 0);
            label1.Name = "label1";
            label1.Size = new Size(102, 30);
            label1.TabIndex = 18;
            label1.Text = "اسم السيرفر";
            // 
            // SettingsForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1257, 464);
            Controls.Add(groupBox3);
            Controls.Add(groupBox2);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon)resources.GetObject("$this.Icon");
            Margin = new Padding(4, 3, 4, 3);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "SettingsForm";
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "الإعدادات";
            Load += SettingsForm_Load;
            ((System.ComponentModel.ISupportInitialize)pictureBoxLogo).EndInit();
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            groupBox3.ResumeLayout(false);
            groupBox3.PerformLayout();
            ResumeLayout(false);
        }
        private System.Windows.Forms.PictureBox pictureBoxLogo;
        private System.Windows.Forms.Button btnSelectLogo;
        private System.Windows.Forms.Button btnBackup;
        private System.Windows.Forms.Button btnRestore;
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnClear;
        private Button btn_update;
        private GroupBox groupBox2;
        private ComboBox cmbTheme;
        private Label lblTheme;
        private TextBox txtDescription;
        private Label lblDescription;
        private TextBox txtOrganizationName;
        private Label lblOrganizationName;
        private ToolTip toolTip1;
        private GroupBox groupBox3;
        private RadioButton radioButtonNetwork;
        private RadioButton radioButtonLocal;
        private TextBox edt_password;
        private TextBox edt_username;
        private TextBox edt_timeout;
        private TextBox edt_database;
        private TextBox edt_servername;
        private Label label5;
        private Label label4;
        private Label label3;
        private Label label2;
        private Label label1;
        private Button btn_saveconstring;
    }
}