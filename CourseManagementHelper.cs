using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace EmployeeManagementSystem
{
    /// <summary>
    /// فئة مساعدة لإدارة العمليات المتقدمة للدورات التدريبية
    /// </summary>
    public static class CourseManagementHelper
    {
        /// <summary>
        /// إرسال إشعارات تلقائية للدورات التي تبدأ قريباً
        /// </summary>
        public static async Task SendUpcomingCourseNotificationsAsync()
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    
                    // البحث عن الدورات التي تبدأ خلال 3 أيام ولم يتم إرسال إشعار لها
                    string sql = @"
                        SELECT CourseId, EmployeeName, CourseType, CourseNumber, StartDate, Location
                        FROM Courses 
                        WHERE StartDate BETWEEN GETDATE() AND DATEADD(day, 3, GETDATE())
                        AND NotificationSent = 0
                        AND IsActive = 1";

                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        var coursesToNotify = new List<Course>();
                        
                        while (reader.Read())
                        {
                            coursesToNotify.Add(new Course
                            {
                                CourseId = reader.GetInt32("CourseId"),
                                EmployeeName = reader["EmployeeName"].ToString(),
                                CourseType = reader["CourseType"].ToString(),
                                CourseNumber = reader["CourseNumber"].ToString(),
                                StartDate = reader.GetDateTime("StartDate"),
                                Location = reader["Location"]?.ToString() ?? ""
                            });
                        }

                        reader.Close();

                        // إرسال الإشعارات
                        foreach (var course in coursesToNotify)
                        {
                            var notification = new CourseNotification
                            {
                                CourseId = course.CourseId,
                                EmployeeName = course.EmployeeName,
                                NotificationType = "تذكير بداية الدورة",
                                Message = $"تذكير: ستبدأ الدورة التدريبية '{course.CourseType}' رقم {course.CourseNumber} في {course.StartDate:yyyy-MM-dd} في {course.Location}",
                                SentDate = DateTime.Now,
                                IsRead = false,
                                Priority = "مهم"
                            };

                            DatabaseHelper.AddCourseNotification(notification);

                            // تحديث حالة الإشعار في جدول الدورات
                            string updateSql = "UPDATE Courses SET NotificationSent = 1 WHERE CourseId = @CourseId";
                            using (var updateCommand = new SqlCommand(updateSql, connection))
                            {
                                updateCommand.Parameters.AddWithValue("@CourseId", course.CourseId);
                                updateCommand.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إرسال إشعارات الدورات: {ex.Message}");
            }
        }

        /// <summary>
        /// حساب إحصائيات الدورات
        /// </summary>
        public static CourseStatistics GetCourseStatistics()
        {
            var stats = new CourseStatistics();

            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();

                    // إجمالي الدورات
                    string totalSql = "SELECT COUNT(*) FROM Courses WHERE IsActive = 1";
                    using (var command = new SqlCommand(totalSql, connection))
                    {
                        stats.TotalCourses = Convert.ToInt32(command.ExecuteScalar());
                    }

                    // الدورات النشطة
                    string activeSql = "SELECT COUNT(*) FROM Courses WHERE Status = 'جارية' AND IsActive = 1";
                    using (var command = new SqlCommand(activeSql, connection))
                    {
                        stats.ActiveCourses = Convert.ToInt32(command.ExecuteScalar());
                    }

                    // الدورات المكتملة
                    string completedSql = "SELECT COUNT(*) FROM Courses WHERE Status = 'مكتملة' AND IsActive = 1";
                    using (var command = new SqlCommand(completedSql, connection))
                    {
                        stats.CompletedCourses = Convert.ToInt32(command.ExecuteScalar());
                    }

                    // الدورات المجدولة
                    string scheduledSql = "SELECT COUNT(*) FROM Courses WHERE Status = 'مجدولة' AND IsActive = 1";
                    using (var command = new SqlCommand(scheduledSql, connection))
                    {
                        stats.ScheduledCourses = Convert.ToInt32(command.ExecuteScalar());
                    }

                    // متوسط التقييم
                    string avgRatingSql = "SELECT AVG(CAST(Rating AS FLOAT)) FROM CourseEvaluations";
                    using (var command = new SqlCommand(avgRatingSql, connection))
                    {
                        var result = command.ExecuteScalar();
                        stats.AverageRating = result != DBNull.Value ? Convert.ToDouble(result) : 0;
                    }

                    // معدل الحضور
                    string attendanceSql = @"
                        SELECT 
                            COUNT(CASE WHEN IsPresent = 1 THEN 1 END) * 100.0 / COUNT(*) 
                        FROM CourseAttendance";
                    using (var command = new SqlCommand(attendanceSql, connection))
                    {
                        var result = command.ExecuteScalar();
                        stats.AttendanceRate = result != DBNull.Value ? Convert.ToDouble(result) : 0;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب إحصائيات الدورات: {ex.Message}");
            }

            return stats;
        }

        /// <summary>
        /// الحصول على الدورات المنتهية الصلاحية
        /// </summary>
        public static DataTable GetExpiredCourses()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"
                    SELECT 
                        CourseId as 'معرف الدورة',
                        EmployeeName as 'اسم الموظف',
                        CourseType as 'نوع الدورة',
                        CourseNumber as 'رقم الدورة',
                        EndDate as 'تاريخ الانتهاء',
                        DATEDIFF(day, EndDate, GETDATE()) as 'أيام منذ الانتهاء'
                    FROM Courses 
                    WHERE EndDate < GETDATE() 
                    AND Status != 'مكتملة'
                    AND IsActive = 1
                    ORDER BY EndDate DESC";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    DataTable table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        /// <summary>
        /// تحديث حالة الدورات المنتهية تلقائياً
        /// </summary>
        public static async Task UpdateExpiredCoursesStatusAsync()
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = @"
                        UPDATE Courses 
                        SET Status = 'مكتملة', 
                            UpdatedDate = GETDATE(),
                            UpdatedBy = 'النظام'
                        WHERE EndDate < GETDATE() 
                        AND Status != 'مكتملة'
                        AND Status != 'ملغية'
                        AND IsActive = 1";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        int updatedRows = command.ExecuteNonQuery();
                        System.Diagnostics.Debug.WriteLine($"تم تحديث {updatedRows} دورة منتهية الصلاحية");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الدورات المنتهية: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على تقرير شامل للدورات
        /// </summary>
        public static DataTable GetComprehensiveCourseReport()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"
                    SELECT 
                        c.CourseId as 'معرف الدورة',
                        c.EmployeeName as 'اسم الموظف',
                        c.CourseType as 'نوع الدورة',
                        c.CourseNumber as 'رقم الدورة',
                        c.Category as 'الفئة',
                        c.StartDate as 'تاريخ البداية',
                        c.EndDate as 'تاريخ النهاية',
                        c.Status as 'الحالة',
                        c.Location as 'الموقع',
                        c.MaxParticipants as 'العدد الأقصى',
                        c.CurrentParticipants as 'العدد الحالي',
                        c.Cost as 'التكلفة',
                        c.Priority as 'الأولوية',
                        CASE WHEN c.CertificateIssued = 1 THEN 'نعم' ELSE 'لا' END as 'الشهادة',
                        ISNULL(AVG(CAST(e.Rating AS FLOAT)), 0) as 'متوسط التقييم',
                        COUNT(DISTINCT a.AttendanceId) as 'عدد أيام الحضور',
                        COUNT(CASE WHEN a.IsPresent = 1 THEN 1 END) as 'أيام الحضور الفعلي'
                    FROM Courses c
                    LEFT JOIN CourseEvaluations e ON c.CourseId = e.CourseId
                    LEFT JOIN CourseAttendance a ON c.CourseId = a.CourseId
                    WHERE c.IsActive = 1
                    GROUP BY c.CourseId, c.EmployeeName, c.CourseType, c.CourseNumber, c.Category,
                             c.StartDate, c.EndDate, c.Status, c.Location, c.MaxParticipants,
                             c.CurrentParticipants, c.Cost, c.Priority, c.CertificateIssued
                    ORDER BY c.StartDate DESC";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    DataTable table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }
    }

    /// <summary>
    /// فئة إحصائيات الدورات
    /// </summary>
    public class CourseStatistics
    {
        public int TotalCourses { get; set; }
        public int ActiveCourses { get; set; }
        public int CompletedCourses { get; set; }
        public int ScheduledCourses { get; set; }
        public double AverageRating { get; set; }
        public double AttendanceRate { get; set; }
    }
}