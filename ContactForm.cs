using System;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class ContactForm : Form
    {
        private DataTable employeesData;
        private DataTable filteredData;

        public ContactForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("بدء تهيئة النموذج...");

                // إعداد النموذج
                this.WindowState = FormWindowState.Normal;

                // إعداد DataGridView
                SetupDataGridView();

                // تحميل البيانات
                LoadEmployeesData();

                System.Diagnostics.Debug.WriteLine("تم تهيئة النموذج بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة النموذج: {ex.Message}");
                MessageBox.Show($"خطأ في تهيئة نموذج دليل الاتصال: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);

                // محاولة إنشاء جدول فارغ كحل أخير
                try
                {
                    CreateEmptyDataTable();
                }
                catch (Exception createEx)
                {
                    System.Diagnostics.Debug.WriteLine($"فشل في إنشاء الجدول الفارغ: {createEx.Message}");
                }
            }
        }

        private void SetupDataGridView()
        {
            // إعداد خصائص DataGridView
            dataGridViewContacts.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(237, 243, 247);
            dataGridViewContacts.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewContacts.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewContacts.GridColor = Color.FromArgb(189, 195, 199);
            dataGridViewContacts.BorderStyle = BorderStyle.Fixed3D;

            // إعداد الأحداث
            dataGridViewContacts.SelectionChanged += dataGridViewContacts_SelectionChanged;
            dataGridViewContacts.CellDoubleClick += dataGridViewContacts_CellDoubleClick;
            dataGridViewContacts.DataBindingComplete += dataGridViewContacts_DataBindingComplete;
        }

        private void dataGridViewContacts_DataBindingComplete(object sender, DataGridViewBindingCompleteEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("DataBindingComplete event triggered");

                // تنسيق الأعمدة بعد اكتمال ربط البيانات
                FormatColumns();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في DataBindingComplete: {ex.Message}");
            }
        }

        private void LoadEmployeesData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("بدء تحميل بيانات الموظفين...");

                // جلب بيانات الموظفين من قاعدة البيانات
                employeesData = GetEmployeesContactData();

                if (employeesData != null && employeesData.Rows.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"تم جلب {employeesData.Rows.Count} موظف");

                    filteredData = employeesData.Copy();

                    // ربط البيانات بـ DataGridView بطريقة آمنة
                    BindDataSafely();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("لا توجد بيانات موظفين");

                    // إنشاء جدول فارغ مع الأعمدة المطلوبة
                    CreateEmptyDataTable();
                    MessageBox.Show("لا توجد بيانات موظفين أو لا توجد أرقام هواتف مسجلة.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // تحديث عداد الموظفين
                UpdateEmployeeCount();

                // تحديث حالة الأزرار
                UpdateButtonStates();

                System.Diagnostics.Debug.WriteLine("انتهاء تحميل بيانات الموظفين");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات: {ex.Message}");

                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);

                // إنشاء جدول فارغ في حالة الخطأ
                CreateEmptyDataTable();
                UpdateEmployeeCount();
                UpdateButtonStates();
            }
        }

        private void BindDataSafely()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("بدء ربط البيانات...");

                // مسح DataSource أولاً
                dataGridViewContacts.DataSource = null;

                // انتظار قصير
                Application.DoEvents();

                // ربط البيانات الجديدة
                dataGridViewContacts.DataSource = filteredData;

                // انتظار لاكتمال ربط البيانات
                Application.DoEvents();

                // تنسيق الأعمدة سيتم من خلال DataBindingComplete event
                System.Diagnostics.Debug.WriteLine("تم ربط البيانات بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في ربط البيانات: {ex.Message}");
                throw;
            }
        }

        private void CreateEmptyDataTable()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("إنشاء جدول فارغ...");

                employeesData = new DataTable();
                employeesData.Columns.Add("كود الموظف", typeof(int));
                employeesData.Columns.Add("الصنف", typeof(string));
                employeesData.Columns.Add("اسم الموظف", typeof(string));
                employeesData.Columns.Add("رقم الهاتف", typeof(string));
                employeesData.Columns.Add("المحافظة", typeof(string));

                filteredData = employeesData.Copy();

                // ربط الجدول الفارغ بطريقة آمنة
                dataGridViewContacts.DataSource = null;
                Application.DoEvents();
                dataGridViewContacts.DataSource = filteredData;
                Application.DoEvents();

                // محاولة تنسيق الأعمدة (قد لا تعمل مع الجدول الفارغ)
                try
                {
                    System.Threading.Thread.Sleep(100);
                    FormatColumns();
                }
                catch (Exception formatEx)
                {
                    System.Diagnostics.Debug.WriteLine($"لا يمكن تنسيق الأعمدة للجدول الفارغ: {formatEx.Message}");
                }

                System.Diagnostics.Debug.WriteLine("تم إنشاء الجدول الفارغ بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء الجدول الفارغ: {ex.Message}");

                // في حالة فشل إنشاء الجدول، مسح DataSource
                dataGridViewContacts.DataSource = null;
            }
        }

        private DataTable GetEmployeesContactData()
        {
            // إنشاء جدول البيانات
            DataTable contactData = new DataTable();
            contactData.Columns.Add("كود الموظف", typeof(int));
            contactData.Columns.Add("الصنف", typeof(string));
            contactData.Columns.Add("اسم الموظف", typeof(string));
            contactData.Columns.Add("رقم الهاتف", typeof(string));
            contactData.Columns.Add("المحافظة", typeof(string));

            try
            {
                // التحقق من سلسلة الاتصال
                string connectionString = ConStringHelper.GetConnectionString();
                if (string.IsNullOrEmpty(connectionString))
                {
                    throw new Exception("سلسلة الاتصال بقاعدة البيانات غير محددة. تأكد من إعداد الاتصال بقاعدة البيانات.");
                }

                // جلب البيانات من قاعدة البيانات
                using (var connection = new System.Data.SqlClient.SqlConnection(connectionString))
                {
                    connection.Open();
                    string sql = @"SELECT
                        EmployeeCode,
                        ISNULL(Name, '') as Name,
                        ISNULL(PhoneNumber, '') as PhoneNumber,
                        ISNULL(Category, '') as Category,
                        ISNULL(Province, '') as Province
                    FROM Employees
                    WHERE PhoneNumber IS NOT NULL
                        AND PhoneNumber != ''
                        AND LEN(LTRIM(RTRIM(PhoneNumber))) > 0
                    ORDER BY Name";

                    using (var command = new System.Data.SqlClient.SqlCommand(sql, connection))
                    {
                        command.CommandTimeout = 30; // 30 ثانية timeout

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var row = contactData.NewRow();
                                row["كود الموظف"] = reader["EmployeeCode"];
                                row["الصنف"] = reader["Category"]?.ToString() ?? "";
                                row["اسم الموظف"] = reader["Name"]?.ToString() ?? "";
                                row["رقم الهاتف"] = reader["PhoneNumber"]?.ToString() ?? "";
                                row["المحافظة"] = reader["Province"]?.ToString() ?? "";
                                contactData.Rows.Add(row);
                            }
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"تم جلب {contactData.Rows.Count} موظف من قاعدة البيانات");
            }
            catch (System.Data.SqlClient.SqlException sqlEx)
            {
                string errorMessage = $"خطأ في قاعدة البيانات: {sqlEx.Message}";
                if (sqlEx.Number == 2) // Cannot open database
                {
                    errorMessage = "لا يمكن الاتصال بقاعدة البيانات. تأكد من أن الخادم يعمل وأن اسم قاعدة البيانات صحيح.";
                }
                else if (sqlEx.Number == 18456) // Login failed
                {
                    errorMessage = "فشل في تسجيل الدخول إلى قاعدة البيانات. تحقق من اسم المستخدم وكلمة المرور.";
                }

                throw new Exception(errorMessage, sqlEx);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ عام في الاتصال بقاعدة البيانات: {ex.Message}", ex);
            }

            return contactData;
        }

        private void FormatColumns()
        {
            try
            {
                // التحقق من أن DataGridView وDataSource ليسا null
                if (dataGridViewContacts?.DataSource == null)
                {
                    System.Diagnostics.Debug.WriteLine("DataSource is null, skipping column formatting");
                    return;
                }

                if (dataGridViewContacts.Columns.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("No columns found, skipping column formatting");
                    return;
                }

                // انتظار قصير للتأكد من اكتمال ربط البيانات
                Application.DoEvents();

                // تنسيق الأعمدة بطريقة آمنة
                FormatColumnSafely("كود الموظف", 50, DataGridViewContentAlignment.MiddleCenter);
                FormatColumnSafely("الصنف", 50, DataGridViewContentAlignment.MiddleCenter);
                FormatColumnSafely("اسم الموظف", 150, DataGridViewContentAlignment.MiddleLeft);
                FormatColumnSafely("رقم الهاتف", 50, DataGridViewContentAlignment.MiddleCenter);
                FormatColumnSafely("المحافظة", 150, DataGridViewContentAlignment.MiddleCenter);

                System.Diagnostics.Debug.WriteLine("Column formatting completed successfully");
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ مع تفاصيل أكثر
                System.Diagnostics.Debug.WriteLine($"خطأ في تنسيق الأعمدة: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        private void FormatColumnSafely(string columnName, int width, DataGridViewContentAlignment alignment)
        {
            try
            {
                if (dataGridViewContacts.Columns.Contains(columnName))
                {
                    var column = dataGridViewContacts.Columns[columnName];
                    if (column != null)
                    {
                        column.Width = width;
                        column.DefaultCellStyle.Alignment = alignment;
                        System.Diagnostics.Debug.WriteLine($"Formatted column: {columnName}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"Column {columnName} is null");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Column {columnName} not found");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error formatting column {columnName}: {ex.Message}");
            }
        }

        private void UpdateEmployeeCount()
        {
            try
            {
                int count = filteredData?.Rows.Count ?? 0;
                lblEmployeeCount.Text = count.ToString();

                if (count == 0)
                {
                    groupBoxEmployees.Text = "قائمة الموظفين (لا توجد بيانات)";
                }
                else if (count == 1)
                {
                    groupBoxEmployees.Text = "قائمة الموظفين (موظف واحد)";
                }
                else
                {
                    groupBoxEmployees.Text = $"قائمة الموظفين ({count} موظف)";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عداد الموظفين: {ex.Message}");
                lblEmployeeCount.Text = "0";
                groupBoxEmployees.Text = "قائمة الموظفين";
            }
        }

        private void UpdateButtonStates()
        {
            bool hasSelection = dataGridViewContacts.SelectedRows.Count > 0;
            bool hasPhoneNumber = false;

            if (hasSelection)
            {
                var selectedRow = dataGridViewContacts.SelectedRows[0];
                string phoneNumber = selectedRow.Cells["رقم الهاتف"].Value?.ToString() ?? "";
                hasPhoneNumber = !string.IsNullOrWhiteSpace(phoneNumber);
                
                // تحديث معلومات الموظف المحدد
                string employeeName = selectedRow.Cells["اسم الموظف"].Value?.ToString() ?? "";
                lblSelectedEmployee.Text = $"الموظف المحدد: {employeeName}";
                lblSelectedPhone.Text = $"رقم الهاتف: {phoneNumber}";
            }
            else
            {
                lblSelectedEmployee.Text = "لم يتم اختيار موظف";
                lblSelectedPhone.Text = "رقم الهاتف: -";
            }

            btnWhatsApp.Enabled = hasSelection && hasPhoneNumber;
            btnCall.Enabled = hasSelection && hasPhoneNumber;
        }

        private void FilterEmployees(string searchText)
        {
            try
            {
                if (employeesData == null || employeesData.Rows.Count == 0)
                {
                    UpdateEmployeeCount();
                    UpdateButtonStates();
                    return;
                }

                if (string.IsNullOrWhiteSpace(searchText))
                {
                    filteredData = employeesData.Copy();
                }
                else
                {
                    // البحث مع تجاهل حالة الأحرف
                    searchText = searchText.Trim().ToLower();

                    var filteredRows = employeesData.AsEnumerable().Where(row =>
                        row.Field<string>("اسم الموظف")?.ToLower().Contains(searchText) == true ||
                        row.Field<string>("رقم الهاتف")?.Contains(searchText) == true ||
                        row.Field<string>("الصنف")?.ToLower().Contains(searchText) == true ||
                        row.Field<string>("المحافظة")?.ToLower().Contains(searchText) == true ||
                        row.Field<int>("كود الموظف").ToString().Contains(searchText) == true
                    );

                    if (filteredRows.Any())
                    {
                        filteredData = filteredRows.CopyToDataTable();
                    }
                    else
                    {
                        // إنشاء جدول فارغ بنفس الهيكل
                        filteredData = employeesData.Clone();
                    }
                }

                // ربط البيانات بطريقة آمنة
                dataGridViewContacts.DataSource = null;
                Application.DoEvents();
                dataGridViewContacts.DataSource = filteredData;

                UpdateEmployeeCount();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث: {ex.Message}");
                // في حالة الخطأ، عرض جميع البيانات
                if (employeesData != null)
                {
                    filteredData = employeesData.Copy();
                    dataGridViewContacts.DataSource = filteredData;
                }
                UpdateEmployeeCount();
                UpdateButtonStates();
            }
        }

        private void OpenWhatsApp(string phoneNumber)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(phoneNumber))
                {
                    MessageBox.Show("رقم الهاتف غير متوفر للموظف المحدد.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // تنظيف رقم الهاتف من الأحرف غير الرقمية
                string cleanPhone = new string(phoneNumber.Where(c => char.IsDigit(c) || c == '+').ToArray());

                // إزالة الأصفار في البداية إذا كان هناك رمز دولة
                if (cleanPhone.StartsWith("00"))
                {
                    cleanPhone = "+" + cleanPhone.Substring(2);
                }

                // إضافة رمز الدولة إذا لم يكن موجوداً
                if (!cleanPhone.StartsWith("+"))
                {
                    // إزالة الصفر في البداية إذا وجد
                    if (cleanPhone.StartsWith("0"))
                    {
                        cleanPhone = cleanPhone.Substring(1);
                    }

                    cleanPhone = "+964" + cleanPhone; // رمز العراق
                }

                // فتح واتساب
                string whatsappUrl = $"https://wa.me/{cleanPhone.Replace("+", "")}";

                // عرض رسالة تأكيد
                DialogResult result = MessageBox.Show(
                    $"سيتم فتح محادثة واتساب مع الرقم: {cleanPhone}\nهل تريد المتابعة؟",
                    "تأكيد",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = whatsappUrl,
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح واتساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void MakePhoneCall(string phoneNumber)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(phoneNumber))
                {
                    MessageBox.Show("رقم الهاتف غير متوفر للموظف المحدد.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // عرض رسالة تأكيد
                DialogResult result = MessageBox.Show(
                    $"سيتم إجراء مكالمة إلى الرقم: {phoneNumber}\nهل تريد المتابعة؟",
                    "تأكيد المكالمة",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // فتح تطبيق الهاتف
                    string telUrl = $"tel:{phoneNumber}";
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = telUrl,
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إجراء المكالمة: {ex.Message}\n\nتأكد من وجود تطبيق هاتف على النظام.", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // الأحداث (Events)
        private void ContactForm_Load(object sender, EventArgs e)
        {
            txtSearch.Focus();
        }

        private void txtSearch_TextChanged(object sender, EventArgs e)
        {
            FilterEmployees(txtSearch.Text.Trim());
        }

        private void dataGridViewContacts_SelectionChanged(object sender, EventArgs e)
        {
            UpdateButtonStates();
        }

        private void dataGridViewContacts_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && btnWhatsApp.Enabled)
            {
                btnWhatsApp_Click(sender, e);
            }
        }

        private void btnWhatsApp_Click(object sender, EventArgs e)
        {
            if (dataGridViewContacts.SelectedRows.Count > 0)
            {
                var selectedRow = dataGridViewContacts.SelectedRows[0];
                string phoneNumber = selectedRow.Cells["رقم الهاتف"].Value?.ToString() ?? "";
                
                if (!string.IsNullOrWhiteSpace(phoneNumber))
                {
                    OpenWhatsApp(phoneNumber);
                }
            }
        }

        private void btnCall_Click(object sender, EventArgs e)
        {
            if (dataGridViewContacts.SelectedRows.Count > 0)
            {
                var selectedRow = dataGridViewContacts.SelectedRows[0];
                string phoneNumber = selectedRow.Cells["رقم الهاتف"].Value?.ToString() ?? "";
                
                if (!string.IsNullOrWhiteSpace(phoneNumber))
                {
                    MakePhoneCall(phoneNumber);
                }
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadEmployeesData();
            txtSearch.Clear();
        }

        private void btnClearSearch_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            txtSearch.Focus();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
