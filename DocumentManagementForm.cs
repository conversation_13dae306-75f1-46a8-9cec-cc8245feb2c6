using System;
using System.Data;
using System.IO;
using System.Windows.Forms;
using System.Drawing;
using System.Data.SqlClient;
//using WIA;
using System.Text.RegularExpressions;

namespace EmployeeManagementSystem
{
    public partial class DocumentManagementForm : Form
    {
        private readonly int employeeCode;
        private readonly string employeeName;
        private readonly DataTable documentsTable;

        public DocumentManagementForm(int employeeCode, string employeeName)
        {
            this.employeeCode = employeeCode;
            this.employeeName = employeeName;
            this.documentsTable = new DataTable();
            InitializeComponent();
            InitializeGrid();
            this.Text = $"إدارة وثائق الموظف - {employeeName}";

            // تطبيق الثيم المحفوظ
            string theme = "default";
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT TOP 1 Theme FROM Settings";
                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            theme = reader["Theme"]?.ToString() ?? "default";
                        }
                    }
                }
            }
            catch
            {
                theme = "default";
            }

            ThemeManager.ApplyThemeToForm(this);

            LoadDocuments();
            UpdateButtonStates();
            cmbEmployeeFiles.SelectedIndexChanged += CmbEmployeeFiles_SelectedIndexChanged;
        }

        private void InitializeGrid()
        {
            dgvDocuments.AutoGenerateColumns = false;
            dgvDocuments.Columns.Clear();

            // إضافة الأعمدة مرة واحدة فقط
            if (dgvDocuments.Columns.Count == 0)
            {
                dgvDocuments.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "DocId",
                    HeaderText = "رقم الوثيقة",
                    DataPropertyName = "DocId",
                    Width = 100
                });

                dgvDocuments.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Description",
                    HeaderText = "وصف الوثيقة",
                    DataPropertyName = "Description",
                    Width = 300
                });

                dgvDocuments.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "UploadDate",
                    HeaderText = "تاريخ الرفع",
                    DataPropertyName = "UploadDate",
                    Width = 150
                });

                dgvDocuments.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "FilePath",
                    HeaderText = "مسار الملف",
                    DataPropertyName = "FilePath",
                    Visible = false
                });

                // تنسيق الجدول
                dgvDocuments.EnableHeadersVisualStyles = false;
                dgvDocuments.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(45, 66, 91);
                dgvDocuments.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
                dgvDocuments.ColumnHeadersDefaultCellStyle.Font = new Font("Cairo", 12F, FontStyle.Bold);
                dgvDocuments.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

                dgvDocuments.DefaultCellStyle.Font = new Font("Cairo", 12F);
                dgvDocuments.DefaultCellStyle.SelectionBackColor = Color.FromArgb(87, 115, 153);
                dgvDocuments.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

                dgvDocuments.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(237, 243, 247);
                dgvDocuments.BackgroundColor = Color.White;
                dgvDocuments.BorderStyle = BorderStyle.None;
                dgvDocuments.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
                dgvDocuments.GridColor = Color.FromArgb(223, 230, 233);
                dgvDocuments.ReadOnly = true;
                dgvDocuments.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
                dgvDocuments.MultiSelect = false;

            }
        }

        private void LoadDocuments()
        {
            try
            {
                var dt = DatabaseHelper.GetEmployeeDocuments(employeeCode);
                documentsTable.Clear();
                documentsTable.Load(dt.CreateDataReader());
                dgvDocuments.DataSource = documentsTable;

                // Update ComboBox
                cmbEmployeeFiles.Items.Clear();
                foreach (DataRow row in documentsTable.Rows)
                {
                    string description = row["Description"]?.ToString() ?? "";
                    string filePath = row["FilePath"]?.ToString() ?? "";
                    if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                    {
                        cmbEmployeeFiles.Items.Add(new FileItem(description, filePath));
                    }
                }

                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الوثائق: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private class FileItem
        {
            public string Description { get; }
            public string FilePath { get; }

            public FileItem(string description, string filePath)
            {
                Description = description;
                FilePath = filePath;
            }

            public override string ToString()
            {
                return Description;
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog dialog = new OpenFileDialog())
            {
                dialog.Filter = "كل الملفات|*.*|PDF Files|*.pdf|Word Files|*.doc;*.docx|Image Files|*.jpg;*.jpeg;*.png;*.gif;*.bmp";
                dialog.Title = "اختيار وثيقة";
                dialog.Multiselect = true;

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    if (string.IsNullOrWhiteSpace(txtDescription.Text))
                    {
                        MessageBox.Show("الرجاء إدخال وصف للوثيقة", "تنبيه",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtDescription.Focus();
                        return;
                    }

                    int successCount = 0; // عداد للملفات التي تم إضافتها بنجاح
                    int totalFiles = dialog.FileNames.Length; // إجمالي عدد الملفات المختارة

                    foreach (string sourceFile in dialog.FileNames)
                    {
                        try
                        {
                            string documentsPath = Path.Combine(Application.StartupPath, "documents");
                            if (!Directory.Exists(documentsPath))
                                Directory.CreateDirectory(documentsPath);

                            string employeeFolderPath = Path.Combine(documentsPath, employeeName);
                            if (!Directory.Exists(employeeFolderPath))
                                Directory.CreateDirectory(employeeFolderPath);

                            int nextNumber = GetNextFileNumber(employeeFolderPath);
                            string originalName = Path.GetFileNameWithoutExtension(sourceFile);
                            string extension = Path.GetExtension(sourceFile).ToLower();
                            string uniqueFileName = $"ملف مضاف {nextNumber} - {originalName}{extension}";
                            string destinationPath = Path.Combine(employeeFolderPath, uniqueFileName);

                            File.Copy(sourceFile, destinationPath, true);

                            string relativePath = Path.Combine("documents", employeeName, uniqueFileName);

                            // قراءة الملف كبايتات
                            byte[] fileBytes = File.ReadAllBytes(destinationPath);

                            // تحقق من نوع الملف هل هو صورة لضغطها
                            if (extension == ".jpg" || extension == ".jpeg" || extension == ".png" || extension == ".bmp" || extension == ".gif")
                            {
                                fileBytes = DatabaseHelper.CompressImage(fileBytes, 60L);  // ضغط الصورة
                            }
                            // ملفات أخرى تبقى كما هي بدون ضغط

                            DatabaseHelper.SaveDocumentToDatabase(employeeCode, txtDescription.Text, relativePath, fileBytes, uniqueFileName);

                            successCount++;
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في إضافة الملف {Path.GetFileName(sourceFile)}: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }


                    // عرض رسالة نجاح تبين عدد الملفات التي تمت إضافتها
                    if (successCount > 0)
                    {
                        string message = (successCount == totalFiles) ?
                            $"تم إضافة جميع المرفقات ({successCount} ملفات) بنجاح" :
                            $"تم إضافة {successCount} من {totalFiles} ملفات بنجاح";

                        MessageBox.Show(message, "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        this.BeginInvoke((MethodInvoker)(() =>
                        {
                            ToastHelper.ShowfileToast();
                        }));
                    }

                    LoadDocuments();
                    UpdateNoDocumentsLabel();
                    txtDescription.Clear();
                    txtDescription.Focus();
                }
            }
        }


        private int GetNextFileNumber(string folderPath)
        {
            int maxNumber = 0;
            var files = Directory.GetFiles(folderPath);

            foreach (var file in files)
            {
                string fileName = Path.GetFileName(file);
                if (fileName.StartsWith("ملف مضاف"))
                {
                    var match = Regex.Match(fileName, @"ملف مضاف (\d+)");
                    if (match.Success && int.TryParse(match.Groups[1].Value, out int number))
                    {
                        if (number > maxNumber)
                            maxNumber = number;
                    }
                }
            }

            return maxNumber + 1;
        }

        private void DeleteDocument()
        {
            if (dgvDocuments.CurrentRow == null)
            {
                MessageBox.Show("الرجاء تحديد الوثيقة المراد حذفها", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var docIdObj = dgvDocuments.CurrentRow.Cells["DocId"].Value;
            if (docIdObj == null || docIdObj == DBNull.Value)
            {
                MessageBox.Show("بيانات الوثيقة غير صحيحة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            int docId = Convert.ToInt32(docIdObj);
            if (MessageBox.Show("هل أنت متأكد من حذف هذه الوثيقة؟", "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                try
                {
                    DatabaseHelper.DeleteDocument(docId);
                    LoadDocuments();
                    MessageBox.Show("تم حذف الوثيقة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الوثيقة: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void ViewDocument()
        {
            if (dgvDocuments.CurrentRow == null) return;

            // جلب مسار الملف النسبي
            string relativePath = dgvDocuments.CurrentRow.Cells["FilePath"].Value?.ToString();

            if (string.IsNullOrEmpty(relativePath))
            {
                MessageBox.Show("مسار الملف غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            string fullPath = Path.Combine(Application.StartupPath, relativePath);

            if (File.Exists(fullPath))
            {
                // إذا الملف موجود على القرص افتحه مباشرة
                try
                {
                    var viewer = new DocumentViewerForm(fullPath);
                    viewer.ShowDialog(this);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ في فتح الوثيقة.\n\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                // إذا الملف غير موجود على القرص، حاول استرجاعه من قاعدة البيانات وحفظه في مجلد الموظف
                int docId = Convert.ToInt32(dgvDocuments.CurrentRow.Cells["DocId"].Value);

                // إنشاء مجلد الموظف إذا لم يكن موجوداً
                string documentsPath = Path.Combine(Application.StartupPath, "documents");
                if (!Directory.Exists(documentsPath))
                    Directory.CreateDirectory(documentsPath);

                string employeeFolderPath = Path.Combine(documentsPath, employeeName);
                if (!Directory.Exists(employeeFolderPath))
                    Directory.CreateDirectory(employeeFolderPath);

                bool loaded = DatabaseHelper.LoadDocumentFromDatabase(docId, employeeFolderPath);
                if (loaded)
                {
                    string fileName = Path.GetFileName(relativePath);
                    string restoredFilePath = Path.Combine(employeeFolderPath, fileName);

                    try
                    {
                        var viewer = new DocumentViewerForm(restoredFilePath);
                        viewer.ShowDialog(this);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"حدث خطأ في فتح الوثيقة.\n\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    MessageBox.Show("تعذر استرجاع الملف من قاعدة البيانات.", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void UpdateButtonStates()
        {
            bool hasSelection = dgvDocuments.SelectedRows.Count > 0;
            btnDelete.Enabled = hasSelection;
            btnView.Enabled = hasSelection;
        }

        private void dgvDocuments_SelectionChanged(object sender, EventArgs e)
        {
            UpdateButtonStates();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            DeleteDocument();
            ToastHelper.ShowDeletefileToast();
            UpdateNoDocumentsLabel();
        }

        private void btnView_Click(object sender, EventArgs e)
        {
            ViewDocument();
        }

        private void DocumentManagementForm_Load(object sender, EventArgs e)
        {
            UIHelper.ShowEmptyMessage(dgvDocuments, lbl_NoDocuments, "لا توجد ملفات");
        }
        private void UpdateNoDocumentsLabel()
        {
            lbl_NoDocuments.Visible = dgvDocuments.Rows.Count == 0;
        }
        private void CmbEmployeeFiles_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (cmbEmployeeFiles.SelectedItem is FileItem selectedFile)
            {
                try
                {
                    // تحويل المسار النسبي إلى مسار مطلق
                    string fullPath = Path.Combine(Application.StartupPath, selectedFile.FilePath);

                    if (File.Exists(fullPath))
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = fullPath,
                            UseShellExecute = true
                        });
                    }
                    else
                    {
                        // إذا الملف غير موجود، حاول استرجاعه من قاعدة البيانات
                        // العثور على الـ DocId من الجدول
                        int docId = -1;
                        foreach (DataRow row in documentsTable.Rows)
                        {
                            if (row["Description"].ToString() == selectedFile.Description && 
                                row["FilePath"].ToString() == selectedFile.FilePath)
                            {
                                docId = Convert.ToInt32(row["DocId"]);
                                break;
                            }
                        }

                        if (docId != -1)
                        {
                            // إنشاء مجلد الموظف إذا لم يكن موجوداً
                            string documentsPath = Path.Combine(Application.StartupPath, "documents");
                            if (!Directory.Exists(documentsPath))
                                Directory.CreateDirectory(documentsPath);

                            string employeeFolderPath = Path.Combine(documentsPath, employeeName);
                            if (!Directory.Exists(employeeFolderPath))
                                Directory.CreateDirectory(employeeFolderPath);

                            bool loaded = DatabaseHelper.LoadDocumentFromDatabase(docId, employeeFolderPath);
                            if (loaded)
                            {
                                // الآن الملف موجود، حاول فتحه مرة أخرى
                                if (File.Exists(fullPath))
                                {
                                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                                    {
                                        FileName = fullPath,
                                        UseShellExecute = true
                                    });
                                }
                                else
                                {
                                    MessageBox.Show("تعذر استرجاع الملف من قاعدة البيانات", "خطأ",
                                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                            else
                            {
                                MessageBox.Show("تعذر استرجاع الملف من قاعدة البيانات", "خطأ",
                                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        else
                        {
                            MessageBox.Show("الملف غير موجود في المسار المحدد", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }



        
        private void btn_Scan_Click(object sender, EventArgs e)
        {
            //if (string.IsNullOrWhiteSpace(txtDescription.Text))
            //{
            //    MessageBox.Show("الرجاء إدخال وصف للوثيقة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            //    txtDescription.Focus();
            //    return;
            //}

            //if (listOfScanner.SelectedIndex == -1)
            //{
            //    MessageBox.Show("الرجاء اختيار جهاز ماسح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            //    return;
            //}

            //int scanCount = (int)numericUpDown1.Value;

            //try
            //{
            //    var deviceManager = new DeviceManager();
            //    Device? scanner = null;

            //    for (int i = 1; i <= deviceManager.DeviceInfos.Count; i++)
            //    {
            //        if (deviceManager.DeviceInfos[i].Type == WiaDeviceType.ScannerDeviceType &&
            //            deviceManager.DeviceInfos[i].Properties["Name"].get_Value().ToString() == listOfScanner.SelectedItem.ToString())
            //        {
            //            scanner = deviceManager.DeviceInfos[i].Connect();
            //            break;
            //        }
            //    }

            //    if (scanner == null)
            //    {
            //        MessageBox.Show("لم يتم العثور على جهاز ماسح ضوئي", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //        return;
            //    }

            //    string documentsPath = Path.Combine(Application.StartupPath, "documents");
            //    if (!Directory.Exists(documentsPath))
            //        Directory.CreateDirectory(documentsPath);

            //    string employeeFolderPath = Path.Combine(documentsPath, employeeName);
            //    if (!Directory.Exists(employeeFolderPath))
            //        Directory.CreateDirectory(employeeFolderPath);

            //    int scannerStartNumber = GetNextScannerFileNumber(employeeFolderPath);

            //    for (int i = 0; i < scanCount; i++)
            //    {
            //        Item scanItem = scanner.Items[1];
            //        ImageFile image = (ImageFile)scanItem.Transfer("{B96B3CAF-0728-11D3-9D7B-0000F81EF32E}");

            //        string fileName = $"ملف سكنر {scannerStartNumber + i} - img.jpg";
            //        string destPath = Path.Combine(employeeFolderPath, fileName);

            //        if (File.Exists(destPath))
            //            File.Delete(destPath);

            //        image.SaveFile(destPath);

            //        // اقرأ الملف كبايتات
            //        byte[] fileBytes = File.ReadAllBytes(destPath);

            //        fileBytes = DatabaseHelper.CompressImage(fileBytes, 60L);
            //        // احفظ في قاعدة البيانات مع اسم الملف والمسار النسبي
            //        string relativePath = Path.Combine("documents", employeeName, fileName);

            //        DatabaseHelper.SaveDocumentToDatabase(employeeCode, txtDescription.Text.Trim(), relativePath, fileBytes, fileName);

            //        Thread.Sleep(500); // تأخير بسيط
            //    }

            //    MessageBox.Show("تم المسح والحفظ بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            //    LoadDocuments();
            //    UpdateNoDocumentsLabel();
            //    txtDescription.Clear();
            //    txtDescription.Focus();
            //}
            //catch (Exception ex)
            //{
            //    MessageBox.Show($"خطأ أثناء المسح: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //}
        }

        private int GetNextScannerFileNumber(string folderPath)
        {
            int maxNumber = 0;
            var files = Directory.GetFiles(folderPath);

            foreach (var file in files)
            {
                string fileName = Path.GetFileName(file);
                if (fileName.StartsWith("ملف سكنر"))
                {
                    var match = Regex.Match(fileName, @"ملف سكنر (\d+)");
                    if (match.Success && int.TryParse(match.Groups[1].Value, out int number))
                    {
                        if (number > maxNumber)
                            maxNumber = number;
                    }
                }
            }

            return maxNumber + 1;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            LoadScanners();
        }
        private void LoadScanners()
        {
            //listOfScanner.Items.Clear();
            //var deviceManager = new DeviceManager();

            //for (int i = 1; i <= deviceManager.DeviceInfos.Count; i++)
            //{
            //    if (deviceManager.DeviceInfos[i].Type == WiaDeviceType.ScannerDeviceType)
            //    {
            //        var deviceInfo = deviceManager.DeviceInfos[i];
            //        listOfScanner.Items.Add(deviceInfo.Properties["Name"].get_Value());
            //    }
            //}

            //if (listOfScanner.Items.Count > 0)
            //    listOfScanner.SelectedIndex = 0;
            //else
            //    MessageBox.Show("لا يوجد أجهزة ماسح متصلة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        private void numericUpDown1_ValueChanged(object sender, EventArgs e)
        {
            try
            {
                // استلام عدد الصور من المستخدم
                int numberOfFilesToScan = Convert.ToInt32(numericUpDown1.Value); // التحويل الآمن من Decimal إلى int

            }
            catch (Exception ex)
            {
                // في حال حدوث خطأ في تحويل القيمة
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}