using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Windows.Forms;
using EmployeeManagementSystem.LoadingGui;

namespace EmployeeManagementSystem
{
    public partial class GroupWorkPeriodForm : Form
    {
        private List<int> selectedWorkEmployees = new List<int>();
        private List<int> selectedVacationEmployees = new List<int>();
        private int selectedGroupId = 0;
        private User? currentUser = null; // إضافة المستخدم الحالي

        public GroupWorkPeriodForm()
        {
            InitializeComponent();
            InitializeDataGridViewFonts();
        }

        // Constructor جديد يستقبل المستخدم الحالي
        public GroupWorkPeriodForm(User currentUser) : this()
        {
            this.currentUser = currentUser;

            // تم إزالة الرسالة المزعجة - الفلترة تعمل تلقائياً

                // تحديث عنوان النافذة
                this.Text = "🔒 فترات العمل الجماعية - مدير القسم";
            }
            else if (EmployeeDepartmentHelper.IsGeneralManager(currentUser))
            {
                this.Text = "🌐 فترات العمل الجماعية - مدير عام";
            }
        }

        private void InitializeDataGridViewFonts()
        {
            try
            {
                // التأكد من أن جميع الخطوط متسقة
                Font dataGridFont = new Font("Cairo", 10F, FontStyle.Bold);
                
                if (dataGridViewGroups.ColumnHeadersDefaultCellStyle != null)
                    dataGridViewGroups.ColumnHeadersDefaultCellStyle.Font = dataGridFont;
                
                if (dataGridViewGroups.DefaultCellStyle != null)
                    dataGridViewGroups.DefaultCellStyle.Font = dataGridFont;
                
                if (dataGridViewGroups.RowHeadersDefaultCellStyle != null)
                    dataGridViewGroups.RowHeadersDefaultCellStyle.Font = dataGridFont;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة خطوط DataGridView: {ex.Message}");
            }
        }

        private void GroupWorkPeriodForm_Load(object sender, EventArgs e)
        {
            LoadEmployees();
            SetDefaultDates();
            InitializeStatusComboBoxes(); // تهيئة ComboBox أولاً
            LoadSavedGroups(); // ثم تحميل المجموعات
            UIHelper.ShowEmptyMessage(listBoxAllEmployees, lbl_NoAllEmployees, "لا يوجد موظفين");
            UIHelper.ShowEmptyMessage(listBoxWorkEmployees, lbl_NoWorkEmployees, "لا يوجد موظفين للعمل");
            UIHelper.ShowEmptyMessage(listBoxVacationEmployees, lbl_NoVacationEmployees, "لا يوجد موظفين للإجازة");
            
            // إضافة زر إعادة تحميل المجموعات إذا كان موجوداً
            System.Diagnostics.Debug.WriteLine("تم تحميل نموذج إدارة المجموعات بنجاح");
        }

        private void UpdateNoDocumentsLabel()
        {
            UIHelper.ShowEmptyMessage(dataGridViewGroups, lbl_NoDocuments, "لا توجد بيانات");
        }
        
        public void RefreshGroups()
        {
            System.Diagnostics.Debug.WriteLine("=== إعادة تحميل المجموعات ===");
            LoadSavedGroups();
            UpdateNoDocumentsLabel();
        }
        private void LoadEmployees()
        {
            try
            {
                // استخدام الفلترة الجديدة
                var employees = EmployeeDepartmentHelper.GetFilteredEmployees(currentUser);

                // تحميل قائمة جميع الموظفين (بدون أرقام)
                listBoxAllEmployees.Items.Clear();
                foreach (DataRow row in employees.Rows)
                {
                    // التحقق من وجود العمود بالاسم العربي أو الإنجليزي
                    string employeeName = "";
                    if (employees.Columns.Contains("الاسم"))
                        employeeName = row["الاسم"].ToString();
                    else if (employees.Columns.Contains("Name"))
                        employeeName = row["Name"].ToString();

                    if (!string.IsNullOrEmpty(employeeName))
                        listBoxAllEmployees.Items.Add(employeeName);
                }
                UpdateNoDocumentsLabel();

                // تحديث عنوان النافذة
                string title = EmployeeDepartmentHelper.GetWindowTitle(currentUser, employees.Rows.Count);
                this.Text = title.Replace("نظام إدارة الموظفين", "فترات العمل الجماعية");

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetDefaultDates()
        {
            dateTimePickerStart.Value = DateTime.Today;
            dateTimePickerEnd.Value = DateTime.Today.AddDays(30);
        }

        private void InitializeStatusComboBoxes()
        {
            // ملاحظة: لا نحتاج لإزالة الأحداث لأننا لا نستخدمها
            
            // تعيين القيم الافتراضية
            cmbWorkStatus.SelectedIndex = 4; // "حضور"
            cmbVacationStatus.SelectedIndex = 6; // "إجازة"
            cmbStatus.SelectedIndex = 0; // "نشط" (للاحتفاظ بالتوافق مع الكود الموجود)
            
            // ملاحظة: تم إزالة أحداث ComboBox لأن العرض الآن يُظهر العدد الفعلي وليس المفلتر
        }
        


        private void btnAddToWork_Click(object sender, EventArgs e)
        {
            MoveSelectedEmployees(listBoxAllEmployees, listBoxWorkEmployees, selectedWorkEmployees);
        }

        private void btnRemoveFromWork_Click(object sender, EventArgs e)
        {
            MoveSelectedEmployees(listBoxWorkEmployees, listBoxAllEmployees, selectedWorkEmployees, true);
        }

        private void btnAddToVacation_Click(object sender, EventArgs e)
        {
            MoveSelectedEmployees(listBoxAllEmployees, listBoxVacationEmployees, selectedVacationEmployees);
        }

        private void btnRemoveFromVacation_Click(object sender, EventArgs e)
        {
            MoveSelectedEmployees(listBoxVacationEmployees, listBoxAllEmployees, selectedVacationEmployees, true);
        }

        private void MoveSelectedEmployees(ListBox source, ListBox destination, List<int> employeeList, bool isRemoving = false)
        {
            try
            {
                if (source.SelectedItems.Count == 0)
                {
                    MessageBox.Show("الرجاء اختيار موظف أو أكثر", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedItems = source.SelectedItems.Cast<string>().ToList();

                foreach (string employeeName in selectedItems)
                {
                    // البحث عن كود الموظف بالاسم
                    int employeeCode = GetEmployeeCodeByName(employeeName);

                    if (employeeCode > 0)
                    {
                        if (isRemoving)
                        {
                            employeeList.Remove(employeeCode);
                        }
                        else
                        {
                            if (!employeeList.Contains(employeeCode))
                            {
                                employeeList.Add(employeeCode);
                            }
                        }
                    }

                    // نقل العنصر
                    destination.Items.Add(employeeName);
                    source.Items.Remove(employeeName);
                }

                UpdateEmployeeCountLabels();
                UIHelper.ShowEmptyMessage(listBoxAllEmployees, lbl_NoAllEmployees, "لا يوجد موظفين");
                UIHelper.ShowEmptyMessage(listBoxWorkEmployees, lbl_NoWorkEmployees, "لا يوجد موظفين للعمل");
                UIHelper.ShowEmptyMessage(listBoxVacationEmployees, lbl_NoVacationEmployees, "لا يوجد موظفين للإجازة");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في نقل الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private int GetEmployeeCodeByName(string employeeName)
        {
            try
            {
                // استخدام الفلترة الجديدة
                var employees = EmployeeDepartmentHelper.GetFilteredEmployees(currentUser);
                foreach (DataRow row in employees.Rows)
                {
                    // التحقق من وجود العمود بالاسم العربي أو الإنجليزي
                    string nameColumn = "";
                    string codeColumn = "";

                    if (employees.Columns.Contains("الاسم"))
                    {
                        nameColumn = "الاسم";
                        codeColumn = "كود الموظف";
                    }
                    else if (employees.Columns.Contains("Name"))
                    {
                        nameColumn = "Name";
                        codeColumn = "EmployeeCode";
                    }

                    if (!string.IsNullOrEmpty(nameColumn) && row[nameColumn].ToString() == employeeName)
                    {
                        return Convert.ToInt32(row[codeColumn]);
                    }
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private void UpdateEmployeeCountLabels()
        {
            lblWorkCount.Text = $"موظفين في العمل: {listBoxWorkEmployees.Items.Count}";
            lblVacationCount.Text = $"موظفين في إجازة: {listBoxVacationEmployees.Items.Count}";
        }

        
        private async void btnCreatePeriods_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh();

            try
            {
                int successCount = 0;
                int errorCount = 0;

                // الحصول على القيم من UI قبل Task.Run
                string workStatus = cmbWorkStatus.SelectedItem?.ToString() ?? "حضور";
                string vacationStatus = cmbVacationStatus.SelectedItem?.ToString() ?? "إجازة";
                DateTime startDate = dateTimePickerStart.Value;
                DateTime endDate = dateTimePickerEnd.Value;
                
                // جمع أسماء الموظفين من الواجهة
                List<string> workEmployeeNames = new List<string>();
                List<string> vacationEmployeeNames = new List<string>();
                
                for (int i = 0; i < listBoxWorkEmployees.Items.Count; i++)
                {
                    workEmployeeNames.Add(listBoxWorkEmployees.Items[i].ToString());
                }
                
                for (int i = 0; i < listBoxVacationEmployees.Items.Count; i++)
                {
                    vacationEmployeeNames.Add(listBoxVacationEmployees.Items[i].ToString());
                }

                await Task.Run(() =>
                {
                    // إنشاء فترات العمل بترتيب الواجهة
                    for (int i = 0; i < workEmployeeNames.Count; i++)
                    {
                        string employeeName = workEmployeeNames[i];
                        int employeeCode = GetEmployeeCodeByName(employeeName);
                        if (employeeCode > 0)
                        {
                            int workPeriodId = CreateWorkPeriod(employeeCode, workStatus);
                            if (workPeriodId > 0)
                            {
                                DatabaseHelper.CreateDailyWorkStatusRecords(workPeriodId, employeeCode, employeeName,
                                    startDate, endDate, workStatus);
                                Interlocked.Increment(ref successCount);
                                System.Diagnostics.Debug.WriteLine($"تم إنشاء فترة عمل [{i + 1}]: {employeeName} - حالة: {workStatus}");
                            }
                            else
                            {
                                Interlocked.Increment(ref errorCount);
                            }
                        }
                    }

                    // إنشاء فترات الإجازة بترتيب الواجهة
                    for (int i = 0; i < vacationEmployeeNames.Count; i++)
                    {
                        string employeeName = vacationEmployeeNames[i];
                        int employeeCode = GetEmployeeCodeByName(employeeName);
                        if (employeeCode > 0)
                        {
                            int workPeriodId = CreateWorkPeriod(employeeCode, vacationStatus);
                            if (workPeriodId > 0)
                            {
                                DatabaseHelper.CreateDailyWorkStatusRecords(workPeriodId, employeeCode, employeeName,
                                    startDate, endDate, vacationStatus);
                                Interlocked.Increment(ref successCount);
                                System.Diagnostics.Debug.WriteLine($"تم إنشاء فترة إجازة [{i + 1}]: {employeeName} - حالة: {vacationStatus}");
                            }
                            else
                            {
                                Interlocked.Increment(ref errorCount);
                            }
                        }
                    }
                });

                loadingForm.Close(); // إغلاق النموذج بعد انتهاء المهمة

                string message = $"تم إنشاء {successCount} فترة عمل بنجاح";
                if (workEmployeeNames.Count > 0)
                    message += $"\n- {workEmployeeNames.Count} موظف عمل بحالة '{workStatus}'";
                if (vacationEmployeeNames.Count > 0)
                    message += $"\n- {vacationEmployeeNames.Count} موظف إجازة بحالة '{vacationStatus}'";
                if (errorCount > 0)
                    message += $"\nفشل في إنشاء {errorCount} فترة";

                MessageBox.Show(message, "نتيجة العملية",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                ToastHelper.CreatePeriodsToast();

                if (successCount > 0)
                    ClearForm();
                // تحديث ظهور الرسائل حسب محتوى القوائم
                UIHelper.ShowEmptyMessage(listBoxAllEmployees, lbl_NoAllEmployees, "لا يوجد موظفين");
                UIHelper.ShowEmptyMessage(listBoxWorkEmployees, lbl_NoWorkEmployees, "لا يوجد موظفين للعمل");
                UIHelper.ShowEmptyMessage(listBoxVacationEmployees, lbl_NoVacationEmployees, "لا يوجد موظفين للإجازة");

            }
            catch (Exception ex)
            {
                loadingForm.Close();
                MessageBox.Show($"خطأ في إنشاء فترات العمل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private int CreateWorkPeriod(int employeeCode, string status)
        {
            try
            {
                var employee = DatabaseHelper.GetEmployeeById(employeeCode);

                var workPeriod = new WorkPeriod
                {
                    EmployeeCode = employeeCode,
                    EmployeeName = employee.Name,
                    ProjectName = txtWorkPlace.Text,
                    Description = txtDescription.Text,
                    StartDate = dateTimePickerStart.Value,
                    EndDate = dateTimePickerEnd.Value,
                    WorkingDays = GetSelectedWorkingDays(),
                    DailyWorkingHours = (double)numericUpDownDailyHours.Value,
                    Status = status,
                    CreatedDate = DateTime.Now
                };

                return DatabaseHelper.AddWorkPeriod(workPeriod);
            }
            catch
            {
                return 0;
            }
        }

        private string GetSelectedWorkingDays()
        {
            var days = new List<string>();
            if (chkSunday.Checked) days.Add("الأحد");
            if (chkMonday.Checked) days.Add("الاثنين");
            if (chkTuesday.Checked) days.Add("الثلاثاء");
            if (chkWednesday.Checked) days.Add("الأربعاء");
            if (chkThursday.Checked) days.Add("الخميس");
            if (chkFriday.Checked) days.Add("الجمعة");
            if (chkSaturday.Checked) days.Add("السبت");
            return string.Join(",", days);
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtWorkPlace.Text))
            {
                MessageBox.Show("الرجاء إدخال مكان العمل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (selectedWorkEmployees.Count == 0 && selectedVacationEmployees.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار موظف واحد على الأقل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (dateTimePickerEnd.Value <= dateTimePickerStart.Value)
            {
                MessageBox.Show("تاريخ النهاية يجب أن يكون بعد تاريخ البداية", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (selectedWorkEmployees.Count > 0 && cmbWorkStatus.SelectedIndex == -1)
            {
                MessageBox.Show("الرجاء اختيار حالة للموظفين في العمل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (selectedVacationEmployees.Count > 0 && cmbVacationStatus.SelectedIndex == -1)
            {
                MessageBox.Show("الرجاء اختيار حالة للموظفين في الإجازة", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }


            if (!GetSelectedWorkingDays().Any())
            {
                MessageBox.Show("الرجاء اختيار يوم واحد على الأقل من أيام العمل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            txtWorkPlace.Clear();
            txtDescription.Clear();

            // إعادة جميع الموظفين للقائمة الأصلية
            listBoxAllEmployees.Items.Clear();
            listBoxWorkEmployees.Items.Clear();
            listBoxVacationEmployees.Items.Clear();

            selectedWorkEmployees.Clear();
            selectedVacationEmployees.Clear();

            LoadEmployees();
            SetDefaultDates();
            UpdateEmployeeCountLabels();
            // تحديث ظهور الرسائل حسب محتوى القوائم
            UIHelper.ShowEmptyMessage(listBoxAllEmployees, lbl_NoAllEmployees, "لا يوجد موظفين");
            UIHelper.ShowEmptyMessage(listBoxWorkEmployees, lbl_NoWorkEmployees, "لا يوجد موظفين للعمل");
            UIHelper.ShowEmptyMessage(listBoxVacationEmployees, lbl_NoVacationEmployees, "لا يوجد موظفين للإجازة");

        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        #region إدارة المجموعات المحفوظة

        private void LoadSavedGroups()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== بدء تحميل المجموعات المحفوظة ===");
                
                // الحصول على القيم المحددة من ComboBox
                string workStatus = cmbWorkStatus.SelectedItem?.ToString() ?? "حضور";
                string vacationStatus = cmbVacationStatus.SelectedItem?.ToString() ?? "إجازة";
                
                System.Diagnostics.Debug.WriteLine($"=== تحديث العدد بناءً على الحالات المختارة ===");
                System.Diagnostics.Debug.WriteLine($"حالة العمل المحددة: '{workStatus}' (Index: {cmbWorkStatus.SelectedIndex})");
                System.Diagnostics.Debug.WriteLine($"حالة الإجازة المحددة: '{vacationStatus}' (Index: {cmbVacationStatus.SelectedIndex})");
                
                var groups = DatabaseHelper.GetAllWorkGroupsWithRealCounts();
                
                System.Diagnostics.Debug.WriteLine($"تم الحصول على {groups.Rows.Count} مجموعة من قاعدة البيانات");
                
                // طباعة البيانات المحملة للتحقق
                foreach (DataRow row in groups.Rows)
                {
                    System.Diagnostics.Debug.WriteLine($"مجموعة: {row["اسم المجموعة"]} - موظفين العمل: {row["موظفين العمل"]} - موظفين الإجازة: {row["موظفين الإجازة"]}");
                }
                
                dataGridViewGroups.DataSource = groups;

                // تحديث الجدول
                dataGridViewGroups.Refresh();

                // تنسيق الأعمدة
                if (dataGridViewGroups.Columns.Count > 0)
                {
                    foreach (DataGridViewColumn column in dataGridViewGroups.Columns)
                    {
                        column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    }

                    // التأكد من عرض الأعمدة بشكل صحيح
                    if (dataGridViewGroups.Columns.Contains("موظفين العمل"))
                    {
                        dataGridViewGroups.Columns["موظفين العمل"].DefaultCellStyle.BackColor = Color.LightGreen;
                        dataGridViewGroups.Columns["موظفين العمل"].Width = 115;
                        dataGridViewGroups.Columns["موظفين العمل"].HeaderText = "عدد موظفي العمل";
                    }
                    if (dataGridViewGroups.Columns.Contains("موظفين الإجازة"))
                    {
                        dataGridViewGroups.Columns["موظفين الإجازة"].DefaultCellStyle.BackColor = Color.LightBlue;
                        dataGridViewGroups.Columns["موظفين الإجازة"].Width = 100;
                        dataGridViewGroups.Columns["موظفين الإجازة"].HeaderText = "عدد موظفي الإجازة";
                    }
                    if (dataGridViewGroups.Columns.Contains("اسم المجموعة"))
                    {
                        dataGridViewGroups.Columns["اسم المجموعة"].Width = 100;
                        dataGridViewGroups.Columns["اسم المجموعة"].HeaderText = "اسم المجموعة";
                    }
                    
                    // إخفاء الأعمدة غير المطلوبة
                    if (dataGridViewGroups.Columns.Contains("معرف المجموعة"))
                    {
                        dataGridViewGroups.Columns["معرف المجموعة"].Visible = false;
                    }
                    
                    // إخفاء أعمدة أخرى غير ضرورية
                    if (dataGridViewGroups.Columns.Contains("تاريخ الإنشاء"))
                    {
                        dataGridViewGroups.Columns["تاريخ الإنشاء"].Visible = false;
                    }
                    if (dataGridViewGroups.Columns.Contains("لم يبدأ"))
                    {
                        dataGridViewGroups.Columns["لم يبدأ"].Visible = false;
                    }
                    if (dataGridViewGroups.Columns.Contains("ملغى"))
                    {
                        dataGridViewGroups.Columns["ملغى"].Visible = false;
                    }
                    if (dataGridViewGroups.Columns.Contains("أنشأ بواسطة"))
                    {
                        dataGridViewGroups.Columns["أنشأ بواسطة"].Visible = false;
                    }
                    if (dataGridViewGroups.Columns.Contains("الوصف"))
                    {
                        dataGridViewGroups.Columns["الوصف"].Visible = false;
                    }
                }
                UIHelper.ShowEmptyMessage(dataGridViewGroups, lbl_NoDocuments, "لا توجد بيانات");
                System.Diagnostics.Debug.WriteLine("=== انتهاء تحميل المجموعات المحفوظة ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل المجموعات المحفوظة: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل المجموعات المحفوظة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnSaveGroup_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtGroupName.Text))
            {
                MessageBox.Show("الرجاء إدخال اسم المجموعة", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (selectedWorkEmployees.Count == 0 && selectedVacationEmployees.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار موظف واحد على الأقل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh();

            try
            {


                int groupId = 0;
                
                // الحصول على القيم من UI قبل Task.Run
                string groupName = txtGroupName.Text;
                string description = txtDescription.Text;
                string workStatus = cmbWorkStatus.SelectedItem?.ToString() ?? "عمل";
                string vacationStatus = cmbVacationStatus.SelectedItem?.ToString() ?? "إجازة";
                
                // جمع أسماء الموظفين من الواجهة
                List<string> workEmployeeNames = new List<string>();
                List<string> vacationEmployeeNames = new List<string>();
                
                for (int i = 0; i < listBoxWorkEmployees.Items.Count; i++)
                {
                    workEmployeeNames.Add(listBoxWorkEmployees.Items[i].ToString());
                }
                
                for (int i = 0; i < listBoxVacationEmployees.Items.Count; i++)
                {
                    vacationEmployeeNames.Add(listBoxVacationEmployees.Items[i].ToString());
                }

                await Task.Run(() =>
                {
                    // إنشاء المجموعة
                    groupId = DatabaseHelper.CreateWorkGroup(groupName, description, "النظام");

                    // إضافة موظفي العمل بترتيب الواجهة والحالة المحددة
                    for (int i = 0; i < workEmployeeNames.Count; i++)
                    {
                        string employeeName = workEmployeeNames[i];
                        int employeeCode = GetEmployeeCodeByName(employeeName);
                        if (employeeCode > 0)
                        {
                            var employee = DatabaseHelper.GetEmployeeById(employeeCode);
                            string keyCardNumber = employee?.KeyCardNumber ?? "";
                            DatabaseHelper.AddMemberToWorkGroup(groupId, employeeCode, employeeName, workStatus, keyCardNumber);
                            System.Diagnostics.Debug.WriteLine($"تم إضافة موظف عمل [{i + 1}]: {employeeName} - حالة: {workStatus} - بطاقة: {keyCardNumber}");
                        }
                    }

                    // إضافة موظفي الإجازة بترتيب الواجهة والحالة المحددة
                    for (int i = 0; i < vacationEmployeeNames.Count; i++)
                    {
                        string employeeName = vacationEmployeeNames[i];
                        int employeeCode = GetEmployeeCodeByName(employeeName);
                        if (employeeCode > 0)
                        {
                            var employee = DatabaseHelper.GetEmployeeById(employeeCode);
                            string keyCardNumber = employee?.KeyCardNumber ?? "";
                            DatabaseHelper.AddMemberToWorkGroup(groupId, employeeCode, employeeName, vacationStatus, keyCardNumber);
                            System.Diagnostics.Debug.WriteLine($"تم إضافة موظف إجازة [{i + 1}]: {employeeName} - حالة: {vacationStatus} - بطاقة: {keyCardNumber}");
                        }
                    }
                    
                    System.Diagnostics.Debug.WriteLine($"تم إضافة {workEmployeeNames.Count} موظف عمل و {vacationEmployeeNames.Count} موظف إجازة للمجموعة {groupId}");
                });

                loadingForm.Close();

                MessageBox.Show("تم حفظ المجموعة بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);



                // تحديث قائمة المجموعات مع تأخير قصير للتأكد من كتابة البيانات
                await Task.Delay(500);
                LoadSavedGroups();
                UpdateNoDocumentsLabel();
                txtGroupName.Clear();
                txtDescription.Clear();
            }
            catch (Exception ex)
            {
                loadingForm.Close();
                MessageBox.Show($"خطأ في حفظ المجموعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnLoadGroup_Click(object sender, EventArgs e)
        {
            try
            {
                if (selectedGroupId == 0)
                {
                    MessageBox.Show("الرجاء اختيار مجموعة للتحميل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"بدء تحميل المجموعة: {selectedGroupId}");

                // مسح التحديد الحالي
                ClearForm();

                // تحميل أعضاء المجموعة
                DatabaseHelper.LoadWorkGroupToForm(selectedGroupId, out List<int> workEmployees, out List<int> vacationEmployees);

                System.Diagnostics.Debug.WriteLine($"تم تحميل {workEmployees.Count} موظف عمل و {vacationEmployees.Count} موظف إجازة من قاعدة البيانات");

                // نقل الموظفين للقوائم المناسبة
                var allEmployees = DatabaseHelper.GetAllEmployees();

                foreach (int employeeCode in workEmployees)
                {
                    var employeeRow = allEmployees.AsEnumerable()
                        .FirstOrDefault(row =>
                        {
                            try
                            {
                                // التعامل مع أنواع البيانات المختلفة
                                var codeValue = row["كود الموظف"];
                                int code = Convert.ToInt32(codeValue);
                                return code == employeeCode;
                            }
                            catch
                            {
                                return false;
                            }
                        });

                    if (employeeRow != null)
                    {
                        string employeeInfo = $"{employeeRow["الاسم"]}";

                        // إزالة من القائمة الأصلية وإضافة لقائمة العمل
                        if (listBoxAllEmployees.Items.Contains(employeeInfo))
                        {
                            listBoxAllEmployees.Items.Remove(employeeInfo);
                            listBoxWorkEmployees.Items.Add(employeeInfo);
                            selectedWorkEmployees.Add(employeeCode);
                        }
                    }
                }

                foreach (int employeeCode in vacationEmployees)
                {
                    var employeeRow = allEmployees.AsEnumerable()
                        .FirstOrDefault(row =>
                        {
                            try
                            {
                                // التعامل مع أنواع البيانات المختلفة
                                var codeValue = row["كود الموظف"];
                                int code = Convert.ToInt32(codeValue);
                                return code == employeeCode;
                            }
                            catch
                            {
                                return false;
                            }
                        });

                    if (employeeRow != null)
                    {
                        string employeeInfo = $"{employeeRow["الاسم"]}";

                        // إزالة من القائمة الأصلية وإضافة لقائمة الإجازة
                        if (listBoxAllEmployees.Items.Contains(employeeInfo))
                        {
                            listBoxAllEmployees.Items.Remove(employeeInfo);
                            listBoxVacationEmployees.Items.Add(employeeInfo);
                            selectedVacationEmployees.Add(employeeCode);
                        }
                    }
                }

                UpdateEmployeeCountLabels();
                // تحديث ظهور الرسائل حسب محتوى القوائم
                UIHelper.ShowEmptyMessage(listBoxAllEmployees, lbl_NoAllEmployees, "لا يوجد موظفين");
                UIHelper.ShowEmptyMessage(listBoxWorkEmployees, lbl_NoWorkEmployees, "لا يوجد موظفين للعمل");
                UIHelper.ShowEmptyMessage(listBoxVacationEmployees, lbl_NoVacationEmployees, "لا يوجد موظفين للإجازة");

                MessageBox.Show("تم تحميل المجموعة بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المجموعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        
        private async void btnDeleteGroup_Click(object sender, EventArgs e)
        {
            if (selectedGroupId == 0)
            {
                MessageBox.Show("الرجاء اختيار مجموعة للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف المجموعة المحددة؟", "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result != DialogResult.Yes)
                return;

            LoadingForm loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh();
            await Task.Delay(100);
            try
            {
                await Task.Run(() =>
                {
                    DatabaseHelper.DeleteWorkGroup(selectedGroupId);
                });

                loadingForm.Close();

                MessageBox.Show("تم حذف المجموعة بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // تحديث قائمة المجموعات مع تأخير قصير للتأكد من حذف البيانات
                await Task.Delay(500);
                LoadSavedGroups();
                UpdateNoDocumentsLabel();
                selectedGroupId = 0;
            }
            catch (Exception ex)
            {
                loadingForm.Close();
                MessageBox.Show($"خطأ في حذف المجموعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void dataGridViewGroups_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewGroups.SelectedRows.Count > 0)
                {
                    var selectedRow = dataGridViewGroups.SelectedRows[0];
                    if (selectedRow.Cells["معرف المجموعة"] != null && selectedRow.Cells["معرف المجموعة"].Value != null)
                    {
                        // التعامل مع أنواع البيانات المختلفة (Int32/Int64)
                        var groupIdValue = selectedRow.Cells["معرف المجموعة"].Value;
                        selectedGroupId = Convert.ToInt32(groupIdValue);

                        // عرض اسم المجموعة في حقل النص
                        if (selectedRow.Cells["اسم المجموعة"] != null)
                        {
                            txtGroupName.Text = selectedRow.Cells["اسم المجموعة"].Value?.ToString() ?? "";
                        }

                        System.Diagnostics.Debug.WriteLine($"تم تحديد المجموعة: {selectedGroupId}");
                    }
                }
                else
                {
                    selectedGroupId = 0;
                    System.Diagnostics.Debug.WriteLine("تم إلغاء تحديد المجموعة");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديد المجموعة: {ex.Message}");
                selectedGroupId = 0;
            }
        }

        #endregion



        private async void btnUpdateGroup_Click(object sender, EventArgs e)
        {
            if (selectedGroupId == 0)
            {
                MessageBox.Show("الرجاء اختيار مجموعة لتعديلها", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(txtGroupName.Text))
            {
                MessageBox.Show("الرجاء إدخال اسم المجموعة", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (selectedWorkEmployees.Count == 0 && selectedVacationEmployees.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار موظف واحد على الأقل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh();

            try
            {
                // الحصول على القيم من UI قبل Task.Run
                string groupName = txtGroupName.Text;
                string description = txtDescription.Text;
                string workStatus = cmbWorkStatus.SelectedItem?.ToString() ?? "عمل";
                string vacationStatus = cmbVacationStatus.SelectedItem?.ToString() ?? "إجازة";
                
                // جمع أسماء الموظفين من الواجهة
                List<string> workEmployeeNames = new List<string>();
                List<string> vacationEmployeeNames = new List<string>();
                
                for (int i = 0; i < listBoxWorkEmployees.Items.Count; i++)
                {
                    workEmployeeNames.Add(listBoxWorkEmployees.Items[i].ToString());
                }
                
                for (int i = 0; i < listBoxVacationEmployees.Items.Count; i++)
                {
                    vacationEmployeeNames.Add(listBoxVacationEmployees.Items[i].ToString());
                }

                await Task.Run(() =>
                {
                    // تحديث اسم ووصف المجموعة
                    DatabaseHelper.UpdateWorkGroup(selectedGroupId, groupName, description);

                    // حذف الأعضاء القدامى
                    DatabaseHelper.DeleteGroupMembers(selectedGroupId);

                    // إعادة إضافة موظفي العمل بترتيب الواجهة والحالة المحددة
                    for (int i = 0; i < workEmployeeNames.Count; i++)
                    {
                        string employeeName = workEmployeeNames[i];
                        int employeeCode = GetEmployeeCodeByName(employeeName);
                        if (employeeCode > 0)
                        {
                            var employee = DatabaseHelper.GetEmployeeById(employeeCode);
                            string keyCardNumber = employee?.KeyCardNumber ?? "";
                            DatabaseHelper.AddMemberToWorkGroup(selectedGroupId, employeeCode, employeeName, workStatus, keyCardNumber);
                        }
                    }

                    // إعادة إضافة موظفي الإجازة بترتيب الواجهة والحالة المحددة
                    for (int i = 0; i < vacationEmployeeNames.Count; i++)
                    {
                        string employeeName = vacationEmployeeNames[i];
                        int employeeCode = GetEmployeeCodeByName(employeeName);
                        if (employeeCode > 0)
                        {
                            var employee = DatabaseHelper.GetEmployeeById(employeeCode);
                            string keyCardNumber = employee?.KeyCardNumber ?? "";
                            DatabaseHelper.AddMemberToWorkGroup(selectedGroupId, employeeCode, employeeName, vacationStatus, keyCardNumber);
                        }
                    }
                });

                loadingForm.Close();

                MessageBox.Show("تم تحديث المجموعة بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // تحديث قائمة المجموعات مع تأخير قصير للتأكد من تحديث البيانات
                await Task.Delay(500);
                LoadSavedGroups();
                UpdateNoDocumentsLabel();
            }
            catch (Exception ex)
            {
                loadingForm.Close();
                MessageBox.Show($"خطأ في تعديل المجموعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void dataGridViewGroups_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            // منع ظهور رسالة الخطأ الافتراضية
            e.Cancel = true;

            // تسجيل الخطأ للتشخيص
            System.Diagnostics.Debug.WriteLine($"DataGridView Error: {e.Exception?.Message} at Row: {e.RowIndex}, Column: {e.ColumnIndex}");

            // يمكن إضافة معالجة خاصة هنا إذا لزم الأمر
        }

    }
}
