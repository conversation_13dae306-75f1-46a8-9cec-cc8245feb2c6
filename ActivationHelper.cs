﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EmployeeManagementSystem
{
    public static class ActivationHelper
    {
        public static bool CheckActivation()
        {
            if (!Properties.Settings.Default.IsActivated)
            {
                MessageBox.Show(
                    "أنت حالياً تستخدم النسخة التجريبية من البرنامج\n\n" +
                    "المميزات المتاحة:\n" +
                    "- يمكنك استخدام جميع خصائص النظام\n" +
                    "- مسموح بإضافة حتى 5 ملفات فقط\n\n" +
                    "للاستمرار بدون قيود:\n" +
                    "1. انتقل إلى نافذة التفعيل (قائمة الإعدادات)\n" +
                    "2. أدخل رمز التفعيل الخاص بك\n" +
                    "3. أو اتصل بفريق الدعم لمساعدتك\n\n" +
                    "شكراً لاختيارك منتجاتنا",
                    "النسخة التجريبية - ميزات مقيدة",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information
                );
                return false;
            }

            return true;
        }
    }

}
