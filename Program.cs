using System;
using System.Data.SqlClient;
using System.Linq;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    internal static class Program
    {
       
        [STAThread]
        static void Main()
        {
            
           
            ApplicationConfiguration.Initialize();
            
            Application.Run(new StartForm());
            // إذا تم الاتصال بنجاح، أكمل التشغيل
            if (!DatabaseHelper.HasUsers())
            {
                if (!ShowFirstUserForm())
                    return;
            }

          
        }

        private static bool ShowFirstUserForm()
        {
            var firstUserForm = new UserForm()
            {
                Text = "إضافة المستخدم الأول للنظام",
                StartPosition = FormStartPosition.CenterScreen
            };

            // إخفاء الأزرار غير الضرورية والـ DataGridView
            foreach (Control ctrl in firstUserForm.Controls)
            {
                if (ctrl is GroupBox groupBox)
                {
                    foreach (Control c in groupBox.Controls)
                    {
                        if (c is Button btn && btn.Text != "إضافة" && btn.Text != "مسح الحقول")
                        {
                            btn.Visible = false;
                        }
                    }
                }
                else if (ctrl is Button btn)
                {
                    if (btn.Text != "إضافة" && btn.Text != "مسح الحقول")
                        btn.Visible = false;
                }
                else if (ctrl is DataGridView)
                {
                    ctrl.Visible = false;
                }
            }

            // ضبط نوع المستخدم "مدير" وتعطيل التغيير (إذا موجود)
            var cmbUserType = firstUserForm.Controls.Find("cmbUserType", true).FirstOrDefault() as ComboBox;
            if (cmbUserType != null)
            {
                cmbUserType.SelectedIndex = cmbUserType.Items.IndexOf("مدير");
                cmbUserType.Enabled = false;
            }

            // منع إغلاق النموذج إذا لم يُضف مستخدم بعد
            firstUserForm.FormClosing += (s, e) =>
            {
                if (!DatabaseHelper.HasUsers() && e.CloseReason == CloseReason.UserClosing)
                {
                    e.Cancel = true;
                    MessageBox.Show("يجب إضافة مستخدم مدير أولاً قبل إغلاق النموذج.", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            };

            // عرض النموذج
            var result = firstUserForm.ShowDialog();

            // إعادة التحقق هل تم إضافة مستخدم
            return result == DialogResult.OK && DatabaseHelper.HasUsers();
        }
    }
}
