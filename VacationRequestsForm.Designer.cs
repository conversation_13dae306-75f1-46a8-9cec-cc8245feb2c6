namespace EmployeeManagementSystem
{
    partial class VacationRequestsForm
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            dataGridViewRequests = new DataGridView();
            lblTitle = new Label();
            lblRequestsCount = new Label();
            groupBoxDetails = new GroupBox();
            lblEmployeeName = new Label();
            lblVacationType = new Label();
            lblStartDate = new Label();
            lblEndDate = new Label();
            lblDaysCount = new Label();
            lblReason = new Label();
            btnApprove = new Button();
            btnReject = new Button();
            btnRefresh = new Button();
            btnClose = new Button();
            btnPrintRequest = new Button();
            toolTip1 = new ToolTip(components);
            ((System.ComponentModel.ISupportInitialize)dataGridViewRequests).BeginInit();
            groupBoxDetails.SuspendLayout();
            SuspendLayout();
            // 
            // dataGridViewRequests
            // 
            dataGridViewRequests.AllowUserToAddRows = false;
            dataGridViewRequests.AllowUserToDeleteRows = false;
            dataGridViewRequests.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dataGridViewRequests.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = SystemColors.Control;
            dataGridViewCellStyle1.Font = new Font("Cairo", 12F, FontStyle.Bold);
            dataGridViewCellStyle1.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = DataGridViewTriState.True;
            dataGridViewRequests.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            dataGridViewRequests.ColumnHeadersHeight = 35;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = SystemColors.Window;
            dataGridViewCellStyle2.Font = new Font("Cairo", 12F);
            dataGridViewCellStyle2.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.False;
            dataGridViewRequests.DefaultCellStyle = dataGridViewCellStyle2;
            dataGridViewRequests.Location = new Point(12, 80);
            dataGridViewRequests.MultiSelect = false;
            dataGridViewRequests.Name = "dataGridViewRequests";
            dataGridViewRequests.ReadOnly = true;
            dataGridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle3.BackColor = SystemColors.Control;
            dataGridViewCellStyle3.Font = new Font("Cairo", 12F);
            dataGridViewCellStyle3.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle3.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle3.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = DataGridViewTriState.True;
            dataGridViewRequests.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            dataGridViewRequests.RowHeadersWidth = 51;
            dataGridViewRequests.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridViewRequests.Size = new Size(960, 300);
            dataGridViewRequests.TabIndex = 0;
            toolTip1.SetToolTip(dataGridViewRequests, "اضغط على أي طلب لعرض تفاصيله");
            dataGridViewRequests.CellClick += dataGridViewRequests_CellClick;
            // 
            // lblTitle
            // 
            lblTitle.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            lblTitle.Font = new Font("Cairo", 16F, FontStyle.Bold);
            lblTitle.ForeColor = Color.FromArgb(45, 103, 138);
            lblTitle.Location = new Point(12, 9);
            lblTitle.Name = "lblTitle";
            lblTitle.Size = new Size(960, 35);
            lblTitle.TabIndex = 1;
            lblTitle.Text = "طلبات الإجازات المعلقة";
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblRequestsCount
            // 
            lblRequestsCount.Font = new Font("Cairo", 12F);
            lblRequestsCount.Location = new Point(12, 50);
            lblRequestsCount.Name = "lblRequestsCount";
            lblRequestsCount.Size = new Size(300, 25);
            lblRequestsCount.TabIndex = 2;
            lblRequestsCount.Text = "عدد الطلبات المعلقة: 0";
            // 
            // groupBoxDetails
            // 
            groupBoxDetails.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxDetails.Controls.Add(lblEmployeeName);
            groupBoxDetails.Controls.Add(lblVacationType);
            groupBoxDetails.Controls.Add(lblStartDate);
            groupBoxDetails.Controls.Add(lblEndDate);
            groupBoxDetails.Controls.Add(lblDaysCount);
            groupBoxDetails.Controls.Add(lblReason);
            groupBoxDetails.Font = new Font("Cairo", 12F, FontStyle.Bold);
            groupBoxDetails.Location = new Point(12, 390);
            groupBoxDetails.Name = "groupBoxDetails";
            groupBoxDetails.Size = new Size(960, 120);
            groupBoxDetails.TabIndex = 3;
            groupBoxDetails.TabStop = false;
            groupBoxDetails.Text = "تفاصيل الطلب المحدد";
            // 
            // lblEmployeeName
            // 
            lblEmployeeName.Font = new Font("Cairo", 11F);
            lblEmployeeName.Location = new Point(20, 30);
            lblEmployeeName.Name = "lblEmployeeName";
            lblEmployeeName.Size = new Size(300, 25);
            lblEmployeeName.TabIndex = 0;
            lblEmployeeName.Text = "الموظف: -";
            // 
            // lblVacationType
            // 
            lblVacationType.Font = new Font("Cairo", 11F);
            lblVacationType.Location = new Point(340, 30);
            lblVacationType.Name = "lblVacationType";
            lblVacationType.Size = new Size(200, 25);
            lblVacationType.TabIndex = 1;
            lblVacationType.Text = "نوع الإجازة: -";
            // 
            // lblStartDate
            // 
            lblStartDate.Font = new Font("Cairo", 11F);
            lblStartDate.Location = new Point(560, 30);
            lblStartDate.Name = "lblStartDate";
            lblStartDate.Size = new Size(150, 25);
            lblStartDate.TabIndex = 2;
            lblStartDate.Text = "من: -";
            // 
            // lblEndDate
            // 
            lblEndDate.Font = new Font("Cairo", 11F);
            lblEndDate.Location = new Point(730, 30);
            lblEndDate.Name = "lblEndDate";
            lblEndDate.Size = new Size(150, 25);
            lblEndDate.TabIndex = 3;
            lblEndDate.Text = "إلى: -";
            // 
            // lblDaysCount
            // 
            lblDaysCount.Font = new Font("Cairo", 11F);
            lblDaysCount.Location = new Point(20, 65);
            lblDaysCount.Name = "lblDaysCount";
            lblDaysCount.Size = new Size(150, 25);
            lblDaysCount.TabIndex = 4;
            lblDaysCount.Text = "عدد الأيام: -";
            // 
            // lblReason
            // 
            lblReason.Font = new Font("Cairo", 11F);
            lblReason.Location = new Point(190, 65);
            lblReason.Name = "lblReason";
            lblReason.Size = new Size(750, 25);
            lblReason.TabIndex = 5;
            lblReason.Text = "السبب: -";
            // 
            // btnApprove
            // 
            btnApprove.Anchor = AnchorStyles.Bottom;
            btnApprove.BackColor = Color.FromArgb(40, 167, 69);
            btnApprove.Enabled = false;
            btnApprove.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnApprove.ForeColor = Color.White;
            btnApprove.Image = Properties.Resources.ok_32px;
            btnApprove.ImageAlign = ContentAlignment.MiddleRight;
            btnApprove.Location = new Point(226, 524);
            btnApprove.Name = "btnApprove";
            btnApprove.Size = new Size(104, 45);
            btnApprove.TabIndex = 4;
            btnApprove.Text = "موافقة";
            btnApprove.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnApprove, "الموافقة على الطلب المحدد");
            btnApprove.UseVisualStyleBackColor = false;
            btnApprove.Click += btnApprove_Click;
            // 
            // btnReject
            // 
            btnReject.Anchor = AnchorStyles.Bottom;
            btnReject.BackColor = Color.FromArgb(220, 53, 69);
            btnReject.Enabled = false;
            btnReject.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnReject.ForeColor = Color.White;
            btnReject.Image = Properties.Resources.delete_32px;
            btnReject.ImageAlign = ContentAlignment.MiddleRight;
            btnReject.Location = new Point(336, 524);
            btnReject.Name = "btnReject";
            btnReject.Size = new Size(100, 45);
            btnReject.TabIndex = 5;
            btnReject.Text = "رفض";
            btnReject.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnReject, "رفض الطلب المحدد");
            btnReject.UseVisualStyleBackColor = false;
            btnReject.Click += btnReject_Click;
            // 
            // btnRefresh
            // 
            btnRefresh.Anchor = AnchorStyles.Bottom;
            btnRefresh.BackColor = Color.FromArgb(0, 123, 255);
            btnRefresh.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnRefresh.ForeColor = Color.White;
            btnRefresh.Image = Properties.Resources.update_32px;
            btnRefresh.ImageAlign = ContentAlignment.MiddleRight;
            btnRefresh.Location = new Point(442, 524);
            btnRefresh.Name = "btnRefresh";
            btnRefresh.Size = new Size(100, 45);
            btnRefresh.TabIndex = 6;
            btnRefresh.Text = "تحديث";
            btnRefresh.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnRefresh, "تحديث قائمة الطلبات");
            btnRefresh.UseVisualStyleBackColor = false;
            btnRefresh.Click += btnRefresh_Click;
            // 
            // btnClose
            // 
            btnClose.Anchor = AnchorStyles.Bottom;
            btnClose.BackColor = Color.FromArgb(108, 117, 125);
            btnClose.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnClose.ForeColor = Color.White;
            btnClose.Image = Properties.Resources.delete_32px;
            btnClose.ImageAlign = ContentAlignment.MiddleRight;
            btnClose.Location = new Point(658, 524);
            btnClose.Name = "btnClose";
            btnClose.Size = new Size(100, 45);
            btnClose.TabIndex = 7;
            btnClose.Text = "إغلاق";
            btnClose.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnClose, "إغلاق النموذج");
            btnClose.UseVisualStyleBackColor = false;
            btnClose.Click += btnClose_Click;
            // 
            // btnPrintRequest
            // 
            btnPrintRequest.Anchor = AnchorStyles.Bottom;
            btnPrintRequest.BackColor = Color.FromArgb(255, 193, 7);
            btnPrintRequest.BackgroundImageLayout = ImageLayout.Center;
            btnPrintRequest.Enabled = false;
            btnPrintRequest.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnPrintRequest.ForeColor = Color.Black;
            btnPrintRequest.Image = Properties.Resources.print_32px;
            btnPrintRequest.ImageAlign = ContentAlignment.MiddleRight;
            btnPrintRequest.Location = new Point(548, 524);
            btnPrintRequest.Name = "btnPrintRequest";
            btnPrintRequest.Size = new Size(104, 45);
            btnPrintRequest.TabIndex = 8;
            btnPrintRequest.Text = "طباعة";
            btnPrintRequest.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnPrintRequest, "طباعة تفاصيل الطلب المحدد");
            btnPrintRequest.UseVisualStyleBackColor = false;
            btnPrintRequest.Click += btnPrintRequest_Click;
            // 
            // VacationRequestsForm
            // 
            AutoScaleDimensions = new SizeF(8F, 30F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(984, 581);
            Controls.Add(btnPrintRequest);
            Controls.Add(btnClose);
            Controls.Add(btnRefresh);
            Controls.Add(btnReject);
            Controls.Add(btnApprove);
            Controls.Add(groupBoxDetails);
            Controls.Add(lblRequestsCount);
            Controls.Add(lblTitle);
            Controls.Add(dataGridViewRequests);
            Font = new Font("Cairo", 12F);
            MinimumSize = new Size(1000, 620);
            Name = "VacationRequestsForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterParent;
            Text = "طلبات الإجازات";
            WindowState = FormWindowState.Maximized;
            ((System.ComponentModel.ISupportInitialize)dataGridViewRequests).EndInit();
            groupBoxDetails.ResumeLayout(false);
            ResumeLayout(false);
        }

        private DataGridView dataGridViewRequests;
        private Label lblTitle;
        private Label lblRequestsCount;
        private GroupBox groupBoxDetails;
        private Label lblEmployeeName;
        private Label lblVacationType;
        private Label lblStartDate;
        private Label lblEndDate;
        private Label lblDaysCount;
        private Label lblReason;
        private Button btnApprove;
        private Button btnReject;
        private Button btnRefresh;
        private Button btnClose;
        private Button btnPrintRequest;
        private ToolTip toolTip1;
    }
}