namespace EmployeeManagementSystem
{
    partial class CourseEvaluationForm
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            groupBox1 = new GroupBox();
            dtEvaluationDate = new DateTimePicker();
            lblEvaluationDate = new Label();
            txtComments = new TextBox();
            lblComments = new Label();
            numRating = new NumericUpDown();
            lblRating = new Label();
            cmbEmployeeName = new ComboBox();
            lblEmployeeName = new Label();
            cmbCourse = new ComboBox();
            lblCourse = new Label();
            btnExportExcel = new Button();
            btnClear = new Button();
            btnDelete = new Button();
            btnUpdate = new Button();
            btnAdd = new Button();
            dataGridView1 = new DataGridView();
            toolTip1 = new ToolTip(components);
            groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numRating).BeginInit();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).BeginInit();
            SuspendLayout();
            // 
            // groupBox1
            // 
            groupBox1.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBox1.Controls.Add(dtEvaluationDate);
            groupBox1.Controls.Add(lblEvaluationDate);
            groupBox1.Controls.Add(txtComments);
            groupBox1.Controls.Add(lblComments);
            groupBox1.Controls.Add(numRating);
            groupBox1.Controls.Add(lblRating);
            groupBox1.Controls.Add(cmbEmployeeName);
            groupBox1.Controls.Add(lblEmployeeName);
            groupBox1.Controls.Add(cmbCourse);
            groupBox1.Controls.Add(lblCourse);
            groupBox1.Controls.Add(btnExportExcel);
            groupBox1.Controls.Add(btnClear);
            groupBox1.Controls.Add(btnDelete);
            groupBox1.Controls.Add(btnUpdate);
            groupBox1.Controls.Add(btnAdd);
            groupBox1.Font = new Font("Cairo", 12F, FontStyle.Bold);
            groupBox1.Location = new Point(12, 12);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(1000, 300);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            groupBox1.Text = "بيانات تقييم الدورة";
            // 
            // dtEvaluationDate
            // 
            dtEvaluationDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            dtEvaluationDate.Font = new Font("Cairo", 12F);
            dtEvaluationDate.Format = DateTimePickerFormat.Short;
            dtEvaluationDate.Location = new Point(20, 80);
            dtEvaluationDate.Name = "dtEvaluationDate";
            dtEvaluationDate.Size = new Size(300, 37);
            dtEvaluationDate.TabIndex = 4;
            // 
            // lblEvaluationDate
            // 
            lblEvaluationDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblEvaluationDate.AutoSize = true;
            lblEvaluationDate.Location = new Point(326, 83);
            lblEvaluationDate.Name = "lblEvaluationDate";
            lblEvaluationDate.Size = new Size(105, 30);
            lblEvaluationDate.TabIndex = 13;
            lblEvaluationDate.Text = "تاريخ التقييم:";
            // 
            // txtComments
            // 
            txtComments.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtComments.Font = new Font("Cairo", 12F);
            txtComments.Location = new Point(20, 120);
            txtComments.Multiline = true;
            txtComments.Name = "txtComments";
            txtComments.RightToLeft = RightToLeft.Yes;
            txtComments.Size = new Size(860, 80);
            txtComments.TabIndex = 3;
            // 
            // lblComments
            // 
            lblComments.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblComments.AutoSize = true;
            lblComments.Location = new Point(886, 123);
            lblComments.Name = "lblComments";
            lblComments.Size = new Size(86, 30);
            lblComments.TabIndex = 11;
            lblComments.Text = "التعليقات:";
            // 
            // numRating
            // 
            numRating.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            numRating.Font = new Font("Cairo", 12F);
            numRating.Location = new Point(580, 80);
            numRating.Maximum = new decimal(new int[] { 10, 0, 0, 0 });
            numRating.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numRating.Name = "numRating";
            numRating.Size = new Size(300, 37);
            numRating.TabIndex = 2;
            numRating.Value = new decimal(new int[] { 1, 0, 0, 0 });
            // 
            // lblRating
            // 
            lblRating.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblRating.AutoSize = true;
            lblRating.Location = new Point(886, 83);
            lblRating.Name = "lblRating";
            lblRating.Size = new Size(117, 30);
            lblRating.TabIndex = 9;
            lblRating.Text = "التقييم (1-10):";
            // 
            // cmbEmployeeName
            // 
            cmbEmployeeName.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbEmployeeName.Font = new Font("Cairo", 12F);
            cmbEmployeeName.FormattingEnabled = true;
            cmbEmployeeName.Location = new Point(20, 40);
            cmbEmployeeName.Name = "cmbEmployeeName";
            cmbEmployeeName.RightToLeft = RightToLeft.No;
            cmbEmployeeName.Size = new Size(400, 38);
            cmbEmployeeName.TabIndex = 1;
            // 
            // lblEmployeeName
            // 
            lblEmployeeName.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblEmployeeName.AutoSize = true;
            lblEmployeeName.Location = new Point(426, 43);
            lblEmployeeName.Name = "lblEmployeeName";
            lblEmployeeName.Size = new Size(113, 30);
            lblEmployeeName.TabIndex = 7;
            lblEmployeeName.Text = "اسم الموظف:";
            // 
            // cmbCourse
            // 
            cmbCourse.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbCourse.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbCourse.Font = new Font("Cairo", 12F);
            cmbCourse.FormattingEnabled = true;
            cmbCourse.Location = new Point(580, 40);
            cmbCourse.Name = "cmbCourse";
            cmbCourse.RightToLeft = RightToLeft.No;
            cmbCourse.Size = new Size(300, 38);
            cmbCourse.TabIndex = 0;
            // 
            // lblCourse
            // 
            lblCourse.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblCourse.AutoSize = true;
            lblCourse.Location = new Point(886, 43);
            lblCourse.Name = "lblCourse";
            lblCourse.Size = new Size(61, 30);
            lblCourse.TabIndex = 5;
            lblCourse.Text = "الدورة:";
            // 
            // btnExportExcel
            // 
            btnExportExcel.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnExportExcel.BackColor = Color.Transparent;
            btnExportExcel.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnExportExcel.Image = Properties.Resources.microsoft_excel_2019_32px;
            btnExportExcel.ImageAlign = ContentAlignment.MiddleRight;
            btnExportExcel.Location = new Point(243, 233);
            btnExportExcel.Name = "btnExportExcel";
            btnExportExcel.Size = new Size(108, 46);
            btnExportExcel.TabIndex = 8;
            btnExportExcel.Text = "تصدير";
            btnExportExcel.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnExportExcel, "تصدير إلى Excel");
            btnExportExcel.UseVisualStyleBackColor = false;
            btnExportExcel.Click += btnExportExcel_Click;
            // 
            // btnClear
            // 
            btnClear.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnClear.BackColor = Color.Transparent;
            btnClear.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnClear.Image = Properties.Resources.clear_formatting_32px;
            btnClear.ImageAlign = ContentAlignment.MiddleRight;
            btnClear.Location = new Point(357, 233);
            btnClear.Name = "btnClear";
            btnClear.Size = new Size(90, 46);
            btnClear.TabIndex = 7;
            btnClear.Text = "إفراغ";
            btnClear.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnClear, "إفراغ الحقول");
            btnClear.UseVisualStyleBackColor = false;
            btnClear.Click += btnClear_Click;
            // 
            // btnDelete
            // 
            btnDelete.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnDelete.BackColor = Color.Transparent;
            btnDelete.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnDelete.Image = Properties.Resources.remove_32px;
            btnDelete.ImageAlign = ContentAlignment.MiddleRight;
            btnDelete.Location = new Point(453, 233);
            btnDelete.Name = "btnDelete";
            btnDelete.Size = new Size(90, 46);
            btnDelete.TabIndex = 9;
            btnDelete.Text = "حذف";
            btnDelete.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnDelete, "حذف");
            btnDelete.UseVisualStyleBackColor = false;
            btnDelete.Click += btnDelete_Click;
            // 
            // btnUpdate
            // 
            btnUpdate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnUpdate.BackColor = Color.Transparent;
            btnUpdate.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnUpdate.Image = Properties.Resources.edit_profile_32px;
            btnUpdate.ImageAlign = ContentAlignment.MiddleRight;
            btnUpdate.Location = new Point(549, 233);
            btnUpdate.Name = "btnUpdate";
            btnUpdate.Size = new Size(102, 46);
            btnUpdate.TabIndex = 6;
            btnUpdate.Text = "تعديل";
            btnUpdate.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnUpdate, "تعديل");
            btnUpdate.UseVisualStyleBackColor = false;
            btnUpdate.Click += btnUpdate_Click;
            // 
            // btnAdd
            // 
            btnAdd.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnAdd.BackColor = Color.Transparent;
            btnAdd.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnAdd.Image = Properties.Resources.ok_32px;
            btnAdd.ImageAlign = ContentAlignment.MiddleRight;
            btnAdd.Location = new Point(657, 233);
            btnAdd.Name = "btnAdd";
            btnAdd.Size = new Size(101, 46);
            btnAdd.TabIndex = 5;
            btnAdd.Text = "إضافة";
            btnAdd.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnAdd, "إضافة");
            btnAdd.UseVisualStyleBackColor = false;
            btnAdd.Click += btnAdd_Click;
            // 
            // dataGridView1
            // 
            dataGridView1.AllowUserToAddRows = false;
            dataGridView1.AllowUserToDeleteRows = false;
            dataGridView1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView1.Location = new Point(12, 318);
            dataGridView1.Name = "dataGridView1";
            dataGridView1.ReadOnly = true;
            dataGridView1.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView1.Size = new Size(1000, 330);
            dataGridView1.TabIndex = 10;
            dataGridView1.CellClick += dataGridView1_CellClick;
            // 
            // CourseEvaluationForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1024, 660);
            Controls.Add(dataGridView1);
            Controls.Add(groupBox1);
            Name = "CourseEvaluationForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "إدارة تقييم الدورات التدريبية";
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numRating).EndInit();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).EndInit();
            ResumeLayout(false);
        }

        private GroupBox groupBox1;
        private Label lblCourse;
        private ComboBox cmbCourse;
        private Label lblEmployeeName;
        private ComboBox cmbEmployeeName;
        private Label lblRating;
        private NumericUpDown numRating;
        private Label lblComments;
        private TextBox txtComments;
        private Label lblEvaluationDate;
        private DateTimePicker dtEvaluationDate;
        private Button btnAdd;
        private Button btnUpdate;
        private Button btnDelete;
        private Button btnClear;
        private Button btnExportExcel;
        private DataGridView dataGridView1;
        private ToolTip toolTip1;
    }
}