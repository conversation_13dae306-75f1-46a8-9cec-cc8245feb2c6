namespace EmployeeManagementSystem
{
    partial class MonthlyOccurrencesForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            panelTop = new Panel();
            lblTitle = new Label();
            groupBoxControls = new GroupBox();
            btnExport = new Button();
            btnReportSettings = new Button();
            btnSearch = new Button();
            btnClearSearch = new Button();
            btnGenerateExcel = new Button();
            btnSaveOccurrences = new Button();
            cmbYear = new ComboBox();
            lblYear = new Label();
            cmbMonth = new ComboBox();
            txtSearch = new TextBox();
            lblMonth = new Label();
            cmbGroup = new ComboBox();
            lblGroup = new Label();
            cmbAttendanceType = new ComboBox();
            lblAttendanceType = new Label();
            chkSelectAll = new CheckBox();
            btnSelectAll = new Button();
            btnClearSelection = new Button();
            groupBoxShifts = new GroupBox();
            lblLegendTitle = new Label();
            dtpFromDate = new DateTimePicker();
            lblPresent = new Label();
            label2 = new Label();
            label1 = new Label();
            lblAbsent = new Label();
            lblEscape = new Label();
            lblVacation = new Label();
            dtpToDate = new DateTimePicker();
            lblFromDate = new Label();
            lblToDate = new Label();
            btnApplyToDateRange = new Button();
            cmbQuickRanges = new ComboBox();
            lblQuickRanges = new Label();
            panelQuickShifts = new Panel();
            btnFirstHalf = new Button();
            btnSecondHalf = new Button();
            btnWeek1 = new Button();
            btnWeek2 = new Button();
            btnClearAll = new Button();
            btnHelp = new Button();
            groupBoxData = new GroupBox();
            lbl_NoDocuments = new Label();
            dataGridViewOccurrences = new DataGridView();
            lblNoData = new Label();
            panelTop.SuspendLayout();
            groupBoxControls.SuspendLayout();
            groupBoxShifts.SuspendLayout();
            panelQuickShifts.SuspendLayout();
            groupBoxData.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridViewOccurrences).BeginInit();
            SuspendLayout();
            // 
            // panelTop
            // 
            panelTop.BackColor = Color.FromArgb(45, 45, 48);
            panelTop.Controls.Add(lblTitle);
            panelTop.Dock = DockStyle.Top;
            panelTop.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            panelTop.Location = new Point(0, 0);
            panelTop.Margin = new Padding(4, 3, 4, 3);
            panelTop.Name = "panelTop";
            panelTop.Size = new Size(1400, 69);
            panelTop.TabIndex = 0;
            // 
            // lblTitle
            // 
            lblTitle.Anchor = AnchorStyles.None;
            lblTitle.AutoSize = true;
            lblTitle.Font = new Font("Cairo", 15.7499981F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lblTitle.ForeColor = Color.White;
            lblTitle.Location = new Point(578, 15);
            lblTitle.Margin = new Padding(4, 0, 4, 0);
            lblTitle.Name = "lblTitle";
            lblTitle.Size = new Size(244, 39);
            lblTitle.TabIndex = 0;
            lblTitle.Text = "تقرير الوقوعات الشهرية";
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // groupBoxControls
            // 
            groupBoxControls.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxControls.Controls.Add(btnExport);
            groupBoxControls.Controls.Add(btnReportSettings);
            groupBoxControls.Controls.Add(btnSearch);
            groupBoxControls.Controls.Add(btnClearSearch);
            groupBoxControls.Controls.Add(btnGenerateExcel);
            groupBoxControls.Controls.Add(btnSaveOccurrences);
            groupBoxControls.Controls.Add(cmbYear);
            groupBoxControls.Controls.Add(lblYear);
            groupBoxControls.Controls.Add(cmbMonth);
            groupBoxControls.Controls.Add(txtSearch);
            groupBoxControls.Controls.Add(lblMonth);
            groupBoxControls.Controls.Add(cmbGroup);
            groupBoxControls.Controls.Add(lblGroup);
            groupBoxControls.Controls.Add(cmbAttendanceType);
            groupBoxControls.Controls.Add(lblAttendanceType);
            groupBoxControls.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxControls.Location = new Point(14, 81);
            groupBoxControls.Margin = new Padding(4, 3, 4, 3);
            groupBoxControls.Name = "groupBoxControls";
            groupBoxControls.Padding = new Padding(4, 3, 4, 3);
            groupBoxControls.RightToLeft = RightToLeft.Yes;
            groupBoxControls.Size = new Size(1373, 140);
            groupBoxControls.TabIndex = 1;
            groupBoxControls.TabStop = false;
            groupBoxControls.Text = "إعدادات التقرير";
            // 
            // btnExport
            // 
            btnExport.BackColor = Color.FromArgb(0, 122, 204);
            btnExport.Enabled = false;
            btnExport.FlatAppearance.BorderSize = 0;
            btnExport.FlatStyle = FlatStyle.Flat;
            btnExport.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnExport.ForeColor = Color.White;
            btnExport.Image = Properties.Resources.microsoft_excel_2019_32px;
            btnExport.ImageAlign = ContentAlignment.MiddleRight;
            btnExport.Location = new Point(9, 85);
            btnExport.Margin = new Padding(4, 3, 4, 3);
            btnExport.Name = "btnExport";
            btnExport.Size = new Size(137, 42);
            btnExport.TabIndex = 5;
            btnExport.Text = "تصدير إلى ";
            btnExport.TextAlign = ContentAlignment.MiddleLeft;
            btnExport.UseVisualStyleBackColor = false;
            btnExport.Click += btnExport_Click;
            // 
            // btnReportSettings
            // 
            btnReportSettings.BackColor = Color.FromArgb(255, 140, 0);
            btnReportSettings.FlatAppearance.BorderSize = 0;
            btnReportSettings.FlatStyle = FlatStyle.Flat;
            btnReportSettings.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnReportSettings.ForeColor = Color.White;
            btnReportSettings.Image = Properties.Resources.settings_32px;
            btnReportSettings.ImageAlign = ContentAlignment.MiddleRight;
            btnReportSettings.Location = new Point(9, 33);
            btnReportSettings.Margin = new Padding(4, 3, 4, 3);
            btnReportSettings.Name = "btnReportSettings";
            btnReportSettings.Size = new Size(137, 42);
            btnReportSettings.TabIndex = 6;
            btnReportSettings.Text = "إعدادات التقرير";
            btnReportSettings.TextAlign = ContentAlignment.MiddleLeft;
            btnReportSettings.UseVisualStyleBackColor = false;
            btnReportSettings.Click += btnReportSettings_Click;
            // 
            // btnSearch
            // 
            btnSearch.BackColor = Color.FromArgb(46, 125, 50);
            btnSearch.FlatAppearance.BorderSize = 0;
            btnSearch.FlatStyle = FlatStyle.Flat;
            btnSearch.Font = new Font("Cairo", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnSearch.ForeColor = Color.White;
            btnSearch.Image = Properties.Resources.search_32px;
            btnSearch.ImageAlign = ContentAlignment.MiddleRight;
            btnSearch.Location = new Point(1050, 37);
            btnSearch.Margin = new Padding(4, 3, 4, 3);
            btnSearch.Name = "btnSearch";
            btnSearch.Size = new Size(87, 42);
            btnSearch.TabIndex = 4;
            btnSearch.Text = "بحث";
            btnSearch.TextAlign = ContentAlignment.MiddleLeft;
            btnSearch.UseVisualStyleBackColor = false;
            btnSearch.Click += btnSearch_Click;
            // 
            // btnClearSearch
            // 
            btnClearSearch.BackColor = Color.FromArgb(192, 0, 0);
            btnClearSearch.FlatAppearance.BorderSize = 0;
            btnClearSearch.FlatStyle = FlatStyle.Flat;
            btnClearSearch.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnClearSearch.ForeColor = Color.White;
            btnClearSearch.Image = Properties.Resources.delete_32px;
            btnClearSearch.ImageAlign = ContentAlignment.MiddleRight;
            btnClearSearch.Location = new Point(1013, 85);
            btnClearSearch.Margin = new Padding(4, 3, 4, 3);
            btnClearSearch.Name = "btnClearSearch";
            btnClearSearch.Size = new Size(124, 42);
            btnClearSearch.TabIndex = 5;
            btnClearSearch.Text = "إلغاء البحث";
            btnClearSearch.TextAlign = ContentAlignment.MiddleLeft;
            btnClearSearch.UseVisualStyleBackColor = false;
            btnClearSearch.Click += btnClearSearch_Click;
            // 
            // btnGenerateExcel
            // 
            btnGenerateExcel.BackColor = Color.FromArgb(46, 125, 50);
            btnGenerateExcel.FlatAppearance.BorderSize = 0;
            btnGenerateExcel.FlatStyle = FlatStyle.Flat;
            btnGenerateExcel.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnGenerateExcel.ForeColor = Color.White;
            btnGenerateExcel.Image = Properties.Resources.create_32px1;
            btnGenerateExcel.ImageAlign = ContentAlignment.MiddleRight;
            btnGenerateExcel.Location = new Point(154, 85);
            btnGenerateExcel.Margin = new Padding(4, 3, 4, 3);
            btnGenerateExcel.Name = "btnGenerateExcel";
            btnGenerateExcel.Size = new Size(131, 42);
            btnGenerateExcel.TabIndex = 4;
            btnGenerateExcel.Text = "انشاء الوقوعات";
            btnGenerateExcel.TextAlign = ContentAlignment.MiddleLeft;
            btnGenerateExcel.UseVisualStyleBackColor = false;
            btnGenerateExcel.Click += btnGenerateExcel_Click;
            // 
            // btnSaveOccurrences
            // 
            btnSaveOccurrences.BackColor = Color.FromArgb(255, 152, 0);
            btnSaveOccurrences.FlatAppearance.BorderSize = 0;
            btnSaveOccurrences.FlatStyle = FlatStyle.Flat;
            btnSaveOccurrences.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnSaveOccurrences.ForeColor = Color.White;
            btnSaveOccurrences.Image = Properties.Resources.save_close_32px1;
            btnSaveOccurrences.ImageAlign = ContentAlignment.MiddleRight;
            btnSaveOccurrences.Location = new Point(154, 33);
            btnSaveOccurrences.Margin = new Padding(4, 3, 4, 3);
            btnSaveOccurrences.Name = "btnSaveOccurrences";
            btnSaveOccurrences.Size = new Size(131, 42);
            btnSaveOccurrences.TabIndex = 6;
            btnSaveOccurrences.Text = "حفظ الوقوعات";
            btnSaveOccurrences.TextAlign = ContentAlignment.MiddleLeft;
            btnSaveOccurrences.UseVisualStyleBackColor = false;
            btnSaveOccurrences.Click += btnSaveOccurrences_Click;
            // 
            // cmbYear
            // 
            cmbYear.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbYear.Font = new Font("Cairo", 9.75F);
            cmbYear.FormattingEnabled = true;
            cmbYear.Location = new Point(290, 42);
            cmbYear.Margin = new Padding(4, 3, 4, 3);
            cmbYear.Name = "cmbYear";
            cmbYear.Size = new Size(190, 32);
            cmbYear.TabIndex = 3;
            // 
            // lblYear
            // 
            lblYear.AutoSize = true;
            lblYear.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblYear.Location = new Point(485, 46);
            lblYear.Margin = new Padding(4, 0, 4, 0);
            lblYear.Name = "lblYear";
            lblYear.Size = new Size(47, 24);
            lblYear.TabIndex = 2;
            lblYear.Text = "السنة:";
            // 
            // cmbMonth
            // 
            cmbMonth.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbMonth.Font = new Font("Cairo", 9.75F);
            cmbMonth.FormattingEnabled = true;
            cmbMonth.Location = new Point(542, 42);
            cmbMonth.Margin = new Padding(4, 3, 4, 3);
            cmbMonth.Name = "cmbMonth";
            cmbMonth.Size = new Size(190, 32);
            cmbMonth.TabIndex = 1;
            // 
            // txtSearch
            // 
            txtSearch.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            txtSearch.Location = new Point(792, 42);
            txtSearch.Name = "txtSearch";
            txtSearch.PlaceholderText = "البحث في أسماء الموظفين...";
            txtSearch.RightToLeft = RightToLeft.No;
            txtSearch.Size = new Size(259, 32);
            txtSearch.TabIndex = 6;
            txtSearch.TextAlign = HorizontalAlignment.Right;
            txtSearch.KeyDown += TxtSearch_KeyDown;
            // 
            // lblMonth
            // 
            lblMonth.AutoSize = true;
            lblMonth.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblMonth.Location = new Point(737, 46);
            lblMonth.Margin = new Padding(4, 0, 4, 0);
            lblMonth.Name = "lblMonth";
            lblMonth.Size = new Size(49, 24);
            lblMonth.TabIndex = 0;
            lblMonth.Text = "الشهر:";
            // 
            // cmbGroup
            // 
            cmbGroup.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbGroup.Font = new Font("Cairo", 9.75F);
            cmbGroup.FormattingEnabled = true;
            cmbGroup.Location = new Point(300, 91);
            cmbGroup.Margin = new Padding(4, 3, 4, 3);
            cmbGroup.Name = "cmbGroup";
            cmbGroup.Size = new Size(190, 32);
            cmbGroup.TabIndex = 7;
            cmbGroup.SelectedIndexChanged += cmbGroup_SelectedIndexChanged;
            // 
            // lblGroup
            // 
            lblGroup.AutoSize = true;
            lblGroup.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblGroup.Location = new Point(498, 95);
            lblGroup.Margin = new Padding(4, 0, 4, 0);
            lblGroup.Name = "lblGroup";
            lblGroup.Size = new Size(74, 24);
            lblGroup.TabIndex = 8;
            lblGroup.Text = "المجموعة:";
            // 
            // cmbAttendanceType
            // 
            cmbAttendanceType.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbAttendanceType.Font = new Font("Cairo", 9.75F);
            cmbAttendanceType.FormattingEnabled = true;
            cmbAttendanceType.Items.AddRange(new object[] { "V - حضور", "غ - غياب" });
            cmbAttendanceType.Location = new Point(588, 91);
            cmbAttendanceType.Margin = new Padding(4, 3, 4, 3);
            cmbAttendanceType.Name = "cmbAttendanceType";
            cmbAttendanceType.Size = new Size(190, 32);
            cmbAttendanceType.TabIndex = 9;
            // 
            // lblAttendanceType
            // 
            lblAttendanceType.AutoSize = true;
            lblAttendanceType.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblAttendanceType.Location = new Point(786, 95);
            lblAttendanceType.Margin = new Padding(4, 0, 4, 0);
            lblAttendanceType.Name = "lblAttendanceType";
            lblAttendanceType.Size = new Size(46, 24);
            lblAttendanceType.TabIndex = 10;
            lblAttendanceType.Text = "الحالة:";
            // 
            // chkSelectAll
            // 
            chkSelectAll.AutoSize = true;
            chkSelectAll.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            chkSelectAll.Location = new Point(1063, 75);
            chkSelectAll.Margin = new Padding(4, 3, 4, 3);
            chkSelectAll.Name = "chkSelectAll";
            chkSelectAll.RightToLeft = RightToLeft.No;
            chkSelectAll.Size = new Size(93, 28);
            chkSelectAll.TabIndex = 11;
            chkSelectAll.Text = "تحديد الكل";
            chkSelectAll.UseVisualStyleBackColor = true;
            chkSelectAll.CheckedChanged += chkSelectAll_CheckedChanged;
            // 
            // btnSelectAll
            // 
            btnSelectAll.BackColor = Color.FromArgb(33, 150, 243);
            btnSelectAll.FlatAppearance.BorderSize = 0;
            btnSelectAll.FlatStyle = FlatStyle.Flat;
            btnSelectAll.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnSelectAll.ForeColor = Color.White;
            btnSelectAll.Image = Properties.Resources.checked_checkbox_32px;
            btnSelectAll.ImageAlign = ContentAlignment.MiddleRight;
            btnSelectAll.Location = new Point(650, 5);
            btnSelectAll.Margin = new Padding(4, 3, 4, 3);
            btnSelectAll.Name = "btnSelectAll";
            btnSelectAll.Size = new Size(124, 45);
            btnSelectAll.TabIndex = 12;
            btnSelectAll.Text = "تحديد اختياري";
            btnSelectAll.TextAlign = ContentAlignment.MiddleLeft;
            btnSelectAll.UseVisualStyleBackColor = false;
            btnSelectAll.Click += btnSelectAll_Click;
            // 
            // btnClearSelection
            // 
            btnClearSelection.BackColor = Color.FromArgb(158, 158, 158);
            btnClearSelection.FlatAppearance.BorderSize = 0;
            btnClearSelection.FlatStyle = FlatStyle.Flat;
            btnClearSelection.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnClearSelection.ForeColor = Color.White;
            btnClearSelection.Image = Properties.Resources.delete_32px;
            btnClearSelection.ImageAlign = ContentAlignment.MiddleRight;
            btnClearSelection.Location = new Point(525, 5);
            btnClearSelection.Margin = new Padding(4, 3, 4, 3);
            btnClearSelection.Name = "btnClearSelection";
            btnClearSelection.Size = new Size(117, 45);
            btnClearSelection.TabIndex = 13;
            btnClearSelection.Text = "إلغاء التحديد";
            btnClearSelection.TextAlign = ContentAlignment.MiddleLeft;
            btnClearSelection.UseVisualStyleBackColor = false;
            btnClearSelection.Click += btnClearSelection_Click;
            // 
            // groupBoxShifts
            // 
            groupBoxShifts.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxShifts.Controls.Add(lblLegendTitle);
            groupBoxShifts.Controls.Add(dtpFromDate);
            groupBoxShifts.Controls.Add(lblPresent);
            groupBoxShifts.Controls.Add(label2);
            groupBoxShifts.Controls.Add(label1);
            groupBoxShifts.Controls.Add(lblAbsent);
            groupBoxShifts.Controls.Add(lblEscape);
            groupBoxShifts.Controls.Add(lblVacation);
            groupBoxShifts.Controls.Add(dtpToDate);
            groupBoxShifts.Controls.Add(lblFromDate);
            groupBoxShifts.Controls.Add(lblToDate);
            groupBoxShifts.Controls.Add(btnApplyToDateRange);
            groupBoxShifts.Controls.Add(cmbQuickRanges);
            groupBoxShifts.Controls.Add(lblQuickRanges);
            groupBoxShifts.Controls.Add(chkSelectAll);
            groupBoxShifts.Controls.Add(panelQuickShifts);
            groupBoxShifts.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxShifts.Location = new Point(14, 230);
            groupBoxShifts.Margin = new Padding(4, 3, 4, 3);
            groupBoxShifts.Name = "groupBoxShifts";
            groupBoxShifts.Padding = new Padding(4, 3, 4, 3);
            groupBoxShifts.RightToLeft = RightToLeft.Yes;
            groupBoxShifts.Size = new Size(1372, 124);
            groupBoxShifts.TabIndex = 2;
            groupBoxShifts.TabStop = false;
            groupBoxShifts.Text = "إدارة الوجبات والفترات";
            // 
            // lblLegendTitle
            // 
            lblLegendTitle.AutoSize = true;
            lblLegendTitle.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lblLegendTitle.Location = new Point(84, 20);
            lblLegendTitle.Margin = new Padding(4, 0, 4, 0);
            lblLegendTitle.Name = "lblLegendTitle";
            lblLegendTitle.Size = new Size(112, 24);
            lblLegendTitle.TabIndex = 4;
            lblLegendTitle.Text = "مفاتيح الوقوعات:";
            // 
            // dtpFromDate
            // 
            dtpFromDate.Font = new Font("Cairo", 9.75F);
            dtpFromDate.Format = DateTimePickerFormat.Short;
            dtpFromDate.Location = new Point(989, 23);
            dtpFromDate.Name = "dtpFromDate";
            dtpFromDate.Size = new Size(120, 32);
            dtpFromDate.TabIndex = 0;
            // 
            // lblPresent
            // 
            lblPresent.BackColor = Color.Chocolate;
            lblPresent.BorderStyle = BorderStyle.FixedSingle;
            lblPresent.Font = new Font("Cairo SemiBold", 9.75F, FontStyle.Bold);
            lblPresent.ForeColor = Color.Transparent;
            lblPresent.Location = new Point(9, 50);
            lblPresent.Margin = new Padding(4, 0, 4, 0);
            lblPresent.Name = "lblPresent";
            lblPresent.Size = new Size(84, 29);
            lblPresent.TabIndex = 0;
            lblPresent.Text = " حضور= V";
            lblPresent.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // label2
            // 
            label2.BackColor = Color.RoyalBlue;
            label2.BorderStyle = BorderStyle.FixedSingle;
            label2.Font = new Font("Cairo SemiBold", 9.75F, FontStyle.Bold);
            label2.ForeColor = Color.Transparent;
            label2.Location = new Point(185, 51);
            label2.Margin = new Padding(4, 0, 4, 0);
            label2.Name = "label2";
            label2.Size = new Size(82, 29);
            label2.TabIndex = 1;
            label2.Text = "دورة = د";
            label2.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // label1
            // 
            label1.BackColor = Color.LimeGreen;
            label1.BorderStyle = BorderStyle.FixedSingle;
            label1.Font = new Font("Cairo SemiBold", 9.75F, FontStyle.Bold);
            label1.ForeColor = Color.Transparent;
            label1.Location = new Point(186, 88);
            label1.Margin = new Padding(4, 0, 4, 0);
            label1.Name = "label1";
            label1.Size = new Size(82, 29);
            label1.TabIndex = 1;
            label1.Text = "مرضية = م";
            label1.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblAbsent
            // 
            lblAbsent.BackColor = Color.Yellow;
            lblAbsent.BorderStyle = BorderStyle.FixedSingle;
            lblAbsent.Font = new Font("Cairo SemiBold", 9.75F, FontStyle.Bold);
            lblAbsent.ForeColor = Color.Blue;
            lblAbsent.Location = new Point(98, 88);
            lblAbsent.Margin = new Padding(4, 0, 4, 0);
            lblAbsent.Name = "lblAbsent";
            lblAbsent.Size = new Size(82, 29);
            lblAbsent.TabIndex = 1;
            lblAbsent.Text = "غياب = غ";
            lblAbsent.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblEscape
            // 
            lblEscape.BackColor = Color.Red;
            lblEscape.BorderStyle = BorderStyle.FixedSingle;
            lblEscape.Font = new Font("Cairo SemiBold", 9.75F, FontStyle.Bold);
            lblEscape.ForeColor = Color.Transparent;
            lblEscape.Location = new Point(98, 51);
            lblEscape.Margin = new Padding(4, 0, 4, 0);
            lblEscape.Name = "lblEscape";
            lblEscape.Size = new Size(82, 29);
            lblEscape.TabIndex = 2;
            lblEscape.Text = "هروب = ت";
            lblEscape.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblVacation
            // 
            lblVacation.BackColor = Color.DodgerBlue;
            lblVacation.BorderStyle = BorderStyle.FixedSingle;
            lblVacation.Font = new Font("Cairo SemiBold", 9.75F, FontStyle.Bold);
            lblVacation.ForeColor = Color.Black;
            lblVacation.Location = new Point(9, 88);
            lblVacation.Margin = new Padding(4, 0, 4, 0);
            lblVacation.Name = "lblVacation";
            lblVacation.Size = new Size(84, 29);
            lblVacation.TabIndex = 3;
            lblVacation.Text = "اجازة = ج";
            lblVacation.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // dtpToDate
            // 
            dtpToDate.Font = new Font("Cairo", 9.75F);
            dtpToDate.Format = DateTimePickerFormat.Short;
            dtpToDate.Location = new Point(819, 23);
            dtpToDate.Name = "dtpToDate";
            dtpToDate.Size = new Size(120, 32);
            dtpToDate.TabIndex = 1;
            // 
            // lblFromDate
            // 
            lblFromDate.AutoSize = true;
            lblFromDate.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblFromDate.Location = new Point(1119, 27);
            lblFromDate.Name = "lblFromDate";
            lblFromDate.Size = new Size(31, 24);
            lblFromDate.TabIndex = 2;
            lblFromDate.Text = "من:";
            // 
            // lblToDate
            // 
            lblToDate.AutoSize = true;
            lblToDate.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblToDate.Location = new Point(949, 29);
            lblToDate.Name = "lblToDate";
            lblToDate.Size = new Size(34, 24);
            lblToDate.TabIndex = 3;
            lblToDate.Text = "إلى:";
            // 
            // btnApplyToDateRange
            // 
            btnApplyToDateRange.BackColor = Color.FromArgb(76, 175, 80);
            btnApplyToDateRange.FlatAppearance.BorderSize = 0;
            btnApplyToDateRange.FlatStyle = FlatStyle.Flat;
            btnApplyToDateRange.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnApplyToDateRange.ForeColor = Color.White;
            btnApplyToDateRange.Image = Properties.Resources.ok_32px;
            btnApplyToDateRange.ImageAlign = ContentAlignment.MiddleRight;
            btnApplyToDateRange.Location = new Point(684, 17);
            btnApplyToDateRange.Name = "btnApplyToDateRange";
            btnApplyToDateRange.Size = new Size(125, 40);
            btnApplyToDateRange.TabIndex = 4;
            btnApplyToDateRange.Text = "تطبيق ";
            btnApplyToDateRange.UseVisualStyleBackColor = false;
            btnApplyToDateRange.Click += btnApplyToDateRange_Click;
            // 
            // cmbQuickRanges
            // 
            cmbQuickRanges.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbQuickRanges.Font = new Font("Cairo", 9.75F);
            cmbQuickRanges.FormattingEnabled = true;
            cmbQuickRanges.Location = new Point(283, 17);
            cmbQuickRanges.Name = "cmbQuickRanges";
            cmbQuickRanges.Size = new Size(250, 32);
            cmbQuickRanges.TabIndex = 5;
            // 
            // lblQuickRanges
            // 
            lblQuickRanges.AutoSize = true;
            lblQuickRanges.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblQuickRanges.Location = new Point(543, 22);
            lblQuickRanges.Name = "lblQuickRanges";
            lblQuickRanges.Size = new Size(109, 24);
            lblQuickRanges.TabIndex = 6;
            lblQuickRanges.Text = "الوجبات السريعة:";
            // 
            // panelQuickShifts
            // 
            panelQuickShifts.BorderStyle = BorderStyle.FixedSingle;
            panelQuickShifts.Controls.Add(btnFirstHalf);
            panelQuickShifts.Controls.Add(btnSecondHalf);
            panelQuickShifts.Controls.Add(btnWeek1);
            panelQuickShifts.Controls.Add(btnWeek2);
            panelQuickShifts.Controls.Add(btnClearAll);
            panelQuickShifts.Controls.Add(btnHelp);
            panelQuickShifts.Controls.Add(btnSelectAll);
            panelQuickShifts.Controls.Add(btnClearSelection);
            panelQuickShifts.Location = new Point(275, 60);
            panelQuickShifts.Name = "panelQuickShifts";
            panelQuickShifts.Size = new Size(782, 57);
            panelQuickShifts.TabIndex = 7;
            // 
            // btnFirstHalf
            // 
            btnFirstHalf.BackColor = Color.LightBlue;
            btnFirstHalf.FlatAppearance.BorderSize = 0;
            btnFirstHalf.FlatStyle = FlatStyle.Flat;
            btnFirstHalf.Font = new Font("Cairo SemiBold", 6.25F, FontStyle.Bold);
            btnFirstHalf.ForeColor = Color.Black;
            btnFirstHalf.Location = new Point(5, 5);
            btnFirstHalf.Name = "btnFirstHalf";
            btnFirstHalf.Size = new Size(75, 45);
            btnFirstHalf.TabIndex = 0;
            btnFirstHalf.Text = "النصف الأول\n(1-15)";
            btnFirstHalf.UseVisualStyleBackColor = false;
            btnFirstHalf.Click += btnFirstHalf_Click;
            // 
            // btnSecondHalf
            // 
            btnSecondHalf.BackColor = Color.LightGreen;
            btnSecondHalf.FlatAppearance.BorderSize = 0;
            btnSecondHalf.FlatStyle = FlatStyle.Flat;
            btnSecondHalf.Font = new Font("Cairo SemiBold", 6.25F, FontStyle.Bold);
            btnSecondHalf.ForeColor = Color.Black;
            btnSecondHalf.Location = new Point(85, 5);
            btnSecondHalf.Name = "btnSecondHalf";
            btnSecondHalf.Size = new Size(75, 45);
            btnSecondHalf.TabIndex = 1;
            btnSecondHalf.Text = "النصف الثاني\n(16-نهاية)";
            btnSecondHalf.UseVisualStyleBackColor = false;
            btnSecondHalf.Click += btnSecondHalf_Click;
            // 
            // btnWeek1
            // 
            btnWeek1.BackColor = Color.LightYellow;
            btnWeek1.FlatAppearance.BorderSize = 0;
            btnWeek1.FlatStyle = FlatStyle.Flat;
            btnWeek1.Font = new Font("Cairo SemiBold", 6.25F, FontStyle.Bold);
            btnWeek1.ForeColor = Color.Black;
            btnWeek1.Location = new Point(165, 5);
            btnWeek1.Name = "btnWeek1";
            btnWeek1.Size = new Size(75, 45);
            btnWeek1.TabIndex = 2;
            btnWeek1.Text = "الأسبوع 1\n(1-7)";
            btnWeek1.UseVisualStyleBackColor = false;
            btnWeek1.Click += btnWeek1_Click;
            // 
            // btnWeek2
            // 
            btnWeek2.BackColor = Color.LightCoral;
            btnWeek2.FlatAppearance.BorderSize = 0;
            btnWeek2.FlatStyle = FlatStyle.Flat;
            btnWeek2.Font = new Font("Cairo SemiBold", 6.25F, FontStyle.Bold);
            btnWeek2.ForeColor = Color.Black;
            btnWeek2.Location = new Point(245, 5);
            btnWeek2.Name = "btnWeek2";
            btnWeek2.Size = new Size(75, 45);
            btnWeek2.TabIndex = 3;
            btnWeek2.Text = "الأسبوع 2\n(8-14)";
            btnWeek2.UseVisualStyleBackColor = false;
            btnWeek2.Click += btnWeek2_Click;
            // 
            // btnClearAll
            // 
            btnClearAll.BackColor = Color.LightGray;
            btnClearAll.FlatAppearance.BorderSize = 0;
            btnClearAll.FlatStyle = FlatStyle.Flat;
            btnClearAll.Font = new Font("Cairo SemiBold", 6.25F, FontStyle.Bold);
            btnClearAll.ForeColor = Color.Black;
            btnClearAll.Location = new Point(325, 5);
            btnClearAll.Name = "btnClearAll";
            btnClearAll.Size = new Size(75, 45);
            btnClearAll.TabIndex = 4;
            btnClearAll.Text = "مسح الكل\n\U0001f9f9";
            btnClearAll.UseVisualStyleBackColor = false;
            btnClearAll.Click += btnClearAll_Click;
            // 
            // btnHelp
            // 
            btnHelp.BackColor = Color.DarkCyan;
            btnHelp.FlatAppearance.BorderSize = 0;
            btnHelp.FlatStyle = FlatStyle.Flat;
            btnHelp.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnHelp.ForeColor = Color.Black;
            btnHelp.Image = Properties.Resources.help_32px;
            btnHelp.ImageAlign = ContentAlignment.MiddleRight;
            btnHelp.Location = new Point(418, 5);
            btnHelp.Name = "btnHelp";
            btnHelp.Size = new Size(100, 45);
            btnHelp.TabIndex = 8;
            btnHelp.Text = " مساعدة";
            btnHelp.TextAlign = ContentAlignment.MiddleLeft;
            btnHelp.UseVisualStyleBackColor = false;
            // 
            // groupBoxData
            // 
            groupBoxData.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxData.Controls.Add(lbl_NoDocuments);
            groupBoxData.Controls.Add(dataGridViewOccurrences);
            groupBoxData.Controls.Add(lblNoData);
            groupBoxData.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxData.Location = new Point(14, 360);
            groupBoxData.Margin = new Padding(4, 3, 4, 3);
            groupBoxData.Name = "groupBoxData";
            groupBoxData.Padding = new Padding(4, 3, 4, 3);
            groupBoxData.RightToLeft = RightToLeft.Yes;
            groupBoxData.Size = new Size(1372, 476);
            groupBoxData.TabIndex = 2;
            groupBoxData.TabStop = false;
            groupBoxData.Text = "بيانات الوقوعات";
            // 
            // lbl_NoDocuments
            // 
            lbl_NoDocuments.Anchor = AnchorStyles.None;
            lbl_NoDocuments.AutoSize = true;
            lbl_NoDocuments.Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lbl_NoDocuments.Location = new Point(659, 223);
            lbl_NoDocuments.Name = "lbl_NoDocuments";
            lbl_NoDocuments.Size = new Size(54, 30);
            lbl_NoDocuments.TabIndex = 35;
            lbl_NoDocuments.Text = "label6";
            lbl_NoDocuments.Visible = false;
            // 
            // dataGridViewOccurrences
            // 
            dataGridViewOccurrences.AllowUserToAddRows = false;
            dataGridViewOccurrences.AllowUserToDeleteRows = false;
            dataGridViewOccurrences.BackgroundColor = Color.White;
            dataGridViewOccurrences.BorderStyle = BorderStyle.Fixed3D;
            dataGridViewOccurrences.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewOccurrences.Dock = DockStyle.Fill;
            dataGridViewOccurrences.Location = new Point(4, 28);
            dataGridViewOccurrences.Margin = new Padding(4, 3, 4, 3);
            dataGridViewOccurrences.MultiSelect = false;
            dataGridViewOccurrences.Name = "dataGridViewOccurrences";
            dataGridViewOccurrences.ReadOnly = true;
            dataGridViewOccurrences.RightToLeft = RightToLeft.Yes;
            dataGridViewOccurrences.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridViewOccurrences.Size = new Size(1364, 445);
            dataGridViewOccurrences.TabIndex = 0;
            // 
            // lblNoData
            // 
            lblNoData.Anchor = AnchorStyles.None;
            lblNoData.AutoSize = true;
            lblNoData.Font = new Font("Arial", 12F, FontStyle.Bold);
            lblNoData.ForeColor = Color.Gray;
            lblNoData.Location = new Point(618, 209);
            lblNoData.Margin = new Padding(4, 0, 4, 0);
            lblNoData.Name = "lblNoData";
            lblNoData.Size = new Size(125, 19);
            lblNoData.TabIndex = 1;
            lblNoData.Text = "لا توجد بيانات وقوعات";
            lblNoData.TextAlign = ContentAlignment.MiddleCenter;
            lblNoData.Visible = false;
            // 
            // MonthlyOccurrencesForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.White;
            ClientSize = new Size(1400, 881);
            Controls.Add(groupBoxData);
            Controls.Add(groupBoxShifts);
            Controls.Add(groupBoxControls);
            Controls.Add(panelTop);
            Margin = new Padding(4, 3, 4, 3);
            MinimumSize = new Size(1164, 686);
            Name = "MonthlyOccurrencesForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "تقرير الوقوعات الشهرية";
            WindowState = FormWindowState.Maximized;
            Load += MonthlyOccurrencesForm_Load;
            panelTop.ResumeLayout(false);
            panelTop.PerformLayout();
            groupBoxControls.ResumeLayout(false);
            groupBoxControls.PerformLayout();
            groupBoxShifts.ResumeLayout(false);
            groupBoxShifts.PerformLayout();
            panelQuickShifts.ResumeLayout(false);
            groupBoxData.ResumeLayout(false);
            groupBoxData.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridViewOccurrences).EndInit();
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panelTop;
        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.GroupBox groupBoxControls;
        private System.Windows.Forms.Button btnExport;
        private System.Windows.Forms.Button btnReportSettings;
        private System.Windows.Forms.Button btnGenerateExcel;
        private System.Windows.Forms.Button btnSaveOccurrences;
        private System.Windows.Forms.ComboBox cmbYear;
        private System.Windows.Forms.Label lblYear;
        private System.Windows.Forms.ComboBox cmbMonth;
        private System.Windows.Forms.Label lblMonth;
        private System.Windows.Forms.TextBox txtSearch;
        private System.Windows.Forms.ComboBox cmbGroup;
        private System.Windows.Forms.Label lblGroup;
        private System.Windows.Forms.ComboBox cmbAttendanceType;
        private System.Windows.Forms.Label lblAttendanceType;
        private System.Windows.Forms.CheckBox chkSelectAll;
        private System.Windows.Forms.Button btnSelectAll;
        private System.Windows.Forms.Button btnClearSelection;
        private System.Windows.Forms.GroupBox groupBoxData;
        private System.Windows.Forms.DataGridView dataGridViewOccurrences;
        private System.Windows.Forms.Label lblNoData;
        private System.Windows.Forms.Label lblLegendTitle;
        private System.Windows.Forms.Label lblVacation;
        private System.Windows.Forms.Label lblEscape;
        private System.Windows.Forms.Label lblAbsent;
        private System.Windows.Forms.Label lblPresent;
        private System.Windows.Forms.GroupBox groupBoxShifts;
        private System.Windows.Forms.DateTimePicker dtpFromDate;
        private System.Windows.Forms.DateTimePicker dtpToDate;
        private System.Windows.Forms.Label lblFromDate;
        private System.Windows.Forms.Label lblToDate;
        private System.Windows.Forms.Button btnApplyToDateRange;
        private System.Windows.Forms.ComboBox cmbQuickRanges;
        private System.Windows.Forms.Label lblQuickRanges;
        private System.Windows.Forms.Panel panelQuickShifts;
        private System.Windows.Forms.Button btnFirstHalf;
        private System.Windows.Forms.Button btnSecondHalf;
        private System.Windows.Forms.Button btnWeek1;
        private System.Windows.Forms.Button btnWeek2;
        private System.Windows.Forms.Button btnClearAll;
        private System.Windows.Forms.Button btnHelp;
        private Label label1;
        private Button btnSearch;
        private Button btnClearSearch;
        private Label lbl_NoDocuments;
        private Label label2;
    }
}