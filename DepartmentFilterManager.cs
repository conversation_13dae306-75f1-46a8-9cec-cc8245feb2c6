using System;
using System.Data;
using System.Windows.Forms;
using System.Collections.Generic;
using System.Linq;

namespace EmployeeManagementSystem
{
    /// <summary>
    /// مدير الفلترة المركزي لتطبيق فلترة الأقسام على جميع النماذج
    /// يوفر طرق موحدة لتطبيق الفلترة وإدارة الصلاحيات
    /// </summary>
    public static class DepartmentFilterManager
    {
        #region تطبيق الفلترة على النماذج

        /// <summary>
        /// تطبيق فلترة شاملة على أي نموذج يحتوي على بيانات موظفين
        /// </summary>
        /// <param name="form">النموذج المراد تطبيق الفلترة عليه</param>
        /// <param name="currentUser">المستخدم الحالي</param>
        /// <param name="dataGridView">جدول البيانات في النموذج</param>
        /// <param name="dataSource">مصدر البيانات</param>
        public static void ApplyDepartmentFilter(Form form, User? currentUser, DataGridView dataGridView, DataTable dataSource)
        {
            if (currentUser == null || dataSource == null) return;

            try
            {
                // تطبيق الفلترة حسب نوع المستخدم
                DataTable filteredData = FilterDataByDepartment(currentUser, dataSource);
                
                // تحديث جدول البيانات
                dataGridView.DataSource = filteredData;
                
                // تحديث عنوان النافذة
                UpdateFormTitle(form, currentUser, filteredData.Rows.Count);
                
                // عرض رسالة إعلامية إذا كان مدير قسم
                ShowFilterInfoIfNeeded(currentUser, filteredData.Rows.Count);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق فلترة القسم: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// فلترة البيانات حسب القسم
        /// </summary>
        private static DataTable FilterDataByDepartment(User currentUser, DataTable originalData)
        {
            // المدير العام يرى جميع البيانات
            if (EmployeeDepartmentHelper.IsGeneralManager(currentUser))
            {
                return originalData;
            }

            // مدير القسم يرى فقط بيانات قسمه
            if (EmployeeDepartmentHelper.IsDepartmentManager(currentUser))
            {
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                if (!managedDepartmentId.HasValue)
                {
                    return CreateEmptyDataTable(originalData);
                }

                var accessibleCodes = EmployeeDepartmentHelper.GetAccessibleEmployeeCodes(currentUser);
                return FilterDataTableByEmployeeCodes(originalData, accessibleCodes);
            }

            // المستخدم العادي يرى جميع البيانات (حسب السياسة الحالية)
            return originalData;
        }

        /// <summary>
        /// فلترة جدول البيانات حسب أكواد الموظفين المسموحة
        /// </summary>
        private static DataTable FilterDataTableByEmployeeCodes(DataTable originalData, List<int> allowedCodes)
        {
            var filteredTable = originalData.Clone();

            foreach (DataRow row in originalData.Rows)
            {
                // البحث عن عمود كود الموظف بأسماء مختلفة
                int employeeCode = GetEmployeeCodeFromRow(row);
                
                if (employeeCode > 0 && allowedCodes.Contains(employeeCode))
                {
                    filteredTable.ImportRow(row);
                }
            }

            return filteredTable;
        }

        /// <summary>
        /// استخراج كود الموظف من صف البيانات
        /// </summary>
        private static int GetEmployeeCodeFromRow(DataRow row)
        {
            // قائمة بأسماء الأعمدة المحتملة لكود الموظف
            string[] possibleColumns = { "EmployeeCode", "كود الموظف", "EmployeeId", "رقم الموظف" };

            foreach (string columnName in possibleColumns)
            {
                if (row.Table.Columns.Contains(columnName) && 
                    int.TryParse(row[columnName]?.ToString(), out int code))
                {
                    return code;
                }
            }

            return 0;
        }

        /// <summary>
        /// إنشاء جدول فارغ بنفس بنية الجدول الأصلي
        /// </summary>
        private static DataTable CreateEmptyDataTable(DataTable originalTable)
        {
            return originalTable.Clone();
        }

        #endregion

        #region تحديث واجهة المستخدم

        /// <summary>
        /// تحديث عنوان النافذة ليعكس حالة الفلترة
        /// </summary>
        private static void UpdateFormTitle(Form form, User currentUser, int recordCount)
        {
            string baseTitle = form.Text;
            
            // إزالة معلومات الفلترة السابقة إن وجدت
            if (baseTitle.Contains(" - "))
            {
                baseTitle = baseTitle.Split('-')[0].Trim();
            }

            if (EmployeeDepartmentHelper.IsDepartmentManager(currentUser))
            {
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                if (managedDepartmentId.HasValue)
                {
                    form.Text = $"🔒 {baseTitle} - القسم {managedDepartmentId} ({recordCount} سجل)";
                }
                else
                {
                    form.Text = $"⚠️ {baseTitle} - لا يوجد قسم مُدار";
                }
            }
            else if (EmployeeDepartmentHelper.IsGeneralManager(currentUser))
            {
                form.Text = $"🌐 {baseTitle} - جميع الأقسام ({recordCount} سجل)";
            }
            else
            {
                form.Text = $"{baseTitle} ({recordCount} سجل)";
            }
        }

        /// <summary>
        /// عرض رسالة إعلامية حول الفلترة إذا لزم الأمر
        /// </summary>
        private static void ShowFilterInfoIfNeeded(User currentUser, int recordCount)
        {
            if (EmployeeDepartmentHelper.IsDepartmentManager(currentUser))
            {
                string message = EmployeeDepartmentHelper.GetFilterWarningMessage(currentUser);
                message += $"\n\nعدد السجلات المعروضة: {recordCount}";
                
                // تم تعطيل الرسائل المزعجة - الفلترة تعمل تلقائياً
                // if (!HasShownFilterMessage(currentUser))
                // {
                //     MessageBox.Show(message, "معلومات الفلترة",
                //         MessageBoxButtons.OK, MessageBoxIcon.Information);
                //     MarkFilterMessageAsShown(currentUser);
                // }
            }
        }

        #endregion

        #region إدارة رسائل الفلترة

        private static readonly HashSet<int> _shownFilterMessages = new HashSet<int>();

        /// <summary>
        /// التحقق من عرض رسالة الفلترة مسبقاً
        /// </summary>
        private static bool HasShownFilterMessage(User currentUser)
        {
            return _shownFilterMessages.Contains(currentUser.UserId);
        }

        /// <summary>
        /// تسجيل عرض رسالة الفلترة
        /// </summary>
        private static void MarkFilterMessageAsShown(User currentUser)
        {
            _shownFilterMessages.Add(currentUser.UserId);
        }

        /// <summary>
        /// إعادة تعيين رسائل الفلترة (عند تسجيل دخول جديد)
        /// </summary>
        public static void ResetFilterMessages()
        {
            _shownFilterMessages.Clear();
        }

        #endregion

        #region دوال مساعدة للنماذج المختلفة

        /// <summary>
        /// تطبيق فلترة خاصة بنموذج الحضور والغياب
        /// </summary>
        public static void ApplyAttendanceFilter(AttendanceForm form, User? currentUser)
        {
            // سيتم تطبيق الفلترة داخل النموذج نفسه باستخدام EmployeeDepartmentHelper
        }

        /// <summary>
        /// تطبيق فلترة خاصة بنموذج الإجازات
        /// </summary>
        public static void ApplyVacationFilter(VacationForm form, User? currentUser)
        {
            // سيتم تطبيق الفلترة داخل النموذج نفسه باستخدام EmployeeDepartmentHelper
        }

        /// <summary>
        /// تطبيق فلترة خاصة بنموذج إدارة المستندات
        /// </summary>
        public static void ApplyDocumentFilter(Form form, User? currentUser, int employeeCode)
        {
            if (!EmployeeDepartmentHelper.ValidateUserAccess(currentUser, employeeCode))
            {
                MessageBox.Show("ليس لديك صلاحية للوصول لمستندات هذا الموظف", "غير مخول", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                form.Close();
            }
        }

        #endregion

        #region دوال التحقق من الصلاحيات

        /// <summary>
        /// التحقق من صلاحية إضافة سجل جديد
        /// </summary>
        public static bool CanAddRecord(User? currentUser, int? employeeCode = null)
        {
            if (currentUser == null) return false;

            // المدير العام يمكنه إضافة أي سجل
            if (EmployeeDepartmentHelper.IsGeneralManager(currentUser))
                return true;

            // مدير القسم يمكنه إضافة سجلات لموظفي قسمه فقط
            if (EmployeeDepartmentHelper.IsDepartmentManager(currentUser))
            {
                if (employeeCode.HasValue)
                {
                    return EmployeeDepartmentHelper.ValidateUserAccess(currentUser, employeeCode.Value);
                }
                // إذا لم يتم تحديد موظف، فيمكن المتابعة (سيتم التحقق لاحقاً)
                return true;
            }

            // المستخدم العادي يمكنه الإضافة (حسب السياسة الحالية)
            return true;
        }

        /// <summary>
        /// التحقق من صلاحية تعديل سجل
        /// </summary>
        public static bool CanEditRecord(User? currentUser, int employeeCode)
        {
            return EmployeeDepartmentHelper.ValidateUserAccess(currentUser, employeeCode);
        }

        /// <summary>
        /// التحقق من صلاحية حذف سجل
        /// </summary>
        public static bool CanDeleteRecord(User? currentUser, int employeeCode)
        {
            return EmployeeDepartmentHelper.ValidateUserAccess(currentUser, employeeCode);
        }

        #endregion
    }
}
