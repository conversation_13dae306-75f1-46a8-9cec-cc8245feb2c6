namespace EmployeeManagementSystem
{
    partial class ActivityLogViewerForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            pnlFilters = new Panel();
            lblFrom = new Label();
            dtpFrom = new DateTimePicker();
            lblTo = new Label();
            dtpTo = new DateTimePicker();
            lblActionType = new Label();
            cmbActionType = new ComboBox();
            lblPriority = new Label();
            cmbPriority = new ComboBox();
            lblStatus = new Label();
            cmbStatus = new ComboBox();
            lblSearch = new Label();
            txtSearch = new TextBox();
            btnRefresh = new Button();
            btnClear = new Button();
            btnExport = new Button();
            btnClose = new Button();
            btnDeleteSelected = new Button();
            btnDeleteAll = new Button();
            pnlMain = new Panel();
            dgvLogs = new DataGridView();
            statusStrip = new StatusStrip();
            lblTotalRecords = new ToolStripStatusLabel();
            lblFilteredRecords = new ToolStripStatusLabel();
            pnlFilters.SuspendLayout();
            pnlMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvLogs).BeginInit();
            statusStrip.SuspendLayout();
            SuspendLayout();
            // 
            // pnlFilters
            // 
            pnlFilters.BackColor = Color.FromArgb(248, 249, 250);
            pnlFilters.Controls.Add(lblFrom);
            pnlFilters.Controls.Add(dtpFrom);
            pnlFilters.Controls.Add(lblTo);
            pnlFilters.Controls.Add(dtpTo);
            pnlFilters.Controls.Add(lblActionType);
            pnlFilters.Controls.Add(cmbActionType);
            pnlFilters.Controls.Add(lblPriority);
            pnlFilters.Controls.Add(cmbPriority);
            pnlFilters.Controls.Add(lblStatus);
            pnlFilters.Controls.Add(cmbStatus);
            pnlFilters.Controls.Add(lblSearch);
            pnlFilters.Controls.Add(txtSearch);
            pnlFilters.Controls.Add(btnRefresh);
            pnlFilters.Controls.Add(btnClear);
            pnlFilters.Controls.Add(btnExport);
            pnlFilters.Controls.Add(btnClose);
            pnlFilters.Controls.Add(btnDeleteSelected);
            pnlFilters.Controls.Add(btnDeleteAll);
            pnlFilters.Dock = DockStyle.Top;
            pnlFilters.Location = new Point(0, 0);
            pnlFilters.Margin = new Padding(4, 3, 4, 3);
            pnlFilters.Name = "pnlFilters";
            pnlFilters.Padding = new Padding(12);
            pnlFilters.Size = new Size(1400, 138);
            pnlFilters.TabIndex = 0;
            // 
            // lblFrom
            // 
            lblFrom.AutoSize = true;
            lblFrom.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblFrom.Location = new Point(1147, 40);
            lblFrom.Margin = new Padding(4, 0, 4, 0);
            lblFrom.Name = "lblFrom";
            lblFrom.RightToLeft = RightToLeft.Yes;
            lblFrom.Size = new Size(30, 23);
            lblFrom.TabIndex = 0;
            lblFrom.Text = "من:";
            // 
            // dtpFrom
            // 
            dtpFrom.Font = new Font("Cairo", 8.249999F, FontStyle.Bold);
            dtpFrom.Format = DateTimePickerFormat.Short;
            dtpFrom.Location = new Point(931, 36);
            dtpFrom.Margin = new Padding(4, 3, 4, 3);
            dtpFrom.Name = "dtpFrom";
            dtpFrom.RightToLeft = RightToLeft.Yes;
            dtpFrom.RightToLeftLayout = true;
            dtpFrom.Size = new Size(209, 28);
            dtpFrom.TabIndex = 1;
            // 
            // lblTo
            // 
            lblTo.AutoSize = true;
            lblTo.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblTo.Location = new Point(890, 41);
            lblTo.Margin = new Padding(4, 0, 4, 0);
            lblTo.Name = "lblTo";
            lblTo.RightToLeft = RightToLeft.Yes;
            lblTo.Size = new Size(33, 23);
            lblTo.TabIndex = 2;
            lblTo.Text = "إلى:";
            // 
            // dtpTo
            // 
            dtpTo.Font = new Font("Cairo", 8.249999F, FontStyle.Bold);
            dtpTo.Format = DateTimePickerFormat.Short;
            dtpTo.Location = new Point(674, 37);
            dtpTo.Margin = new Padding(4, 3, 4, 3);
            dtpTo.Name = "dtpTo";
            dtpTo.RightToLeft = RightToLeft.Yes;
            dtpTo.RightToLeftLayout = true;
            dtpTo.Size = new Size(209, 28);
            dtpTo.TabIndex = 3;
            // 
            // lblActionType
            // 
            lblActionType.AutoSize = true;
            lblActionType.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblActionType.Location = new Point(581, 40);
            lblActionType.Margin = new Padding(4, 0, 4, 0);
            lblActionType.Name = "lblActionType";
            lblActionType.RightToLeft = RightToLeft.Yes;
            lblActionType.Size = new Size(76, 23);
            lblActionType.TabIndex = 4;
            lblActionType.Text = "نوع العملية:";
            // 
            // cmbActionType
            // 
            cmbActionType.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbActionType.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            cmbActionType.FormattingEnabled = true;
            cmbActionType.Items.AddRange(new object[] { "الكل", "إضافة", "تحديث", "حذف", "استعلام", "تسجيل دخول", "تسجيل خروج", "خطأ" });
            cmbActionType.Location = new Point(382, 37);
            cmbActionType.Margin = new Padding(4, 3, 4, 3);
            cmbActionType.Name = "cmbActionType";
            cmbActionType.RightToLeft = RightToLeft.Yes;
            cmbActionType.Size = new Size(191, 31);
            cmbActionType.TabIndex = 5;
            // 
            // lblPriority
            // 
            lblPriority.AutoSize = true;
            lblPriority.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblPriority.Location = new Point(312, 40);
            lblPriority.Margin = new Padding(4, 0, 4, 0);
            lblPriority.Name = "lblPriority";
            lblPriority.RightToLeft = RightToLeft.Yes;
            lblPriority.Size = new Size(56, 23);
            lblPriority.TabIndex = 6;
            lblPriority.Text = "الأولوية:";
            // 
            // cmbPriority
            // 
            cmbPriority.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbPriority.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            cmbPriority.FormattingEnabled = true;
            cmbPriority.Items.AddRange(new object[] { "الكل", "عادي", "مهم", "حرج" });
            cmbPriority.Location = new Point(149, 35);
            cmbPriority.Margin = new Padding(4, 3, 4, 3);
            cmbPriority.Name = "cmbPriority";
            cmbPriority.RightToLeft = RightToLeft.Yes;
            cmbPriority.Size = new Size(155, 31);
            cmbPriority.TabIndex = 7;
            // 
            // lblStatus
            // 
            lblStatus.AutoSize = true;
            lblStatus.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblStatus.Location = new Point(1238, 94);
            lblStatus.Margin = new Padding(4, 0, 4, 0);
            lblStatus.Name = "lblStatus";
            lblStatus.RightToLeft = RightToLeft.Yes;
            lblStatus.Size = new Size(44, 23);
            lblStatus.TabIndex = 8;
            lblStatus.Text = "الحالة:";
            // 
            // cmbStatus
            // 
            cmbStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbStatus.Font = new Font("Cairo", 8.999999F, FontStyle.Bold, GraphicsUnit.Point, 0);
            cmbStatus.FormattingEnabled = true;
            cmbStatus.Items.AddRange(new object[] { "الكل", "نجح", "فشل", "تحذير" });
            cmbStatus.Location = new Point(1063, 90);
            cmbStatus.Margin = new Padding(4, 3, 4, 3);
            cmbStatus.Name = "cmbStatus";
            cmbStatus.RightToLeft = RightToLeft.Yes;
            cmbStatus.Size = new Size(167, 31);
            cmbStatus.TabIndex = 9;
            // 
            // lblSearch
            // 
            lblSearch.AutoSize = true;
            lblSearch.Font = new Font("Cairo", 8.249999F, FontStyle.Bold);
            lblSearch.Location = new Point(1004, 94);
            lblSearch.Margin = new Padding(4, 0, 4, 0);
            lblSearch.Name = "lblSearch";
            lblSearch.RightToLeft = RightToLeft.Yes;
            lblSearch.Size = new Size(41, 20);
            lblSearch.TabIndex = 10;
            lblSearch.Text = "البحث:";
            // 
            // txtSearch
            // 
            txtSearch.Font = new Font("Cairo", 8.999999F);
            txtSearch.Location = new Point(661, 89);
            txtSearch.Margin = new Padding(4, 3, 4, 3);
            txtSearch.Name = "txtSearch";
            txtSearch.RightToLeft = RightToLeft.Yes;
            txtSearch.Size = new Size(335, 30);
            txtSearch.TabIndex = 11;
            // 
            // btnRefresh
            // 
            btnRefresh.BackColor = Color.FromArgb(76, 175, 80);
            btnRefresh.FlatStyle = FlatStyle.Flat;
            btnRefresh.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnRefresh.ForeColor = Color.White;
            btnRefresh.Location = new Point(546, 84);
            btnRefresh.Margin = new Padding(4, 3, 4, 3);
            btnRefresh.Name = "btnRefresh";
            btnRefresh.Size = new Size(93, 33);
            btnRefresh.TabIndex = 12;
            btnRefresh.Text = "تحديث";
            btnRefresh.UseVisualStyleBackColor = false;
            btnRefresh.Click += BtnRefresh_Click;
            // 
            // btnClear
            // 
            btnClear.BackColor = Color.FromArgb(255, 193, 7);
            btnClear.FlatStyle = FlatStyle.Flat;
            btnClear.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnClear.ForeColor = Color.White;
            btnClear.Location = new Point(445, 84);
            btnClear.Margin = new Padding(4, 3, 4, 3);
            btnClear.Name = "btnClear";
            btnClear.Size = new Size(93, 33);
            btnClear.TabIndex = 13;
            btnClear.Text = "مسح";
            btnClear.UseVisualStyleBackColor = false;
            btnClear.Click += BtnClear_Click;
            // 
            // btnExport
            // 
            btnExport.BackColor = Color.FromArgb(33, 150, 243);
            btnExport.FlatStyle = FlatStyle.Flat;
            btnExport.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnExport.ForeColor = Color.White;
            btnExport.Location = new Point(343, 84);
            btnExport.Margin = new Padding(4, 3, 4, 3);
            btnExport.Name = "btnExport";
            btnExport.Size = new Size(93, 33);
            btnExport.TabIndex = 14;
            btnExport.Text = "تصدير";
            btnExport.UseVisualStyleBackColor = false;
            btnExport.Click += BtnExport_Click;
            // 
            // btnClose
            // 
            btnClose.BackColor = Color.FromArgb(96, 125, 139);
            btnClose.FlatStyle = FlatStyle.Flat;
            btnClose.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnClose.ForeColor = Color.White;
            btnClose.Location = new Point(242, 84);
            btnClose.Margin = new Padding(4, 3, 4, 3);
            btnClose.Name = "btnClose";
            btnClose.Size = new Size(93, 33);
            btnClose.TabIndex = 15;
            btnClose.Text = "إغلاق";
            btnClose.UseVisualStyleBackColor = false;
            btnClose.Click += BtnClose_Click;
            // 
            // btnDeleteSelected
            // 
            btnDeleteSelected.BackColor = Color.FromArgb(244, 67, 54);
            btnDeleteSelected.FlatStyle = FlatStyle.Flat;
            btnDeleteSelected.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnDeleteSelected.ForeColor = Color.White;
            btnDeleteSelected.Location = new Point(141, 84);
            btnDeleteSelected.Margin = new Padding(4, 3, 4, 3);
            btnDeleteSelected.Name = "btnDeleteSelected";
            btnDeleteSelected.Size = new Size(93, 33);
            btnDeleteSelected.TabIndex = 16;
            btnDeleteSelected.Text = "حذف المحدد";
            btnDeleteSelected.UseVisualStyleBackColor = false;
            btnDeleteSelected.Click += BtnDeleteSelected_Click;
            // 
            // btnDeleteAll
            // 
            btnDeleteAll.BackColor = Color.FromArgb(183, 28, 28);
            btnDeleteAll.FlatStyle = FlatStyle.Flat;
            btnDeleteAll.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnDeleteAll.ForeColor = Color.White;
            btnDeleteAll.Location = new Point(40, 84);
            btnDeleteAll.Margin = new Padding(4, 3, 4, 3);
            btnDeleteAll.Name = "btnDeleteAll";
            btnDeleteAll.Size = new Size(93, 33);
            btnDeleteAll.TabIndex = 17;
            btnDeleteAll.Text = "حذف الكل";
            btnDeleteAll.UseVisualStyleBackColor = false;
            btnDeleteAll.Click += BtnDeleteAll_Click;
            // 
            // pnlMain
            // 
            pnlMain.Controls.Add(dgvLogs);
            pnlMain.Dock = DockStyle.Fill;
            pnlMain.Location = new Point(0, 138);
            pnlMain.Margin = new Padding(4, 3, 4, 3);
            pnlMain.Name = "pnlMain";
            pnlMain.Padding = new Padding(12);
            pnlMain.Size = new Size(1400, 609);
            pnlMain.TabIndex = 1;
            // 
            // dgvLogs
            // 
            dgvLogs.AllowUserToAddRows = false;
            dgvLogs.AllowUserToDeleteRows = false;
            dgvLogs.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvLogs.BackgroundColor = Color.White;
            dgvLogs.BorderStyle = BorderStyle.None;
            dataGridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle1.BackColor = SystemColors.Control;
            dataGridViewCellStyle1.Font = new Font("Cairo", 8.999999F, FontStyle.Regular, GraphicsUnit.Point, 0);
            dataGridViewCellStyle1.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = DataGridViewTriState.True;
            dgvLogs.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            dgvLogs.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle2.BackColor = SystemColors.Window;
            dataGridViewCellStyle2.Font = new Font("Cairo", 8.999999F, FontStyle.Regular, GraphicsUnit.Point, 0);
            dataGridViewCellStyle2.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.False;
            dgvLogs.DefaultCellStyle = dataGridViewCellStyle2;
            dgvLogs.Dock = DockStyle.Fill;
            dgvLogs.GridColor = Color.FromArgb(224, 224, 224);
            dgvLogs.Location = new Point(12, 12);
            dgvLogs.Margin = new Padding(4, 3, 4, 3);
            dgvLogs.MultiSelect = false;
            dgvLogs.Name = "dgvLogs";
            dgvLogs.ReadOnly = true;
            dgvLogs.RightToLeft = RightToLeft.Yes;
            dataGridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle3.BackColor = SystemColors.Control;
            dataGridViewCellStyle3.Font = new Font("Cairo", 8.999999F, FontStyle.Regular, GraphicsUnit.Point, 0);
            dataGridViewCellStyle3.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle3.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle3.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = DataGridViewTriState.True;
            dgvLogs.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            dgvLogs.RowHeadersVisible = false;
            dgvLogs.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvLogs.Size = new Size(1376, 585);
            dgvLogs.TabIndex = 0;
            dgvLogs.CellDoubleClick += DgvLogs_CellDoubleClick;
            // 
            // statusStrip
            // 
            statusStrip.Items.AddRange(new ToolStripItem[] { lblTotalRecords, lblFilteredRecords });
            statusStrip.Location = new Point(0, 747);
            statusStrip.Name = "statusStrip";
            statusStrip.Padding = new Padding(16, 0, 1, 0);
            statusStrip.RightToLeft = RightToLeft.Yes;
            statusStrip.Size = new Size(1400, 28);
            statusStrip.TabIndex = 2;
            statusStrip.Text = "statusStrip1";
            // 
            // lblTotalRecords
            // 
            lblTotalRecords.Font = new Font("Cairo", 8.999999F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lblTotalRecords.Name = "lblTotalRecords";
            lblTotalRecords.Size = new Size(99, 23);
            lblTotalRecords.Text = "إجمالي السجلات: 0";
            // 
            // lblFilteredRecords
            // 
            lblFilteredRecords.Font = new Font("Cairo", 8.999999F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lblFilteredRecords.Name = "lblFilteredRecords";
            lblFilteredRecords.Size = new Size(107, 23);
            lblFilteredRecords.Text = "السجلات المفلترة: 0";
            // 
            // ActivityLogViewerForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1400, 775);
            Controls.Add(pnlMain);
            Controls.Add(statusStrip);
            Controls.Add(pnlFilters);
            Margin = new Padding(4, 3, 4, 3);
            MinimumSize = new Size(1164, 686);
            Name = "ActivityLogViewerForm";
            RightToLeft = RightToLeft.No;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterParent;
            Text = "مراقب سجلات الأنشطة";
            Load += ActivityLogViewerForm_Load;
            pnlFilters.ResumeLayout(false);
            pnlFilters.PerformLayout();
            pnlMain.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dgvLogs).EndInit();
            statusStrip.ResumeLayout(false);
            statusStrip.PerformLayout();
            ResumeLayout(false);
            PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Panel pnlFilters;
        private System.Windows.Forms.Label lblFrom;
        private System.Windows.Forms.DateTimePicker dtpFrom;
        private System.Windows.Forms.Label lblTo;
        private System.Windows.Forms.DateTimePicker dtpTo;
        private System.Windows.Forms.Label lblActionType;
        private System.Windows.Forms.ComboBox cmbActionType;
        private System.Windows.Forms.Label lblPriority;
        private System.Windows.Forms.ComboBox cmbPriority;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.ComboBox cmbStatus;
        private System.Windows.Forms.Label lblSearch;
        private System.Windows.Forms.TextBox txtSearch;
        private System.Windows.Forms.Button btnRefresh;
        private System.Windows.Forms.Button btnClear;
        private System.Windows.Forms.Button btnExport;
        private System.Windows.Forms.Button btnClose;
        private System.Windows.Forms.Button btnDeleteSelected;
        private System.Windows.Forms.Button btnDeleteAll;
        private System.Windows.Forms.Panel pnlMain;
        private System.Windows.Forms.DataGridView dgvLogs;
        private System.Windows.Forms.StatusStrip statusStrip;
        private System.Windows.Forms.ToolStripStatusLabel lblTotalRecords;
        private System.Windows.Forms.ToolStripStatusLabel lblFilteredRecords;
    }
}