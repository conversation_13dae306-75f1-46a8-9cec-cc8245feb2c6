using System.Data;
using System.Data.SqlClient; // غيرت من SQLite إلى SqlClient
using EmployeeManagementSystem.Properties;
using System.Diagnostics;
using System.Configuration;
using System.IO.Compression;
using System.Security.Principal;


namespace EmployeeManagementSystem
{
    public partial class SettingsForm : Form
    {
        private string logoPath = "";
        private Image defaultLogo; // لحفظ الصورة الافتراضية

        public SettingsForm()
        {
            InitializeComponent();

            // حفظ الصورة الافتراضية في المتغير
            defaultLogo = Properties.Resources.picture;

           
            ThemeManager.ApplyThemeToForm(this);
        }
        private void LoadSettings()
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    conn.Open();

                    string query = @"
                SELECT TOP 1 CompanyName, CompanyDes, CompanyLogo, Theme 
                FROM Settings";

                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        string defaultName = Settings.Default.CompanyName;
                        string defaultDes = Settings.Default.CompanyDes;

                        if (reader.Read())
                        {
                            string? name = reader["CompanyName"]?.ToString();
                            string? des = reader["CompanyDes"]?.ToString();
                            string? logoPath = reader["CompanyLogo"]?.ToString();
                            string? theme = reader["Theme"]?.ToString()?.ToLower();

                            reader.Close(); // ضروري قبل أي أمر SQL جديد

                            // إذا كانت الحقول فارغة، نحفظ القيم من Settings
                            if (string.IsNullOrWhiteSpace(name) || string.IsNullOrWhiteSpace(des))
                            {
                                using SqlCommand update = new SqlCommand(@"
                            UPDATE Settings 
                            SET CompanyName = @name, CompanyDes = @des", conn);

                                update.Parameters.AddWithValue("@name", defaultName);
                                update.Parameters.AddWithValue("@des", defaultDes);
                                update.ExecuteNonQuery();

                                txtOrganizationName.Text = defaultName;
                                txtDescription.Text = defaultDes;
                            }
                            else
                            {
                                txtOrganizationName.Text = name;
                                txtDescription.Text = des;
                            }

                            // تحميل الشعار
                            if (!string.IsNullOrWhiteSpace(logoPath) && File.Exists(logoPath))
                            {
                                try
                                {
                                    using var stream = new FileStream(logoPath, FileMode.Open, FileAccess.Read);
                                    pictureBoxLogo.Image = Image.FromStream(stream);
                                    this.logoPath = logoPath; // تحديث متغير logoPath
                                }
                                catch
                                {
                                    pictureBoxLogo.Image = defaultLogo;
                                    this.logoPath = ""; // إعادة تعيين logoPath في حالة الخطأ
                                }
                            }
                            else
                            {
                                pictureBoxLogo.Image = defaultLogo;
                                this.logoPath = ""; // إعادة تعيين logoPath
                            }

                            // تحميل الثيم
                            cmbTheme.SelectedIndex = theme switch
                            {
                                "light" => 1,
                                "dark" => 2,
                                _ => 0
                            };
                        }
                        else
                        {
                            reader.Close(); // مهم جدًا

                            using SqlCommand insert = new SqlCommand(@"
                        INSERT INTO Settings (CompanyName, CompanyDes)
                        VALUES (@name, @des)", conn);

                            insert.Parameters.AddWithValue("@name", defaultName);
                            insert.Parameters.AddWithValue("@des", defaultDes);
                            insert.ExecuteNonQuery();

                            txtOrganizationName.Text = defaultName;
                            txtDescription.Text = defaultDes;
                            pictureBoxLogo.Image = defaultLogo;
                            this.logoPath = ""; // إعادة تعيين logoPath
                            cmbTheme.SelectedIndex = 0;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                pictureBoxLogo.Image = defaultLogo;
                this.logoPath = ""; // إعادة تعيين logoPath في حالة الخطأ
                MessageBox.Show("فشل تحميل الإعدادات من القاعدة:\n" + ex.Message);
            }
        }


        //private void LoadSettings()
        //{
        //    try
        //    {
        //        using (SqlConnection conn = new SqlConnection(ConStringHelper.GetConnectionString()))
        //        {
        //            conn.Open();
        //            string query = "SELECT TOP 1 CompanyName, CompanyDes, CompanyLogo, Theme FROM Settings";
        //            using (SqlCommand cmd = new SqlCommand(query, conn))
        //            using (SqlDataReader reader = cmd.ExecuteReader())
        //            {
        //                if (reader.Read())
        //                {


        //                    txtOrganizationName.Text = reader["CompanyName"]?.ToString();
        //                    txtDescription.Text = reader["CompanyDes"]?.ToString();

        //                    this.txtOrganizationName.Text = Settings.Default.CompanyName;
        //                    this.txtDescription.Text = Settings.Default.CompanyDes;

        //                    string logoPath = reader["CompanyLogo"]?.ToString() ?? string.Empty;

        //                    if (!string.IsNullOrEmpty(logoPath) && File.Exists(logoPath))
        //                    {
        //                        try
        //                        {
        //                            using var stream = new FileStream(logoPath, FileMode.Open, FileAccess.Read);
        //                            pictureBoxLogo.Image = Image.FromStream(stream);
        //                        }
        //                        catch
        //                        {
        //                            // إذا فشل تحميل الصورة، نستخدم الصورة الافتراضية
        //                            pictureBoxLogo.Image = defaultLogo;
        //                        }
        //                    }
        //                    else
        //                    {
        //                        // إذا لم تكن هناك صورة محفوظة، نستخدم الصورة الافتراضية
        //                        pictureBoxLogo.Image = defaultLogo;
        //                    }

        //                    string theme = reader["Theme"]?.ToString()?.ToLower() ?? "default";
        //                    switch (theme)
        //                    {
        //                        case "light":
        //                            cmbTheme.SelectedIndex = 1;
        //                            break;
        //                        case "dark":
        //                            cmbTheme.SelectedIndex = 2;
        //                            break;
        //                        default:
        //                            cmbTheme.SelectedIndex = 0;
        //                            break;
        //                    }
        //                }
        //                else
        //                {
        //                    // إذا لم توجد إعدادات محفوظة، نستخدم الصورة الافتراضية
        //                    pictureBoxLogo.Image = defaultLogo;
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        // في حالة حدوث خطأ، نستخدم الصورة الافتراضية
        //        pictureBoxLogo.Image = defaultLogo;
        //        MessageBox.Show("فشل تحميل الإعدادات من القاعدة:\n" + ex.Message);
        //    }
        //}



        private void UpdateSettings(string companyName, string companyDes, string logo, string theme, string lang)
        {
            try
            {
                using (SqlConnection conn = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    string query = @"
         IF EXISTS (SELECT 1 FROM Settings)
             UPDATE Settings 
             SET CompanyName = @CompanyName, CompanyDes = @CompanyDes, CompanyLogo = @CompanyLogo, Theme = @Theme, Language = @Language
         ELSE
             INSERT INTO Settings (CompanyName, CompanyDes, CompanyLogo, Theme, Language)
             VALUES (@CompanyName, @CompanyDes, @CompanyLogo, @Theme, @Language)";
                    using (SqlCommand cmd = new SqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@CompanyName", companyName);
                        cmd.Parameters.AddWithValue("@CompanyDes", companyDes);
                        cmd.Parameters.AddWithValue("@CompanyLogo", (object)logo ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Theme", theme);
                        cmd.Parameters.AddWithValue("@Language", lang);
                        conn.Open();
                        cmd.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث الإعدادات في قاعدة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        private void ApplyTheme(string theme)
        {
            ThemeManager.ApplyTheme(theme);
            ThemeManager.ApplyThemeToForm(this);

            if (Application.OpenForms["MainForm"] is Form mainForm)
            {
                ThemeManager.ApplyThemeToForm(mainForm);
            }

            foreach (Form form in Application.OpenForms)
            {
                if (form != this && form != Application.OpenForms["MainForm"])
                {
                    ThemeManager.ApplyThemeToForm(form);
                }
            }
        }

        private void btnSelectLogo_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "صور|*.jpg;*.jpeg;*.png;*.bmp";
                openFileDialog.Title = "اختر شعار المؤسسة";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string selectedFile = openFileDialog.FileName;
                    string destinationPath = Path.Combine(Application.StartupPath, "images",
                        $"logo_{Path.GetFileName(selectedFile)}");

                    Directory.CreateDirectory(Path.Combine(Application.StartupPath, "images"));
                    File.Copy(selectedFile, destinationPath, true);

                    logoPath = destinationPath;
                    using (var stream = new FileStream(logoPath, FileMode.Open, FileAccess.Read))
                    {
                        pictureBoxLogo.Image = Image.FromStream(stream);
                    }
                }
            }
        }

        private void btnBackup_Click(object sender, EventArgs e)
        {
            try
            {
                using (FolderBrowserDialog folder = new FolderBrowserDialog())
                {
                    folder.Description = "اختر مكان حفظ النسخة الاحتياطية";

                    if (folder.ShowDialog() == DialogResult.OK)
                    {
                        // اسم قاعدة البيانات ومسار النسخ الاحتياطي
                        string dbname = "HRMSDB"; // أو استخدم db.Database.Connection.Database إذا كنت تستخدم Entity Framework
                        string dbBackUp = "HRMSDB_" + DateTime.Now.ToString("yyyyMMddHHmm");
                        string fullpath = Path.Combine(folder.SelectedPath, dbBackUp + ".bak");

                        // صيغة النسخ الاحتياطي
                        string sqlCommand = $@"
                BACKUP DATABASE [{dbname}]
                TO DISK = N'{fullpath}'
                WITH FORMAT, INIT, NAME = N'HRMSDB Backup', SKIP, NOREWIND, NOUNLOAD, STATS = 10";

                        // تنفيذ النسخ الاحتياطي
                        using (SqlConnection connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                        {
                            connection.Open();
                            using (SqlCommand command = new SqlCommand(sqlCommand, connection))
                            {
                                command.ExecuteNonQuery();
                            }
                        }

                        // نسخ مجلد المستندات إلى نفس المكان
                        string documentsDir = Path.Combine(Application.StartupPath, "Documents");
                        string documentsBackupPath = Path.Combine(folder.SelectedPath, "Documents_Backup_" + DateTime.Now.ToString("yyyyMMddHHmm"));

                        CopyDirectory(documentsDir, documentsBackupPath);

                        MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح!", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnRestore_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق مما إذا كان البرنامج يعمل كمسؤول
                if (!IsRunningAsAdministrator())
                {
                    RestartAsAdministrator();
                    return;
                }

                using (OpenFileDialog openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Filter = "ملفات النسخ الاحتياطي|*.bak";
                    openFileDialog.Title = "اختر ملف النسخة الاحتياطية للاستعادة";

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        string backupPath = openFileDialog.FileName;
                        string dbname = "HRMSDB";

                        // أوامر SQL للاستعادة
                        string sqlCommand = $@"
                USE master;
                ALTER DATABASE [{dbname}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
                RESTORE DATABASE [{dbname}]
                FROM DISK = N'{backupPath}'
                WITH REPLACE, RECOVERY, 
                STATS = 10;
                ALTER DATABASE [{dbname}] SET MULTI_USER;";

                        // تنفيذ الاستعادة
                        using (SqlConnection connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                        {
                            connection.Open();
                            using (SqlCommand command = new SqlCommand(sqlCommand, connection))
                            {
                                command.ExecuteNonQuery();
                            }
                        }

                        string? backupDir = Path.GetDirectoryName(backupPath);
                        if (string.IsNullOrEmpty(backupDir))
                        {
                            // تعامل مع الحالة — مثلاً عرض رسالة خطأ أو الخروج من الدالة
                            MessageBox.Show("مسار النسخة الاحتياطية غير صالح.", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }

                        string? documentsBackupDir = Directory.GetDirectories(backupDir, "Documents_Backup_*")
                                                              .OrderByDescending(d => d)
                                                              .FirstOrDefault();


                        if (documentsBackupDir != null)
                        {
                            string documentsDir = Path.Combine(Application.StartupPath, "Documents");

                            // حذف المجلد القديم إذا كان موجوداً
                            if (Directory.Exists(documentsDir))
                            {
                                Directory.Delete(documentsDir, true);
                            }

                            // نسخ المجلد الجديد
                            CopyDirectory(documentsBackupDir, documentsDir);
                        }

                        MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح!\nسيتم إعادة تشغيل البرنامج.", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        Application.Restart();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء استعادة النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // الدوال المساعدة (تبقى كما هي)
        private void CopyDirectory(string sourceDir, string destinationDir)
        {
            var dir = new DirectoryInfo(sourceDir);

            if (!dir.Exists)
                throw new DirectoryNotFoundException($"المجلد المصدري غير موجود: {dir.FullName}");

            Directory.CreateDirectory(destinationDir);

            foreach (FileInfo file in dir.GetFiles())
            {
                string targetFilePath = Path.Combine(destinationDir, file.Name);
                file.CopyTo(targetFilePath);
            }

            foreach (DirectoryInfo subDir in dir.GetDirectories())
            {
                string newDestinationDir = Path.Combine(destinationDir, subDir.Name);
                CopyDirectory(subDir.FullName, newDestinationDir);
            }
        }

        private bool IsRunningAsAdministrator()
        {
            var identity = WindowsIdentity.GetCurrent();
            var principal = new WindowsPrincipal(identity);
            return principal.IsInRole(WindowsBuiltInRole.Administrator);
        }

        private void RestartAsAdministrator()
        {
            var startInfo = new ProcessStartInfo
            {
                FileName = Application.ExecutablePath,
                UseShellExecute = true,
                Verb = "runas"
            };

            try
            {
                Process.Start(startInfo);
                Application.Exit();
            }
            catch
            {
                MessageBox.Show("لم يتم تشغيل البرنامج كمسؤول. لا يمكن استعادة النسخة الاحتياطية بدون صلاحيات المسؤول.", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }


        private void btnSave_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtOrganizationName.Text))
            {
                MessageBox.Show("الرجاء إدخال اسم المؤسسة", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            //SaveSettings();
            SaveGenralSettings();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            txtOrganizationName.Text = "اسم المؤسسة";
            txtDescription.Text = "وصف المؤسسة";

            // إعادة تحميل الصورة الافتراضية
            pictureBoxLogo.Image = defaultLogo;

            logoPath = "";
            cmbTheme.SelectedIndex = 0;
        }

        private void btn_update_Click(object sender, EventArgs e)
        {
            try
            {
                if (!System.Net.NetworkInformation.NetworkInterface.GetIsNetworkAvailable())
                {
                    MessageBox.Show("لا يوجد اتصال بالإنترنت. يرجى التحقق من اتصال الشبكة والمحاولة مرة أخرى.",
                                    "خطأ في الاتصال", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                string updaterPath = Path.Combine(Application.StartupPath, "HRMSDBUpdater.exe");

                if (File.Exists(updaterPath))
                {
                    using (Form loadingForm = new Form
                    {
                        Text = "جاري... التحقق من التحديثات",
                        Size = new Size(400, 100),
                        StartPosition = FormStartPosition.CenterScreen,
                        FormBorderStyle = FormBorderStyle.FixedDialog,
                        MaximizeBox = false,
                        MinimizeBox = false,
                        TopMost = true
                    })
                    {
                        Label lblMessage = new Label
                        {
                            Text = "جاري... البحث عن تحديثات، يرجى الانتظار",
                            AutoSize = false,
                            Dock = DockStyle.Fill,
                            TextAlign = ContentAlignment.MiddleCenter,
                            Font = new Font("Arial", 12, FontStyle.Bold)
                        };

                        loadingForm.Controls.Add(lblMessage);
                        loadingForm.Show();
                        loadingForm.Refresh();

                        System.Threading.Thread.Sleep(2000);

                        bool isUpdateAvailable = true;

                        loadingForm.Close();

                        if (isUpdateAvailable)
                        {
                            ProcessStartInfo startInfo = new ProcessStartInfo
                            {
                                FileName = updaterPath,
                                UseShellExecute = true,
                                WindowStyle = ProcessWindowStyle.Normal
                            };

                            Process.Start(startInfo);

                            this.Hide();
                        }
                        else
                        {
                            MessageBox.Show("برنامجك محدث بالفعل. لا توجد تحديثات جديدة.",
                                            "التحديث", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على برنامج التحديث! تأكد من وجود الملف 'HRMSDBUpdater.exe' في مجلد المشروع.",
                                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء محاولة تشغيل برنامج التحديث:\n{ex.Message}",
                                "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnBackupDocuments_Click(object sender, EventArgs e)
        {

        }


        private void btnRestoreDocuments_Click(object sender, EventArgs e)
        {

        }
        private void SaveGenralSettings()
        {

            try
            {
               
                // Set Settings
                Settings.Default.CompanyName = txtOrganizationName.Text;
                Settings.Default.CompanyDes = txtDescription.Text;
                Settings.Default.Timeout = edt_timeout.Text;

                // Save
                Settings.Default.Save();

                //MessageBox.Show("تم حفظ الاعدادات");

                string theme = "default";
                switch (cmbTheme.SelectedIndex)
                {
                    case 1:
                        theme = "light";
                        break;
                    case 2:
                        theme = "dark";
                        break;
                }

                UpdateSettings(
                    txtOrganizationName.Text,
                    txtDescription.Text, 
                    logoPath,
                    theme,
                    "ar" // اللغة العربية كقيمة افتراضية
                );
                ToastHelper.ShowSettingsToast();
                MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجاح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                ApplyTheme(theme);
                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        public static void SaveGeneralSettings(string companyName, string companyDes, string timeout)
        {
            Settings.Default.CompanyName = companyName;
            Settings.Default.CompanyDes = companyDes;
            Settings.Default.Timeout = timeout;
            Settings.Default.Save();
        }
        private void GetSettings()
        {
           
            // General Settings
            txtOrganizationName.Text = Settings.Default.CompanyName;
            txtDescription.Text = Settings.Default.CompanyDes;
            edt_timeout.Text = Settings.Default.Timeout;  // يجب قراءة القيمة من الإعداد وتعبئة الحقل

            // Connection Settings
            if (Settings.Default.ServerType == "Local")
            {
                radioButtonLocal.Checked = true;
                edt_username.Enabled = false;
                edt_password.Enabled = false;
                edt_timeout.Enabled = false;
            }
            else
            {
                radioButtonNetwork.Checked = true;
                edt_username.Enabled = true;
                edt_password.Enabled = true;
                edt_timeout.Enabled = true;
            }

            // تعيين قيم الحقول من الإعدادات (القراءة وليس الكتابة)
            edt_servername.Text = Settings.Default.Server;
            edt_database.Text = Settings.Default.DataBase;
            edt_username.Text = Settings.Default.AUserName;
            edt_password.Text = Settings.Default.APassword;
        }


        private void btn_saveconstring_Click(object sender, EventArgs e)
        {
            if (radioButtonLocal.Checked)
            {
                Settings.Default.ServerType = "Local";
            }
            else
            {
                Settings.Default.ServerType = "network";
            }
            Settings.Default.Server = edt_servername.Text;
            Settings.Default.DataBase = edt_database.Text;
            Settings.Default.AUserName = edt_username.Text;
            Settings.Default.APassword = edt_password.Text;
            Settings.Default.Timeout = edt_timeout.Text;

            Settings.Default.Save();

            MessageBox.Show("تم حفظ الاعدادات");
            Application.Restart(); // لإعادة تحميل الإعدادات بعد الحفظ
        }

        private void radioButtonLocal_CheckedChanged_1(object sender, EventArgs e)
        {
            if (radioButtonLocal.Checked)
            {
                edt_username.Enabled = false;
                edt_password.Enabled = false;
                edt_timeout.Enabled = false;
            }
        }

        private void radioButtonNetwork_CheckedChanged_1(object sender, EventArgs e)
        {
            if (radioButtonNetwork.Checked)
            {
                edt_username.Enabled = true;
                edt_password.Enabled = true;
                edt_timeout.Enabled = true;
            }
        }

        private void SettingsForm_Load(object sender, EventArgs e)
        {
            GetSettings();
            LoadSettings();
            
        }
    }
}
