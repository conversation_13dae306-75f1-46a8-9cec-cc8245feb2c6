namespace EmployeeManagementSystem
{
    partial class WorkPeriodForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            groupBoxWorkPeriod = new GroupBox();
            numericUpDownDailyHours = new NumericUpDown();
            lblDailyHours = new Label();
            cmbStatus = new ComboBox();
            lblStatus = new Label();
            groupBoxCalendar = new GroupBox();
            monthCalendar = new MonthCalendar();
            lblCalendarTitle = new Label();
            groupBoxWorkingDays = new GroupBox();
            chkSaturday = new CheckBox();
            chkFriday = new CheckBox();
            chkThursday = new CheckBox();
            chkWednesday = new CheckBox();
            chkTuesday = new CheckBox();
            chkMonday = new CheckBox();
            chkSunday = new CheckBox();
            dateTimePickerEnd = new DateTimePicker();
            dateTimePickerStart = new DateTimePicker();
            lblEndDate = new Label();
            lblStartDate = new Label();
            txtDescription = new TextBox();
            lblDescription = new Label();
            txtProjectName = new TextBox();
            lblProjectName = new Label();
            cmbEmployee = new ComboBox();
            lblEmployee = new Label();
            groupBoxActions = new GroupBox();
            btnClear = new Button();
            btnDelete = new Button();
            btnUpdate = new Button();
            btnAdd = new Button();
            groupBoxWorkPeriodList = new GroupBox();
            lbl_NoDocuments = new Label();
            btnRefresh = new Button();
            btnDailyTracking = new Button();
            chkSelectAll = new CheckBox();
            btnDeleteSelected = new Button();
            cmbSearchGroup = new ComboBox();
            lblSearchGroup = new Label();
            dateTimePickerSearchStart = new DateTimePicker();
            dateTimePickerSearchEnd = new DateTimePicker();
            lblSearchPeriod = new Label();
            btnAdvancedSearch = new Button();
            btnSearch = new Button();
            txtSearch = new TextBox();
            dataGridViewWorkPeriods = new DataGridView();
            groupBoxSummary = new GroupBox();
            lblTotalHours = new Label();
            lblTotalDays = new Label();
            lblSelectedPeriod = new Label();
            groupBoxWorkPeriod.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDownDailyHours).BeginInit();
            groupBoxCalendar.SuspendLayout();
            groupBoxWorkingDays.SuspendLayout();
            groupBoxActions.SuspendLayout();
            groupBoxWorkPeriodList.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridViewWorkPeriods).BeginInit();
            groupBoxSummary.SuspendLayout();
            SuspendLayout();
            // 
            // groupBoxWorkPeriod
            // 
            groupBoxWorkPeriod.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxWorkPeriod.Controls.Add(numericUpDownDailyHours);
            groupBoxWorkPeriod.Controls.Add(lblDailyHours);
            groupBoxWorkPeriod.Controls.Add(cmbStatus);
            groupBoxWorkPeriod.Controls.Add(lblStatus);
            groupBoxWorkPeriod.Controls.Add(groupBoxCalendar);
            groupBoxWorkPeriod.Controls.Add(groupBoxWorkingDays);
            groupBoxWorkPeriod.Controls.Add(dateTimePickerEnd);
            groupBoxWorkPeriod.Controls.Add(dateTimePickerStart);
            groupBoxWorkPeriod.Controls.Add(lblEndDate);
            groupBoxWorkPeriod.Controls.Add(lblStartDate);
            groupBoxWorkPeriod.Controls.Add(txtDescription);
            groupBoxWorkPeriod.Controls.Add(lblDescription);
            groupBoxWorkPeriod.Controls.Add(txtProjectName);
            groupBoxWorkPeriod.Controls.Add(lblProjectName);
            groupBoxWorkPeriod.Controls.Add(cmbEmployee);
            groupBoxWorkPeriod.Controls.Add(lblEmployee);
            groupBoxWorkPeriod.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxWorkPeriod.Location = new Point(12, 12);
            groupBoxWorkPeriod.Name = "groupBoxWorkPeriod";
            groupBoxWorkPeriod.RightToLeft = RightToLeft.Yes;
            groupBoxWorkPeriod.Size = new Size(1160, 280);
            groupBoxWorkPeriod.TabIndex = 0;
            groupBoxWorkPeriod.TabStop = false;
            groupBoxWorkPeriod.Text = "إدارة فترات العمل";
            // 
            // numericUpDownDailyHours
            // 
            numericUpDownDailyHours.DecimalPlaces = 1;
            numericUpDownDailyHours.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            numericUpDownDailyHours.Location = new Point(279, 241);
            numericUpDownDailyHours.Maximum = new decimal(new int[] { 24, 0, 0, 0 });
            numericUpDownDailyHours.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numericUpDownDailyHours.Name = "numericUpDownDailyHours";
            numericUpDownDailyHours.Size = new Size(130, 32);
            numericUpDownDailyHours.TabIndex = 14;
            numericUpDownDailyHours.Value = new decimal(new int[] { 8, 0, 0, 0 });
            // 
            // lblDailyHours
            // 
            lblDailyHours.Font = new Font("Cairo", 9.75F);
            lblDailyHours.Image = Properties.Resources.time_32px;
            lblDailyHours.ImageAlign = ContentAlignment.MiddleRight;
            lblDailyHours.Location = new Point(414, 241);
            lblDailyHours.Name = "lblDailyHours";
            lblDailyHours.Size = new Size(157, 32);
            lblDailyHours.TabIndex = 13;
            lblDailyHours.Text = "ساعات العمل اليومية:";
            lblDailyHours.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // cmbStatus
            // 
            cmbStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbStatus.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            cmbStatus.FormattingEnabled = true;
            cmbStatus.Items.AddRange(new object[] { "نشط", "مكتمل", "ملغي", "لم يبدأ", "حضور", "غياب", "إجازة", "هروب" });
            cmbStatus.Location = new Point(279, 36);
            cmbStatus.Name = "cmbStatus";
            cmbStatus.Size = new Size(243, 32);
            cmbStatus.TabIndex = 12;
            // 
            // lblStatus
            // 
            lblStatus.AutoSize = true;
            lblStatus.Font = new Font("Cairo", 9.75F);
            lblStatus.Location = new Point(528, 40);
            lblStatus.Name = "lblStatus";
            lblStatus.Size = new Size(43, 24);
            lblStatus.TabIndex = 11;
            lblStatus.Text = "الحالة:";
            // 
            // groupBoxCalendar
            // 
            groupBoxCalendar.Controls.Add(monthCalendar);
            groupBoxCalendar.Controls.Add(lblCalendarTitle);
            groupBoxCalendar.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxCalendar.Location = new Point(12, 55);
            groupBoxCalendar.Name = "groupBoxCalendar";
            groupBoxCalendar.RightToLeft = RightToLeft.Yes;
            groupBoxCalendar.Size = new Size(250, 214);
            groupBoxCalendar.TabIndex = 15;
            groupBoxCalendar.TabStop = false;
            groupBoxCalendar.Text = "التقويم الشهري";
            // 
            // monthCalendar
            // 
            monthCalendar.Location = new Point(8, 44);
            monthCalendar.MaxSelectionCount = 31;
            monthCalendar.Name = "monthCalendar";
            monthCalendar.RightToLeft = RightToLeft.Yes;
            monthCalendar.RightToLeftLayout = true;
            monthCalendar.TabIndex = 1;
            monthCalendar.DateChanged += monthCalendar_DateChanged;
            // 
            // lblCalendarTitle
            // 
            lblCalendarTitle.AutoSize = true;
            lblCalendarTitle.Font = new Font("Cairo", 8.999999F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lblCalendarTitle.Location = new Point(3, 20);
            lblCalendarTitle.Name = "lblCalendarTitle";
            lblCalendarTitle.Size = new Size(137, 23);
            lblCalendarTitle.TabIndex = 0;
            lblCalendarTitle.Text = "اختر الأيام المطلوبة للعمل";
            // 
            // groupBoxWorkingDays
            // 
            groupBoxWorkingDays.Controls.Add(chkSaturday);
            groupBoxWorkingDays.Controls.Add(chkFriday);
            groupBoxWorkingDays.Controls.Add(chkThursday);
            groupBoxWorkingDays.Controls.Add(chkWednesday);
            groupBoxWorkingDays.Controls.Add(chkTuesday);
            groupBoxWorkingDays.Controls.Add(chkMonday);
            groupBoxWorkingDays.Controls.Add(chkSunday);
            groupBoxWorkingDays.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            groupBoxWorkingDays.Location = new Point(580, 204);
            groupBoxWorkingDays.Name = "groupBoxWorkingDays";
            groupBoxWorkingDays.RightToLeft = RightToLeft.Yes;
            groupBoxWorkingDays.Size = new Size(496, 69);
            groupBoxWorkingDays.TabIndex = 10;
            groupBoxWorkingDays.TabStop = false;
            groupBoxWorkingDays.Text = "أيام العمل";
            // 
            // chkSaturday
            // 
            chkSaturday.AutoSize = true;
            chkSaturday.Font = new Font("Cairo", 8.999999F);
            chkSaturday.Location = new Point(23, 32);
            chkSaturday.Name = "chkSaturday";
            chkSaturday.RightToLeft = RightToLeft.Yes;
            chkSaturday.Size = new Size(60, 27);
            chkSaturday.TabIndex = 13;
            chkSaturday.Text = "السبت";
            chkSaturday.UseVisualStyleBackColor = true;
            // 
            // chkFriday
            // 
            chkFriday.AutoSize = true;
            chkFriday.Font = new Font("Cairo", 8.999999F);
            chkFriday.Location = new Point(89, 32);
            chkFriday.Name = "chkFriday";
            chkFriday.RightToLeft = RightToLeft.Yes;
            chkFriday.Size = new Size(64, 27);
            chkFriday.TabIndex = 12;
            chkFriday.Text = "الجمعة";
            chkFriday.UseVisualStyleBackColor = true;
            // 
            // chkThursday
            // 
            chkThursday.AutoSize = true;
            chkThursday.Font = new Font("Cairo", 8.999999F);
            chkThursday.Location = new Point(159, 32);
            chkThursday.Name = "chkThursday";
            chkThursday.RightToLeft = RightToLeft.Yes;
            chkThursday.Size = new Size(69, 27);
            chkThursday.TabIndex = 11;
            chkThursday.Text = "الخميس";
            chkThursday.UseVisualStyleBackColor = true;
            // 
            // chkWednesday
            // 
            chkWednesday.AutoSize = true;
            chkWednesday.Font = new Font("Cairo", 8.999999F);
            chkWednesday.Location = new Point(231, 32);
            chkWednesday.Name = "chkWednesday";
            chkWednesday.RightToLeft = RightToLeft.Yes;
            chkWednesday.Size = new Size(63, 27);
            chkWednesday.TabIndex = 10;
            chkWednesday.Text = "الأربعاء";
            chkWednesday.UseVisualStyleBackColor = true;
            // 
            // chkTuesday
            // 
            chkTuesday.AutoSize = true;
            chkTuesday.Font = new Font("Cairo", 8.999999F);
            chkTuesday.Location = new Point(296, 32);
            chkTuesday.Name = "chkTuesday";
            chkTuesday.RightToLeft = RightToLeft.Yes;
            chkTuesday.Size = new Size(60, 27);
            chkTuesday.TabIndex = 9;
            chkTuesday.Text = "الثلاثاء";
            chkTuesday.UseVisualStyleBackColor = true;
            // 
            // chkMonday
            // 
            chkMonday.AutoSize = true;
            chkMonday.Font = new Font("Cairo", 8.999999F);
            chkMonday.Location = new Point(362, 32);
            chkMonday.Name = "chkMonday";
            chkMonday.RightToLeft = RightToLeft.Yes;
            chkMonday.Size = new Size(61, 27);
            chkMonday.TabIndex = 8;
            chkMonday.Text = "الاثنين";
            chkMonday.UseVisualStyleBackColor = true;
            // 
            // chkSunday
            // 
            chkSunday.AutoSize = true;
            chkSunday.Font = new Font("Cairo", 8.999999F);
            chkSunday.Location = new Point(429, 32);
            chkSunday.Name = "chkSunday";
            chkSunday.RightToLeft = RightToLeft.Yes;
            chkSunday.Size = new Size(51, 27);
            chkSunday.TabIndex = 7;
            chkSunday.Text = "الأحد";
            chkSunday.UseVisualStyleBackColor = true;
            // 
            // dateTimePickerEnd
            // 
            dateTimePickerEnd.Font = new Font("Cairo", 9.75F);
            dateTimePickerEnd.Format = DateTimePickerFormat.Short;
            dateTimePickerEnd.Location = new Point(580, 166);
            dateTimePickerEnd.Name = "dateTimePickerEnd";
            dateTimePickerEnd.Size = new Size(172, 32);
            dateTimePickerEnd.TabIndex = 9;
            // 
            // dateTimePickerStart
            // 
            dateTimePickerStart.Font = new Font("Cairo", 9.75F);
            dateTimePickerStart.Format = DateTimePickerFormat.Short;
            dateTimePickerStart.Location = new Point(858, 166);
            dateTimePickerStart.Name = "dateTimePickerStart";
            dateTimePickerStart.Size = new Size(172, 32);
            dateTimePickerStart.TabIndex = 8;
            // 
            // lblEndDate
            // 
            lblEndDate.AutoSize = true;
            lblEndDate.Font = new Font("Cairo", 9.75F);
            lblEndDate.Location = new Point(757, 170);
            lblEndDate.Name = "lblEndDate";
            lblEndDate.Size = new Size(77, 24);
            lblEndDate.TabIndex = 7;
            lblEndDate.Text = "تاريخ النهاية:";
            // 
            // lblStartDate
            // 
            lblStartDate.AutoSize = true;
            lblStartDate.Font = new Font("Cairo", 9.75F);
            lblStartDate.Location = new Point(1035, 170);
            lblStartDate.Name = "lblStartDate";
            lblStartDate.Size = new Size(74, 24);
            lblStartDate.TabIndex = 6;
            lblStartDate.Text = "تاريخ البداية:";
            // 
            // txtDescription
            // 
            txtDescription.Font = new Font("Cairo", 9.75F);
            txtDescription.Location = new Point(580, 117);
            txtDescription.Multiline = true;
            txtDescription.Name = "txtDescription";
            txtDescription.Size = new Size(450, 43);
            txtDescription.TabIndex = 5;
            // 
            // lblDescription
            // 
            lblDescription.AutoSize = true;
            lblDescription.Font = new Font("Cairo", 9.75F);
            lblDescription.Location = new Point(1037, 120);
            lblDescription.Name = "lblDescription";
            lblDescription.Size = new Size(50, 24);
            lblDescription.TabIndex = 4;
            lblDescription.Text = "الوصف:";
            // 
            // txtProjectName
            // 
            txtProjectName.Font = new Font("Cairo", 9.75F);
            txtProjectName.Location = new Point(580, 77);
            txtProjectName.Name = "txtProjectName";
            txtProjectName.Size = new Size(450, 32);
            txtProjectName.TabIndex = 3;
            // 
            // lblProjectName
            // 
            lblProjectName.AutoSize = true;
            lblProjectName.Font = new Font("Cairo", 9.75F);
            lblProjectName.Location = new Point(1037, 80);
            lblProjectName.Name = "lblProjectName";
            lblProjectName.Size = new Size(77, 24);
            lblProjectName.TabIndex = 2;
            lblProjectName.Text = "مكان العمل:";
            // 
            // cmbEmployee
            // 
            cmbEmployee.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbEmployee.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            cmbEmployee.FormattingEnabled = true;
            cmbEmployee.Location = new Point(580, 39);
            cmbEmployee.Name = "cmbEmployee";
            cmbEmployee.Size = new Size(450, 32);
            cmbEmployee.TabIndex = 1;
            cmbEmployee.SelectedIndexChanged += cmbEmployee_SelectedIndexChanged;
            // 
            // lblEmployee
            // 
            lblEmployee.AutoSize = true;
            lblEmployee.Font = new Font("Cairo", 9.75F);
            lblEmployee.Location = new Point(1040, 42);
            lblEmployee.Name = "lblEmployee";
            lblEmployee.Size = new Size(59, 24);
            lblEmployee.TabIndex = 0;
            lblEmployee.Text = "الموظف:";
            // 
            // groupBoxActions
            // 
            groupBoxActions.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxActions.Controls.Add(btnClear);
            groupBoxActions.Controls.Add(btnDelete);
            groupBoxActions.Controls.Add(btnUpdate);
            groupBoxActions.Controls.Add(btnAdd);
            groupBoxActions.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxActions.Location = new Point(12, 300);
            groupBoxActions.Name = "groupBoxActions";
            groupBoxActions.RightToLeft = RightToLeft.Yes;
            groupBoxActions.Size = new Size(580, 80);
            groupBoxActions.TabIndex = 1;
            groupBoxActions.TabStop = false;
            groupBoxActions.Text = "العمليات";
            // 
            // btnClear
            // 
            btnClear.BackColor = Color.FromArgb(108, 117, 125);
            btnClear.FlatStyle = FlatStyle.Flat;
            btnClear.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnClear.ForeColor = Color.White;
            btnClear.Image = Properties.Resources.clear_formatting_32px;
            btnClear.ImageAlign = ContentAlignment.MiddleRight;
            btnClear.Location = new Point(20, 30);
            btnClear.Name = "btnClear";
            btnClear.Size = new Size(133, 35);
            btnClear.TabIndex = 3;
            btnClear.Text = "مسح الحقول";
            btnClear.TextAlign = ContentAlignment.MiddleLeft;
            btnClear.UseVisualStyleBackColor = false;
            btnClear.Click += btnClear_Click;
            // 
            // btnDelete
            // 
            btnDelete.BackColor = Color.FromArgb(220, 53, 69);
            btnDelete.FlatStyle = FlatStyle.Flat;
            btnDelete.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnDelete.ForeColor = Color.White;
            btnDelete.Image = Properties.Resources.delete_32px;
            btnDelete.ImageAlign = ContentAlignment.MiddleRight;
            btnDelete.Location = new Point(170, 30);
            btnDelete.Name = "btnDelete";
            btnDelete.Size = new Size(120, 35);
            btnDelete.TabIndex = 2;
            btnDelete.Text = "حذف";
            btnDelete.UseVisualStyleBackColor = false;
            btnDelete.Click += btnDelete_Click;
            // 
            // btnUpdate
            // 
            btnUpdate.BackColor = Color.FromArgb(255, 193, 7);
            btnUpdate.FlatStyle = FlatStyle.Flat;
            btnUpdate.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnUpdate.ForeColor = Color.Black;
            btnUpdate.Image = Properties.Resources.update_32px;
            btnUpdate.ImageAlign = ContentAlignment.MiddleRight;
            btnUpdate.Location = new Point(305, 30);
            btnUpdate.Name = "btnUpdate";
            btnUpdate.Size = new Size(120, 35);
            btnUpdate.TabIndex = 1;
            btnUpdate.Text = "تحديث";
            btnUpdate.UseVisualStyleBackColor = false;
            btnUpdate.Click += btnUpdate_Click;
            // 
            // btnAdd
            // 
            btnAdd.BackColor = Color.FromArgb(40, 167, 69);
            btnAdd.FlatStyle = FlatStyle.Flat;
            btnAdd.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnAdd.ForeColor = Color.White;
            btnAdd.Image = Properties.Resources.ok_32px;
            btnAdd.ImageAlign = ContentAlignment.MiddleRight;
            btnAdd.Location = new Point(440, 30);
            btnAdd.Name = "btnAdd";
            btnAdd.Size = new Size(120, 35);
            btnAdd.TabIndex = 0;
            btnAdd.Text = "إضافة";
            btnAdd.UseVisualStyleBackColor = false;
            btnAdd.Click += btnAdd_Click;
            // 
            // groupBoxWorkPeriodList
            // 
            groupBoxWorkPeriodList.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxWorkPeriodList.Controls.Add(lbl_NoDocuments);
            groupBoxWorkPeriodList.Controls.Add(btnRefresh);
            groupBoxWorkPeriodList.Controls.Add(btnDailyTracking);
            groupBoxWorkPeriodList.Controls.Add(chkSelectAll);
            groupBoxWorkPeriodList.Controls.Add(btnDeleteSelected);
            groupBoxWorkPeriodList.Controls.Add(cmbSearchGroup);
            groupBoxWorkPeriodList.Controls.Add(lblSearchGroup);
            groupBoxWorkPeriodList.Controls.Add(dateTimePickerSearchStart);
            groupBoxWorkPeriodList.Controls.Add(dateTimePickerSearchEnd);
            groupBoxWorkPeriodList.Controls.Add(lblSearchPeriod);
            groupBoxWorkPeriodList.Controls.Add(btnAdvancedSearch);
            groupBoxWorkPeriodList.Controls.Add(btnSearch);
            groupBoxWorkPeriodList.Controls.Add(txtSearch);
            groupBoxWorkPeriodList.Controls.Add(dataGridViewWorkPeriods);
            groupBoxWorkPeriodList.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxWorkPeriodList.Location = new Point(12, 390);
            groupBoxWorkPeriodList.Name = "groupBoxWorkPeriodList";
            groupBoxWorkPeriodList.RightToLeft = RightToLeft.Yes;
            groupBoxWorkPeriodList.Size = new Size(1160, 350);
            groupBoxWorkPeriodList.TabIndex = 2;
            groupBoxWorkPeriodList.TabStop = false;
            groupBoxWorkPeriodList.Text = "قائمة فترات العمل";
            // 
            // lbl_NoDocuments
            // 
            lbl_NoDocuments.Anchor = AnchorStyles.None;
            lbl_NoDocuments.AutoSize = true;
            lbl_NoDocuments.Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lbl_NoDocuments.Location = new Point(553, 253);
            lbl_NoDocuments.Name = "lbl_NoDocuments";
            lbl_NoDocuments.Size = new Size(54, 30);
            lbl_NoDocuments.TabIndex = 37;
            lbl_NoDocuments.Text = "label6";
            lbl_NoDocuments.Visible = false;
            // 
            // btnRefresh
            // 
            btnRefresh.BackColor = Color.FromArgb(0, 123, 255);
            btnRefresh.FlatStyle = FlatStyle.Flat;
            btnRefresh.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnRefresh.ForeColor = Color.White;
            btnRefresh.Image = Properties.Resources.update_32px;
            btnRefresh.ImageAlign = ContentAlignment.MiddleRight;
            btnRefresh.Location = new Point(812, 59);
            btnRefresh.Name = "btnRefresh";
            btnRefresh.Size = new Size(89, 35);
            btnRefresh.TabIndex = 4;
            btnRefresh.Text = "تحديث";
            btnRefresh.TextAlign = ContentAlignment.MiddleLeft;
            btnRefresh.UseVisualStyleBackColor = false;
            btnRefresh.Click += btnRefresh_Click;
            // 
            // btnDailyTracking
            // 
            btnDailyTracking.BackColor = Color.FromArgb(255, 165, 0);
            btnDailyTracking.FlatStyle = FlatStyle.Flat;
            btnDailyTracking.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnDailyTracking.ForeColor = Color.White;
            btnDailyTracking.Image = Properties.Resources.chart_increasing_32px;
            btnDailyTracking.ImageAlign = ContentAlignment.MiddleRight;
            btnDailyTracking.Location = new Point(696, 19);
            btnDailyTracking.Name = "btnDailyTracking";
            btnDailyTracking.Size = new Size(205, 37);
            btnDailyTracking.TabIndex = 5;
            btnDailyTracking.Text = "التتبع اليومي";
            btnDailyTracking.UseVisualStyleBackColor = false;
            btnDailyTracking.Click += btnDailyTracking_Click;
            // 
            // chkSelectAll
            // 
            chkSelectAll.AutoSize = true;
            chkSelectAll.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            chkSelectAll.ForeColor = Color.Blue;
            chkSelectAll.Location = new Point(921, 64);
            chkSelectAll.Name = "chkSelectAll";
            chkSelectAll.RightToLeft = RightToLeft.No;
            chkSelectAll.Size = new Size(93, 28);
            chkSelectAll.TabIndex = 5;
            chkSelectAll.Text = "تحديد الكل";
            chkSelectAll.UseVisualStyleBackColor = true;
            chkSelectAll.CheckedChanged += chkSelectAll_CheckedChanged;
            // 
            // btnDeleteSelected
            // 
            btnDeleteSelected.BackColor = Color.FromArgb(220, 53, 69);
            btnDeleteSelected.FlatStyle = FlatStyle.Flat;
            btnDeleteSelected.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnDeleteSelected.ForeColor = Color.White;
            btnDeleteSelected.Image = Properties.Resources.remove_property_32px;
            btnDeleteSelected.ImageAlign = ContentAlignment.MiddleRight;
            btnDeleteSelected.Location = new Point(1020, 59);
            btnDeleteSelected.Name = "btnDeleteSelected";
            btnDeleteSelected.Size = new Size(120, 35);
            btnDeleteSelected.TabIndex = 6;
            btnDeleteSelected.Text = "حذف المحدد";
            btnDeleteSelected.TextAlign = ContentAlignment.MiddleLeft;
            btnDeleteSelected.UseVisualStyleBackColor = false;
            btnDeleteSelected.Click += btnDeleteSelected_Click;
            // 
            // cmbSearchGroup
            // 
            cmbSearchGroup.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbSearchGroup.Font = new Font("Cairo", 8.999999F, FontStyle.Regular, GraphicsUnit.Point, 0);
            cmbSearchGroup.FormattingEnabled = true;
            cmbSearchGroup.Location = new Point(20, 63);
            cmbSearchGroup.Name = "cmbSearchGroup";
            cmbSearchGroup.Size = new Size(171, 31);
            cmbSearchGroup.TabIndex = 10;
            // 
            // lblSearchGroup
            // 
            lblSearchGroup.Font = new Font("Cairo", 8.999999F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lblSearchGroup.Image = Properties.Resources.filter_32px;
            lblSearchGroup.ImageAlign = ContentAlignment.MiddleRight;
            lblSearchGroup.Location = new Point(197, 64);
            lblSearchGroup.Name = "lblSearchGroup";
            lblSearchGroup.Size = new Size(129, 30);
            lblSearchGroup.TabIndex = 11;
            lblSearchGroup.Text = "البحث بالمجموعة:";
            lblSearchGroup.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // dateTimePickerSearchStart
            // 
            dateTimePickerSearchStart.Font = new Font("Cairo", 8.999999F);
            dateTimePickerSearchStart.Format = DateTimePickerFormat.Short;
            dateTimePickerSearchStart.Location = new Point(465, 64);
            dateTimePickerSearchStart.Name = "dateTimePickerSearchStart";
            dateTimePickerSearchStart.Size = new Size(120, 30);
            dateTimePickerSearchStart.TabIndex = 12;
            // 
            // dateTimePickerSearchEnd
            // 
            dateTimePickerSearchEnd.Font = new Font("Cairo", 8.999999F);
            dateTimePickerSearchEnd.Format = DateTimePickerFormat.Short;
            dateTimePickerSearchEnd.Location = new Point(332, 63);
            dateTimePickerSearchEnd.Name = "dateTimePickerSearchEnd";
            dateTimePickerSearchEnd.Size = new Size(120, 30);
            dateTimePickerSearchEnd.TabIndex = 13;
            // 
            // lblSearchPeriod
            // 
            lblSearchPeriod.Font = new Font("Cairo", 8.999999F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lblSearchPeriod.Image = Properties.Resources.filter_32px;
            lblSearchPeriod.ImageAlign = ContentAlignment.MiddleRight;
            lblSearchPeriod.Location = new Point(591, 63);
            lblSearchPeriod.Name = "lblSearchPeriod";
            lblSearchPeriod.Size = new Size(95, 30);
            lblSearchPeriod.TabIndex = 14;
            lblSearchPeriod.Text = "فترة البحث:";
            lblSearchPeriod.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // btnAdvancedSearch
            // 
            btnAdvancedSearch.BackColor = Color.FromArgb(0, 123, 255);
            btnAdvancedSearch.FlatStyle = FlatStyle.Flat;
            btnAdvancedSearch.Font = new Font("Cairo", 8.999999F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnAdvancedSearch.ForeColor = Color.White;
            btnAdvancedSearch.Image = Properties.Resources.search_32px;
            btnAdvancedSearch.ImageAlign = ContentAlignment.MiddleRight;
            btnAdvancedSearch.Location = new Point(696, 59);
            btnAdvancedSearch.Name = "btnAdvancedSearch";
            btnAdvancedSearch.Size = new Size(112, 34);
            btnAdvancedSearch.TabIndex = 15;
            btnAdvancedSearch.Text = "بحث متقدم";
            btnAdvancedSearch.TextAlign = ContentAlignment.MiddleLeft;
            btnAdvancedSearch.UseVisualStyleBackColor = false;
            btnAdvancedSearch.Click += btnAdvancedSearch_Click;
            // 
            // btnSearch
            // 
            btnSearch.BackColor = Color.FromArgb(40, 167, 69);
            btnSearch.FlatStyle = FlatStyle.Flat;
            btnSearch.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnSearch.ForeColor = Color.White;
            btnSearch.Image = Properties.Resources.search_32px;
            btnSearch.ImageAlign = ContentAlignment.MiddleRight;
            btnSearch.Location = new Point(521, 19);
            btnSearch.Name = "btnSearch";
            btnSearch.Size = new Size(109, 39);
            btnSearch.TabIndex = 3;
            btnSearch.Text = "بحث";
            btnSearch.UseVisualStyleBackColor = false;
            btnSearch.Click += btnSearch_Click;
            // 
            // txtSearch
            // 
            txtSearch.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            txtSearch.Location = new Point(20, 23);
            txtSearch.Name = "txtSearch";
            txtSearch.PlaceholderText = "بحث (اسم الموظف أو مكان العمل):";
            txtSearch.Size = new Size(502, 32);
            txtSearch.TabIndex = 2;
            // 
            // dataGridViewWorkPeriods
            // 
            dataGridViewWorkPeriods.AllowUserToAddRows = false;
            dataGridViewWorkPeriods.AllowUserToDeleteRows = false;
            dataGridViewWorkPeriods.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dataGridViewWorkPeriods.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridViewWorkPeriods.BackgroundColor = Color.White;
            dataGridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = SystemColors.Control;
            dataGridViewCellStyle1.Font = new Font("Cairo", 10F, FontStyle.Bold);
            dataGridViewCellStyle1.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle1.WrapMode = DataGridViewTriState.True;
            dataGridViewWorkPeriods.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            dataGridViewWorkPeriods.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.Window;
            dataGridViewCellStyle2.Font = new Font("Cairo", 10F, FontStyle.Bold);
            dataGridViewCellStyle2.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.False;
            dataGridViewWorkPeriods.DefaultCellStyle = dataGridViewCellStyle2;
            dataGridViewWorkPeriods.Location = new Point(20, 97);
            dataGridViewWorkPeriods.Name = "dataGridViewWorkPeriods";
            dataGridViewWorkPeriods.ReadOnly = true;
            dataGridViewWorkPeriods.RightToLeft = RightToLeft.Yes;
            dataGridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = SystemColors.Control;
            dataGridViewCellStyle3.Font = new Font("Cairo", 10F, FontStyle.Bold);
            dataGridViewCellStyle3.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle3.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle3.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = DataGridViewTriState.True;
            dataGridViewWorkPeriods.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            dataGridViewWorkPeriods.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridViewWorkPeriods.Size = new Size(1120, 243);
            dataGridViewWorkPeriods.TabIndex = 0;
            dataGridViewWorkPeriods.DataError += dataGridViewWorkPeriods_DataError;
            dataGridViewWorkPeriods.SelectionChanged += dataGridViewWorkPeriods_SelectionChanged;
            // 
            // groupBoxSummary
            // 
            groupBoxSummary.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            groupBoxSummary.Controls.Add(lblTotalHours);
            groupBoxSummary.Controls.Add(lblTotalDays);
            groupBoxSummary.Controls.Add(lblSelectedPeriod);
            groupBoxSummary.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxSummary.Location = new Point(600, 300);
            groupBoxSummary.Name = "groupBoxSummary";
            groupBoxSummary.RightToLeft = RightToLeft.Yes;
            groupBoxSummary.Size = new Size(572, 80);
            groupBoxSummary.TabIndex = 3;
            groupBoxSummary.TabStop = false;
            groupBoxSummary.Text = "ملخص الفترة المحددة";
            // 
            // lblTotalHours
            // 
            lblTotalHours.AutoSize = true;
            lblTotalHours.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblTotalHours.ForeColor = Color.Blue;
            lblTotalHours.Location = new Point(20, 50);
            lblTotalHours.Name = "lblTotalHours";
            lblTotalHours.Size = new Size(113, 24);
            lblTotalHours.TabIndex = 2;
            lblTotalHours.Text = "إجمالي الساعات: 0";
            // 
            // lblTotalDays
            // 
            lblTotalDays.AutoSize = true;
            lblTotalDays.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblTotalDays.ForeColor = Color.Green;
            lblTotalDays.Location = new Point(200, 50);
            lblTotalDays.Name = "lblTotalDays";
            lblTotalDays.Size = new Size(96, 24);
            lblTotalDays.TabIndex = 1;
            lblTotalDays.Text = "إجمالي الأيام: 0";
            // 
            // lblSelectedPeriod
            // 
            lblSelectedPeriod.AutoSize = true;
            lblSelectedPeriod.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblSelectedPeriod.Location = new Point(350, 25);
            lblSelectedPeriod.Name = "lblSelectedPeriod";
            lblSelectedPeriod.Size = new Size(111, 24);
            lblSelectedPeriod.TabIndex = 0;
            lblSelectedPeriod.Text = "لم يتم اختيار فترة";
            // 
            // WorkPeriodForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.White;
            ClientSize = new Size(1184, 751);
            Controls.Add(groupBoxSummary);
            Controls.Add(groupBoxWorkPeriodList);
            Controls.Add(groupBoxActions);
            Controls.Add(groupBoxWorkPeriod);
            Name = "WorkPeriodForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            Text = "إدارة فترات العمل";
            Load += WorkPeriodForm_Load;
            groupBoxWorkPeriod.ResumeLayout(false);
            groupBoxWorkPeriod.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDownDailyHours).EndInit();
            groupBoxCalendar.ResumeLayout(false);
            groupBoxCalendar.PerformLayout();
            groupBoxWorkingDays.ResumeLayout(false);
            groupBoxWorkingDays.PerformLayout();
            groupBoxActions.ResumeLayout(false);
            groupBoxWorkPeriodList.ResumeLayout(false);
            groupBoxWorkPeriodList.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridViewWorkPeriods).EndInit();
            groupBoxSummary.ResumeLayout(false);
            groupBoxSummary.PerformLayout();
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBoxWorkPeriod;
        private System.Windows.Forms.Label lblEmployee;
        private System.Windows.Forms.ComboBox cmbEmployee;
        private System.Windows.Forms.TextBox txtProjectName;
        private System.Windows.Forms.Label lblProjectName;
        private System.Windows.Forms.TextBox txtDescription;
        private System.Windows.Forms.Label lblDescription;
        private System.Windows.Forms.DateTimePicker dateTimePickerStart;
        private System.Windows.Forms.Label lblStartDate;
        private System.Windows.Forms.DateTimePicker dateTimePickerEnd;
        private System.Windows.Forms.Label lblEndDate;
        private System.Windows.Forms.GroupBox groupBoxWorkingDays;
        private System.Windows.Forms.MonthCalendar monthCalendar;
        private System.Windows.Forms.Label lblCalendarTitle;
        private System.Windows.Forms.GroupBox groupBoxCalendar;
        private System.Windows.Forms.ComboBox cmbStatus;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.NumericUpDown numericUpDownDailyHours;
        private System.Windows.Forms.Label lblDailyHours;
        private System.Windows.Forms.GroupBox groupBoxActions;
        private System.Windows.Forms.Button btnAdd;
        private System.Windows.Forms.Button btnUpdate;
        private System.Windows.Forms.Button btnDelete;
        private System.Windows.Forms.Button btnClear;
        private System.Windows.Forms.GroupBox groupBoxWorkPeriodList;
        private System.Windows.Forms.DataGridView dataGridViewWorkPeriods;
        private System.Windows.Forms.TextBox txtSearch;
        private System.Windows.Forms.Button btnSearch;
        private System.Windows.Forms.CheckBox chkSelectAll;
        private System.Windows.Forms.Button btnDeleteSelected;
        private System.Windows.Forms.Button btnRefresh;
        private System.Windows.Forms.Button btnDailyTracking;
        private System.Windows.Forms.ComboBox cmbSearchGroup;
        private System.Windows.Forms.Label lblSearchGroup;
        private System.Windows.Forms.DateTimePicker dateTimePickerSearchStart;
        private System.Windows.Forms.DateTimePicker dateTimePickerSearchEnd;
        private System.Windows.Forms.Label lblSearchPeriod;
        private System.Windows.Forms.Button btnAdvancedSearch;
        private System.Windows.Forms.GroupBox groupBoxSummary;
        private System.Windows.Forms.Label lblSelectedPeriod;
        private System.Windows.Forms.Label lblTotalDays;
        private System.Windows.Forms.Label lblTotalHours;
        private CheckBox chkSaturday;
        private CheckBox chkFriday;
        private CheckBox chkThursday;
        private CheckBox chkWednesday;
        private CheckBox chkTuesday;
        private CheckBox chkMonday;
        private CheckBox chkSunday;
        private Label lbl_NoDocuments;
    }
}