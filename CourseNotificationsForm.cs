using System;
using System.Data;
using System.Windows.Forms;
using System.Drawing;
using ClosedXML.Excel;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;

namespace EmployeeManagementSystem
{
    public partial class CourseNotificationsForm : Form
    {
        private int? selectedNotificationId = null;

        public CourseNotificationsForm()
        {
            InitializeComponent();
            LoadCourses();
            LoadEmployeeNames();
            LoadNotificationData();
            ClearForm();
            ThemeManager.ApplyThemeToForm(this);
        }

        private void LoadCourses()
        {
            try
            {
                var courses = DatabaseHelper.GetAllCourses();
                cmbCourse.Items.Clear();
                foreach (DataRow row in courses.Rows)
                {
                    cmbCourse.Items.Add($"{row["رقم الدورة"]} - {row["نوع الدورة"]}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تحميل الدورات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadEmployeeNames()
        {
            try
            {
                var employees = DatabaseHelper.GetAllEmployees();
                cmbEmployeeName.Items.Clear();
                foreach (DataRow row in employees.Rows)
                {
                    cmbEmployeeName.Items.Add(row["الاسم"].ToString());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تحميل أسماء الموظفين: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadNotificationData()
        {
            try
            {
                var table = DatabaseHelper.GetCourseNotifications();
                dataGridView1.DataSource = table;
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تحميل بيانات الإشعارات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearForm()
        {
            selectedNotificationId = null;
            cmbCourse.SelectedIndex = -1;
            cmbEmployeeName.SelectedIndex = -1;
            cmbNotificationType.SelectedIndex = -1;
            txtMessage.Clear();
            cmbPriority.SelectedIndex = -1;
            chkIsRead.Checked = false;
            dtSentDate.Value = DateTime.Now;
            btnAdd.Enabled = true;
            btnUpdate.Enabled = false;
            btnDelete.Enabled = false;
            dataGridView1.ClearSelection();
        }

        private bool ValidateForm()
        {
            if (cmbCourse.SelectedIndex == -1)
            {
                MessageBox.Show("الرجاء اختيار الدورة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(cmbEmployeeName.Text))
            {
                MessageBox.Show("الرجاء اختيار اسم الموظف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (cmbNotificationType.SelectedIndex == -1)
            {
                MessageBox.Show("الرجاء اختيار نوع الإشعار", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtMessage.Text))
            {
                MessageBox.Show("الرجاء إدخال نص الرسالة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (cmbPriority.SelectedIndex == -1)
            {
                MessageBox.Show("الرجاء اختيار الأولوية", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private CourseNotification GetFormData()
        {
            return new CourseNotification
            {
                NotificationId = selectedNotificationId ?? 0,
                CourseId = GetSelectedCourseId(),
                EmployeeName = cmbEmployeeName.Text,
                NotificationType = cmbNotificationType.Text,
                Message = txtMessage.Text,
                SentDate = dtSentDate.Value,
                IsRead = chkIsRead.Checked,
                Priority = cmbPriority.Text
            };
        }

        private int GetSelectedCourseId()
        {
            if (cmbCourse.SelectedIndex >= 0)
            {
                var courses = DatabaseHelper.GetAllCourses();
                return Convert.ToInt32(courses.Rows[cmbCourse.SelectedIndex]["المعرف"]);
            }
            return 0;
        }

        private async void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;

                var notification = GetFormData();
                await Task.Run(() => DatabaseHelper.AddCourseNotification(notification));

                MessageBox.Show("تم إضافة الإشعار بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                LoadNotificationData();
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء إضافة الإشعار: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnUpdate_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm() || !selectedNotificationId.HasValue) return;

                var notification = GetFormData();
                await Task.Run(() => DatabaseHelper.UpdateCourseNotification(notification));

                MessageBox.Show("تم تحديث الإشعار بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                LoadNotificationData();
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تحديث الإشعار: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridView1.SelectedRows.Count == 0) return;

                if (MessageBox.Show("هل أنت متأكد من حذف الإشعار؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    foreach (DataGridViewRow row in dataGridView1.SelectedRows)
                    {
                        int notificationId = Convert.ToInt32(row.Cells["NotificationId"].Value);
                        await Task.Run(() => DatabaseHelper.DeleteCourseNotification(notificationId));
                    }

                    MessageBox.Show("تم حذف الإشعار بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadNotificationData();
                    ClearForm();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء حذف الإشعار: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private void dataGridView1_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                DataGridViewRow row = dataGridView1.Rows[e.RowIndex];
                selectedNotificationId = Convert.ToInt32(row.Cells["NotificationId"].Value);
                
                // تحديد الدورة
                int courseId = Convert.ToInt32(row.Cells["CourseId"].Value);
                var courses = DatabaseHelper.GetAllCourses();
                for (int i = 0; i < courses.Rows.Count; i++)
                {
                    if (Convert.ToInt32(courses.Rows[i]["المعرف"]) == courseId)
                    {
                        cmbCourse.SelectedIndex = i;
                        break;
                    }
                }

                cmbEmployeeName.Text = row.Cells["اسم الموظف"].Value?.ToString() ?? "";
                cmbNotificationType.Text = row.Cells["نوع الإشعار"].Value?.ToString() ?? "";
                txtMessage.Text = row.Cells["الرسالة"].Value?.ToString() ?? "";
                dtSentDate.Value = Convert.ToDateTime(row.Cells["تاريخ الإرسال"].Value);
                chkIsRead.Checked = Convert.ToBoolean(row.Cells["مقروء"].Value);
                cmbPriority.Text = row.Cells["الأولوية"].Value?.ToString() ?? "";

                btnAdd.Enabled = false;
                btnUpdate.Enabled = true;
                btnDelete.Enabled = true;
            }
        }

        private async void btnSendBulkNotifications_Click(object sender, EventArgs e)
        {
            try
            {
                if (cmbCourse.SelectedIndex == -1)
                {
                    MessageBox.Show("الرجاء اختيار الدورة أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtMessage.Text))
                {
                    MessageBox.Show("الرجاء إدخال نص الرسالة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show("هل تريد إرسال إشعار لجميع الموظفين المسجلين في هذه الدورة؟", 
                    "تأكيد الإرسال", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // الحصول على قائمة الموظفين المسجلين في الدورة
                    var courseId = GetSelectedCourseId();
                    var enrolledEmployees = DatabaseHelper.GetCourseEnrolledEmployees(courseId);

                    int sentCount = 0;
                    foreach (DataRow employee in enrolledEmployees.Rows)
                    {
                        var notification = new CourseNotification
                        {
                            CourseId = courseId,
                            EmployeeName = employee["اسم الموظف"].ToString(),
                            NotificationType = cmbNotificationType.Text ?? "عام",
                            Message = txtMessage.Text,
                            SentDate = DateTime.Now,
                            IsRead = false,
                            Priority = cmbPriority.Text ?? "عادي"
                        };

                        await Task.Run(() => DatabaseHelper.AddCourseNotification(notification));
                        sentCount++;
                    }

                    MessageBox.Show($"تم إرسال {sentCount} إشعار بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadNotificationData();
                    ClearForm();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء إرسال الإشعارات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                using (SaveFileDialog sfd = new SaveFileDialog())
                {
                    sfd.Filter = "Excel Workbook|*.xlsx";
                    sfd.FileName = "تقرير_إشعارات_الدورات_" + DateTime.Now.ToString("yyyy-MM-dd") + ".xlsx";

                    if (sfd.ShowDialog() == DialogResult.OK)
                    {
                        using (XLWorkbook workbook = new XLWorkbook())
                        {
                            var worksheet = workbook.Worksheets.Add("إشعارات الدورات");

                            // Add headers
                            for (int i = 0; i < dataGridView1.Columns.Count; i++)
                            {
                                worksheet.Cell(1, i + 1).Value = dataGridView1.Columns[i].HeaderText;
                            }

                            // Add data
                            for (int i = 0; i < dataGridView1.Rows.Count; i++)
                            {
                                for (int j = 0; j < dataGridView1.Columns.Count; j++)
                                {
                                    worksheet.Cell(i + 2, j + 1).Value = dataGridView1.Rows[i].Cells[j].Value?.ToString() ?? "";
                                }
                            }

                            // Format as table
                            var range = worksheet.Range(1, 1, dataGridView1.Rows.Count + 1, dataGridView1.Columns.Count);
                            range.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            range.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                            worksheet.Row(1).Style.Fill.BackgroundColor = XLColor.LightGray;
                            worksheet.Row(1).Style.Font.Bold = true;

                            worksheet.Columns().AdjustToContents();
                            workbook.SaveAs(sfd.FileName);
                        }

                        MessageBox.Show("تم تصدير البيانات بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تصدير البيانات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}