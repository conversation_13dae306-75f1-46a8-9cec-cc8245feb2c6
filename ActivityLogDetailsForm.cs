using System;
using System.Drawing;
using System.Text.Json;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class ActivityLogDetailsForm : Form
    {
        private ActivityLog activityLog;

        public ActivityLogDetailsForm(ActivityLog log)
        {
            InitializeComponent();
            this.activityLog = log;
        }

        private void ActivityLogDetailsForm_Load(object sender, EventArgs e)
        {
            LoadData();
        }

        private void LoadData()
        {
            try
            {
                // تحميل البيانات الأساسية
                lblUser.Text = $"{activityLog.FullName} ({activityLog.Username})";
                lblAction.Text = GetFriendlyActionName(activityLog.Action ?? "غير محدد");
                lblTable.Text = GetFriendlyTableName(activityLog.TableName ?? "غير محدد");
                lblRecordId.Text = activityLog.RecordId?.ToString() ?? "غير محدد";
                lblDate.Text = activityLog.ActionDate.ToString("yyyy-MM-dd HH:mm:ss");
                lblIpAddress.Text = activityLog.IpAddress ?? "غير محدد";
                lblComputerName.Text = activityLog.ComputerName ?? "غير محدد";

                // تلوين الحالة
                lblStatus.Text = activityLog.Status ?? "غير محدد";
                switch (activityLog.Status)
                {
                    case "نجح":
                        lblStatus.ForeColor = Color.Green;
                        break;
                    case "فشل":
                        lblStatus.ForeColor = Color.Red;
                        break;
                    case "تحذير":
                        lblStatus.ForeColor = Color.Orange;
                        break;
                    default:
                        lblStatus.ForeColor = Color.Black;
                        break;
                }

                // تلوين الأولوية
                lblPriority.Text = activityLog.Priority ?? "غير محدد";
                switch (activityLog.Priority)
                {
                    case "حرج":
                        lblPriority.ForeColor = Color.Red;
                        lblPriority.Font = new Font(lblPriority.Font, FontStyle.Bold);
                        break;
                    case "مهم":
                        lblPriority.ForeColor = Color.Orange;
                        lblPriority.Font = new Font(lblPriority.Font, FontStyle.Bold);
                        break;
                    case "عادي":
                        lblPriority.ForeColor = Color.Green;
                        break;
                    default:
                        lblPriority.ForeColor = Color.Black;
                        break;
                }

                // تحميل القيم القديمة مع تنسيق محسن
                LoadFormattedValues(rtbOldValues, activityLog.OldValues, "لا توجد قيم قديمة");

                // تحميل القيم الجديدة مع تنسيق محسن
                LoadFormattedValues(rtbNewValues, activityLog.NewValues, "لا توجد قيم جديدة");

                // تحميل الملاحظات
                rtbNotes.Text = activityLog.AdditionalInfo ?? "لا توجد ملاحظات";

                // تحميل رسالة الخطأ
                if (!string.IsNullOrEmpty(activityLog.ErrorMessage))
                {
                    rtbErrorMessage.Text = activityLog.ErrorMessage;
                    rtbErrorMessage.Visible = true;
                    lblErrorMessageTitle.Visible = true;
                }
                else
                {
                    rtbErrorMessage.Text = "لا توجد أخطاء";
                    rtbErrorMessage.Visible = false;
                    lblErrorMessageTitle.Visible = false;
                }

                // تحسين عنوان النموذج
                this.Text = $"تفاصيل النشاط - {GetFriendlyActionName(activityLog.Action)} - {activityLog.ActionDate:yyyy-MM-dd HH:mm}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحويل أسماء الإجراءات إلى أسماء مفهومة للمستخدم
        /// </summary>
        private string GetFriendlyActionName(string action)
        {
            if (string.IsNullOrEmpty(action)) return "غير محدد";

            // تحسين عرض أسماء الإجراءات للنماذج الثلاثة
            var actionMappings = new Dictionary<string, string>
            {
                // إجراءات الإجازات
                {"إضافة إجازة", "🏖️ إضافة إجازة جديدة"},
                {"تحديث إجازة", "✏️ تعديل بيانات إجازة"},
                {"حذف إجازة", "🗑️ حذف إجازة"},
                {"بحث في الإجازات", "🔍 البحث في الإجازات"},
                {"تصدير الإجازات", "📤 تصدير بيانات الإجازات"},
                {"طباعة تقرير الإجازات", "🖨️ طباعة تقرير الإجازات"},
                
                // إجراءات الدورات
                {"إضافة دورة", "🎓 إضافة دورة تدريبية جديدة"},
                {"تحديث دورة", "✏️ تعديل بيانات دورة"},
                {"حذف دورة", "🗑️ حذف دورة تدريبية"},
                {"بحث في الدورات", "🔍 البحث في الدورات"},
                {"تصدير الدورات", "📤 تصدير بيانات الدورات"},
                {"طباعة تقرير الدورات", "🖨️ طباعة تقرير الدورات"},
                
                // إجراءات الوقوعات الشهرية
                {"إضافة وقوعات شهرية جديدة", "📅 إضافة وقوعات شهرية"},
                {"تحديث وقوعات شهرية", "✏️ تعديل الوقوعات الشهرية"},
                {"بحث في تقرير الأحداث الشهرية", "🔍 البحث في الوقوعات"},
                {"تصدير تقرير الوقوعات الشهرية إلى Excel", "📊 تصدير تقرير الوقوعات"},
                {"إنشاء تقرير الأحداث الشهرية", "📋 إنشاء تقرير الوقوعات"},
                
                // إجراءات النظام العامة
                {"فتح نموذج", "🚪 فتح نموذج"},
                {"إغلاق نموذج", "🚪 إغلاق نموذج"},
                {"تسجيل دخول", "🔐 تسجيل دخول"},
                {"تسجيل خروج", "🔓 تسجيل خروج"}
            };

            // البحث عن تطابق مباشر
            if (actionMappings.ContainsKey(action))
                return actionMappings[action];

            // البحث عن تطابق جزئي
            foreach (var mapping in actionMappings)
            {
                if (action.Contains(mapping.Key))
                    return mapping.Value;
            }

            return action; // إرجاع الاسم الأصلي إذا لم يوجد تطابق
        }

        /// <summary>
        /// تحويل أسماء الجداول إلى أسماء مفهومة للمستخدم
        /// </summary>
        private string GetFriendlyTableName(string tableName)
        {
            if (string.IsNullOrEmpty(tableName)) return "غير محدد";

            var tableMappings = new Dictionary<string, string>
            {
                {"Vacations", "📋 جدول الإجازات"},
                {"Courses", "🎓 جدول الدورات التدريبية"},
                {"MonthlyOccurrences", "📅 جدول الوقوعات الشهرية"},
                {"add_geab", "📊 جدول الحضور والغياب"},
                {"Employees", "👥 جدول الموظفين"},
                {"System", "⚙️ النظام"},
                {"Security", "🔒 الأمان"}
            };

            return tableMappings.ContainsKey(tableName) ? tableMappings[tableName] : tableName;
        }

        /// <summary>
        /// تحميل وتنسيق القيم بشكل محسن
        /// </summary>
        private void LoadFormattedValues(RichTextBox rtb, string jsonValues, string emptyMessage)
        {
            if (string.IsNullOrEmpty(jsonValues))
            {
                rtb.Text = emptyMessage;
                return;
            }

            try
            {
                var jsonDoc = JsonDocument.Parse(jsonValues);
                
                // تحقق من نوع البيانات وقم بتنسيقها وفقاً لذلك
                if (IsVacationData(jsonValues))
                {
                    rtb.Text = FormatVacationData(jsonDoc);
                }
                else if (IsCourseData(jsonValues))
                {
                    rtb.Text = FormatCourseData(jsonDoc);
                }
                else if (IsMonthlyOccurrenceData(jsonValues))
                {
                    rtb.Text = FormatMonthlyOccurrenceData(jsonDoc);
                }
                else
                {
                    // تنسيق JSON عادي
                    rtb.Text = JsonSerializer.Serialize(jsonDoc, new JsonSerializerOptions
                    {
                        WriteIndented = true,
                        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                    });
                }
            }
            catch
            {
                rtb.Text = jsonValues;
            }
        }

        /// <summary>
        /// تحقق من كون البيانات خاصة بالإجازات
        /// </summary>
        private bool IsVacationData(string jsonValues)
        {
            return jsonValues.Contains("VacationType") ||
                   jsonValues.Contains("StartDate") ||
                   jsonValues.Contains("EndDate") ||
                   jsonValues.Contains("VacationDays");
        }

        /// <summary>
        /// تحقق من كون البيانات خاصة بالدورات
        /// </summary>
        private bool IsCourseData(string jsonValues)
        {
            return jsonValues.Contains("CourseName") ||
                   jsonValues.Contains("CourseLocation") ||
                   jsonValues.Contains("CourseStartDate") ||
                   jsonValues.Contains("CourseEndDate");
        }

        /// <summary>
        /// تحقق من كون البيانات خاصة بالوقوعات الشهرية
        /// </summary>
        private bool IsMonthlyOccurrenceData(string jsonValues)
        {
            return jsonValues.Contains("EmployeeCode") ||
                   jsonValues.Contains("Month") ||
                   jsonValues.Contains("Year") ||
                   jsonValues.Contains("Days") ||
                   jsonValues.Contains("day_");
        }

        /// <summary>
        /// تنسيق بيانات الإجازات
        /// </summary>
        private string FormatVacationData(JsonDocument jsonDoc)
        {
            var result = "📋 بيانات الإجازة:\n";
            result += "═══════════════════════════════════════\n\n";

            var root = jsonDoc.RootElement;
            
            if (root.TryGetProperty("EmployeeName", out var empName))
                result += $"👤 اسم الموظف: {empName.GetString()}\n";
            
            if (root.TryGetProperty("EmployeeCode", out var empCode))
                result += $"🆔 الرقم الوظيفي: {empCode.GetString()}\n";
            
            if (root.TryGetProperty("VacationType", out var vacType))
                result += $"🏖️ نوع الإجازة: {vacType.GetString()}\n";
            
            if (root.TryGetProperty("StartDate", out var startDate))
                result += $"📅 تاريخ البداية: {startDate.GetString()}\n";
            
            if (root.TryGetProperty("EndDate", out var endDate))
                result += $"📅 تاريخ النهاية: {endDate.GetString()}\n";
            
            if (root.TryGetProperty("VacationDays", out var days))
                result += $"📊 عدد الأيام: {days.GetInt32()}\n";
            
            if (root.TryGetProperty("Reason", out var reason))
                result += $"📝 السبب: {reason.GetString()}\n";

            return result;
        }

        /// <summary>
        /// تنسيق بيانات الدورات
        /// </summary>
        private string FormatCourseData(JsonDocument jsonDoc)
        {
            var result = "🎓 بيانات الدورة التدريبية:\n";
            result += "═══════════════════════════════════════\n\n";

            var root = jsonDoc.RootElement;
            
            if (root.TryGetProperty("EmployeeName", out var empName))
                result += $"👤 اسم الموظف: {empName.GetString()}\n";
            
            if (root.TryGetProperty("EmployeeCode", out var empCode))
                result += $"🆔 الرقم الوظيفي: {empCode.GetString()}\n";
            
            if (root.TryGetProperty("CourseName", out var courseName))
                result += $"📚 اسم الدورة: {courseName.GetString()}\n";
            
            if (root.TryGetProperty("CourseLocation", out var location))
                result += $"📍 مكان الدورة: {location.GetString()}\n";
            
            if (root.TryGetProperty("CourseStartDate", out var startDate))
                result += $"📅 تاريخ البداية: {startDate.GetString()}\n";
            
            if (root.TryGetProperty("CourseEndDate", out var endDate))
                result += $"📅 تاريخ النهاية: {endDate.GetString()}\n";
            
            if (root.TryGetProperty("CourseDuration", out var duration))
                result += $"⏱️ مدة الدورة: {duration.GetInt32()} يوم\n";

            return result;
        }

        /// <summary>
        /// تنسيق بيانات الوقوعات الشهرية
        /// </summary>
        private string FormatMonthlyOccurrenceData(JsonDocument jsonDoc)
        {
            var result = "📅 بيانات الوقوعات الشهرية:\n";
            result += "═══════════════════════════════════════\n\n";

            var root = jsonDoc.RootElement;
            
            if (root.TryGetProperty("EmployeeName", out var empName))
                result += $"👤 اسم الموظف: {empName.GetString()}\n";
            
            if (root.TryGetProperty("EmployeeCode", out var empCode))
                result += $"🆔 الرقم الوظيفي: {empCode.GetString()}\n";
            
            if (root.TryGetProperty("Month", out var month))
                result += $"📅 الشهر: {month.GetInt32()}\n";
            
            if (root.TryGetProperty("Year", out var year))
                result += $"📅 السنة: {year.GetInt32()}\n";
            
            if (root.TryGetProperty("Group", out var group))
                result += $"👥 المجموعة: {group.GetString()}\n";

            // عرض بيانات الأيام إذا كانت موجودة
            if (root.TryGetProperty("Days", out var daysElement))
            {
                result += "\n📊 بيانات الأيام:\n";
                result += "─────────────────────────────────────\n";
                
                foreach (var dayProperty in daysElement.EnumerateObject())
                {
                    var dayNumber = dayProperty.Name.Replace("day_", "");
                    var dayValue = dayProperty.Value.GetString();
                    if (!string.IsNullOrEmpty(dayValue))
                    {
                        var statusIcon = GetDayStatusIcon(dayValue);
                        result += $"{statusIcon} اليوم {dayNumber}: {GetDayStatusText(dayValue)}\n";
                    }
                }
            }

            if (root.TryGetProperty("Notes", out var notes) && !string.IsNullOrEmpty(notes.GetString()))
                result += $"\n📝 الملاحظات: {notes.GetString()}\n";

            return result;
        }

        /// <summary>
        /// الحصول على أيقونة حالة اليوم
        /// </summary>
        private string GetDayStatusIcon(string status)
        {
            return status?.ToUpper() switch
            {
                "V" => "✅",
                "غ" => "❌",
                "ت" => "🏃",
                "ج" => "🏖️",
                "م" => "🏥",
                "د" => "🎓",
                _ => "❓"
            };
        }

        /// <summary>
        /// الحصول على نص حالة اليوم
        /// </summary>
        private string GetDayStatusText(string status)
        {
            return status?.ToUpper() switch
            {
                "V" => "حضور",
                "غ" => "غياب",
                "ت" => "هروب",
                "ج" => "إجازة",
                "م" => "مرضية",
                "د" => "دورة",
                _ => "غير محدد"
            };
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            if (keyData == Keys.Escape)
            {
                this.Close();
                return true;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }
    }
}