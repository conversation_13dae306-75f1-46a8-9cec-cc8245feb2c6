namespace EmployeeManagementSystem
{
    partial class CourseNotificationsForm
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            groupBox1 = new GroupBox();
            btnSendBulkNotifications = new Button();
            chkIsRead = new CheckBox();
            cmbPriority = new ComboBox();
            lblPriority = new Label();
            dtSentDate = new DateTimePicker();
            lblSentDate = new Label();
            txtMessage = new TextBox();
            lblMessage = new Label();
            cmbNotificationType = new ComboBox();
            lblNotificationType = new Label();
            cmbEmployeeName = new ComboBox();
            lblEmployeeName = new Label();
            cmbCourse = new ComboBox();
            lblCourse = new Label();
            btnExportExcel = new Button();
            btnClear = new Button();
            btnDelete = new Button();
            btnUpdate = new Button();
            btnAdd = new Button();
            dataGridView1 = new DataGridView();
            toolTip1 = new ToolTip(components);
            groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).BeginInit();
            SuspendLayout();
            // 
            // groupBox1
            // 
            groupBox1.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBox1.Controls.Add(btnSendBulkNotifications);
            groupBox1.Controls.Add(chkIsRead);
            groupBox1.Controls.Add(cmbPriority);
            groupBox1.Controls.Add(lblPriority);
            groupBox1.Controls.Add(dtSentDate);
            groupBox1.Controls.Add(lblSentDate);
            groupBox1.Controls.Add(txtMessage);
            groupBox1.Controls.Add(lblMessage);
            groupBox1.Controls.Add(cmbNotificationType);
            groupBox1.Controls.Add(lblNotificationType);
            groupBox1.Controls.Add(cmbEmployeeName);
            groupBox1.Controls.Add(lblEmployeeName);
            groupBox1.Controls.Add(cmbCourse);
            groupBox1.Controls.Add(lblCourse);
            groupBox1.Controls.Add(btnExportExcel);
            groupBox1.Controls.Add(btnClear);
            groupBox1.Controls.Add(btnDelete);
            groupBox1.Controls.Add(btnUpdate);
            groupBox1.Controls.Add(btnAdd);
            groupBox1.Font = new Font("Cairo", 12F, FontStyle.Bold);
            groupBox1.Location = new Point(12, 12);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(1000, 320);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            groupBox1.Text = "بيانات إشعارات الدورة";
            // 
            // btnSendBulkNotifications
            // 
            btnSendBulkNotifications.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnSendBulkNotifications.BackColor = Color.FromArgb(52, 152, 219);
            btnSendBulkNotifications.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnSendBulkNotifications.ForeColor = Color.White;
            btnSendBulkNotifications.Image = Properties.Resources.notification1_32px;
            btnSendBulkNotifications.ImageAlign = ContentAlignment.MiddleRight;
            btnSendBulkNotifications.Location = new Point(165, 260);
            btnSendBulkNotifications.Name = "btnSendBulkNotifications";
            btnSendBulkNotifications.Size = new Size(150, 46);
            btnSendBulkNotifications.TabIndex = 10;
            btnSendBulkNotifications.Text = "إرسال جماعي";
            btnSendBulkNotifications.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnSendBulkNotifications, "إرسال إشعار لجميع المسجلين في الدورة");
            btnSendBulkNotifications.UseVisualStyleBackColor = false;
            btnSendBulkNotifications.Click += btnSendBulkNotifications_Click;
            // 
            // chkIsRead
            // 
            chkIsRead.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            chkIsRead.AutoSize = true;
            chkIsRead.Font = new Font("Cairo", 12F, FontStyle.Bold);
            chkIsRead.Location = new Point(343, 204);
            chkIsRead.Name = "chkIsRead";
            chkIsRead.RightToLeft = RightToLeft.No;
            chkIsRead.Size = new Size(77, 34);
            chkIsRead.TabIndex = 6;
            chkIsRead.Text = "مقروء";
            chkIsRead.UseVisualStyleBackColor = true;
            // 
            // cmbPriority
            // 
            cmbPriority.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbPriority.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbPriority.Font = new Font("Cairo", 12F);
            cmbPriority.FormattingEnabled = true;
            cmbPriority.Items.AddRange(new object[] { "منخفض", "عادي", "مهم", "عاجل" });
            cmbPriority.Location = new Point(580, 160);
            cmbPriority.Name = "cmbPriority";
            cmbPriority.RightToLeft = RightToLeft.No;
            cmbPriority.Size = new Size(300, 38);
            cmbPriority.TabIndex = 4;
            // 
            // lblPriority
            // 
            lblPriority.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblPriority.AutoSize = true;
            lblPriority.Location = new Point(886, 163);
            lblPriority.Name = "lblPriority";
            lblPriority.Size = new Size(73, 30);
            lblPriority.TabIndex = 17;
            lblPriority.Text = "الأولوية:";
            // 
            // dtSentDate
            // 
            dtSentDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            dtSentDate.CustomFormat = "yyyy-MM-dd HH:mm";
            dtSentDate.Font = new Font("Cairo", 12F);
            dtSentDate.Format = DateTimePickerFormat.Custom;
            dtSentDate.Location = new Point(580, 200);
            dtSentDate.Name = "dtSentDate";
            dtSentDate.Size = new Size(300, 37);
            dtSentDate.TabIndex = 5;
            // 
            // lblSentDate
            // 
            lblSentDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblSentDate.AutoSize = true;
            lblSentDate.Location = new Point(886, 203);
            lblSentDate.Name = "lblSentDate";
            lblSentDate.Size = new Size(103, 30);
            lblSentDate.TabIndex = 15;
            lblSentDate.Text = "تاريخ الإرسال:";
            // 
            // txtMessage
            // 
            txtMessage.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtMessage.Font = new Font("Cairo", 12F);
            txtMessage.Location = new Point(20, 84);
            txtMessage.Multiline = true;
            txtMessage.Name = "txtMessage";
            txtMessage.RightToLeft = RightToLeft.Yes;
            txtMessage.Size = new Size(860, 70);
            txtMessage.TabIndex = 3;
            // 
            // lblMessage
            // 
            lblMessage.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblMessage.AutoSize = true;
            lblMessage.Location = new Point(886, 87);
            lblMessage.Name = "lblMessage";
            lblMessage.Size = new Size(69, 30);
            lblMessage.TabIndex = 13;
            lblMessage.Text = "الرسالة:";
            // 
            // cmbNotificationType
            // 
            cmbNotificationType.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbNotificationType.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbNotificationType.Font = new Font("Cairo", 12F);
            cmbNotificationType.FormattingEnabled = true;
            cmbNotificationType.Items.AddRange(new object[] { "بداية الدورة", "انتهاء الدورة", "تذكير", "إلغاء", "تأجيل", "عام" });
            cmbNotificationType.Location = new Point(580, 40);
            cmbNotificationType.Name = "cmbNotificationType";
            cmbNotificationType.RightToLeft = RightToLeft.No;
            cmbNotificationType.Size = new Size(300, 38);
            cmbNotificationType.TabIndex = 1;
            // 
            // lblNotificationType
            // 
            lblNotificationType.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblNotificationType.AutoSize = true;
            lblNotificationType.Location = new Point(886, 43);
            lblNotificationType.Name = "lblNotificationType";
            lblNotificationType.Size = new Size(96, 30);
            lblNotificationType.TabIndex = 11;
            lblNotificationType.Text = "نوع الإشعار:";
            // 
            // cmbEmployeeName
            // 
            cmbEmployeeName.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbEmployeeName.Font = new Font("Cairo", 12F);
            cmbEmployeeName.FormattingEnabled = true;
            cmbEmployeeName.Location = new Point(20, 40);
            cmbEmployeeName.Name = "cmbEmployeeName";
            cmbEmployeeName.RightToLeft = RightToLeft.No;
            cmbEmployeeName.Size = new Size(400, 38);
            cmbEmployeeName.TabIndex = 2;
            // 
            // lblEmployeeName
            // 
            lblEmployeeName.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblEmployeeName.AutoSize = true;
            lblEmployeeName.Location = new Point(426, 43);
            lblEmployeeName.Name = "lblEmployeeName";
            lblEmployeeName.Size = new Size(113, 30);
            lblEmployeeName.TabIndex = 9;
            lblEmployeeName.Text = "اسم الموظف:";
            // 
            // cmbCourse
            // 
            cmbCourse.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbCourse.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbCourse.Font = new Font("Cairo", 12F);
            cmbCourse.FormattingEnabled = true;
            cmbCourse.Location = new Point(20, 160);
            cmbCourse.Name = "cmbCourse";
            cmbCourse.RightToLeft = RightToLeft.No;
            cmbCourse.Size = new Size(400, 38);
            cmbCourse.TabIndex = 0;
            // 
            // lblCourse
            // 
            lblCourse.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblCourse.AutoSize = true;
            lblCourse.Location = new Point(426, 163);
            lblCourse.Name = "lblCourse";
            lblCourse.Size = new Size(61, 30);
            lblCourse.TabIndex = 7;
            lblCourse.Text = "الدورة:";
            // 
            // btnExportExcel
            // 
            btnExportExcel.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnExportExcel.BackColor = Color.Transparent;
            btnExportExcel.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnExportExcel.Image = Properties.Resources.microsoft_excel_2019_32px;
            btnExportExcel.ImageAlign = ContentAlignment.MiddleRight;
            btnExportExcel.Location = new Point(321, 260);
            btnExportExcel.Name = "btnExportExcel";
            btnExportExcel.Size = new Size(108, 46);
            btnExportExcel.TabIndex = 11;
            btnExportExcel.Text = "تصدير";
            btnExportExcel.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnExportExcel, "تصدير إلى Excel");
            btnExportExcel.UseVisualStyleBackColor = false;
            btnExportExcel.Click += btnExportExcel_Click;
            // 
            // btnClear
            // 
            btnClear.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnClear.BackColor = Color.Transparent;
            btnClear.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnClear.Image = Properties.Resources.clear_formatting_32px;
            btnClear.ImageAlign = ContentAlignment.MiddleRight;
            btnClear.Location = new Point(435, 260);
            btnClear.Name = "btnClear";
            btnClear.Size = new Size(90, 46);
            btnClear.TabIndex = 9;
            btnClear.Text = "إفراغ";
            btnClear.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnClear, "إفراغ الحقول");
            btnClear.UseVisualStyleBackColor = false;
            btnClear.Click += btnClear_Click;
            // 
            // btnDelete
            // 
            btnDelete.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnDelete.BackColor = Color.Transparent;
            btnDelete.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnDelete.Image = Properties.Resources.remove_32px;
            btnDelete.ImageAlign = ContentAlignment.MiddleRight;
            btnDelete.Location = new Point(531, 260);
            btnDelete.Name = "btnDelete";
            btnDelete.Size = new Size(90, 46);
            btnDelete.TabIndex = 12;
            btnDelete.Text = "حذف";
            btnDelete.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnDelete, "حذف");
            btnDelete.UseVisualStyleBackColor = false;
            btnDelete.Click += btnDelete_Click;
            // 
            // btnUpdate
            // 
            btnUpdate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnUpdate.BackColor = Color.Transparent;
            btnUpdate.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnUpdate.Image = Properties.Resources.edit_profile_32px;
            btnUpdate.ImageAlign = ContentAlignment.MiddleRight;
            btnUpdate.Location = new Point(627, 260);
            btnUpdate.Name = "btnUpdate";
            btnUpdate.Size = new Size(102, 46);
            btnUpdate.TabIndex = 8;
            btnUpdate.Text = "تعديل";
            btnUpdate.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnUpdate, "تعديل");
            btnUpdate.UseVisualStyleBackColor = false;
            btnUpdate.Click += btnUpdate_Click;
            // 
            // btnAdd
            // 
            btnAdd.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnAdd.BackColor = Color.Transparent;
            btnAdd.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnAdd.Image = Properties.Resources.ok_32px;
            btnAdd.ImageAlign = ContentAlignment.MiddleRight;
            btnAdd.Location = new Point(735, 260);
            btnAdd.Name = "btnAdd";
            btnAdd.Size = new Size(101, 46);
            btnAdd.TabIndex = 7;
            btnAdd.Text = "إضافة";
            btnAdd.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnAdd, "إضافة");
            btnAdd.UseVisualStyleBackColor = false;
            btnAdd.Click += btnAdd_Click;
            // 
            // dataGridView1
            // 
            dataGridView1.AllowUserToAddRows = false;
            dataGridView1.AllowUserToDeleteRows = false;
            dataGridView1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView1.Location = new Point(12, 338);
            dataGridView1.Name = "dataGridView1";
            dataGridView1.ReadOnly = true;
            dataGridView1.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView1.Size = new Size(1000, 310);
            dataGridView1.TabIndex = 13;
            dataGridView1.CellClick += dataGridView1_CellClick;
            // 
            // CourseNotificationsForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1024, 660);
            Controls.Add(dataGridView1);
            Controls.Add(groupBox1);
            Name = "CourseNotificationsForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "إدارة إشعارات الدورات التدريبية";
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).EndInit();
            ResumeLayout(false);
        }

        private GroupBox groupBox1;
        private Label lblCourse;
        private ComboBox cmbCourse;
        private Label lblEmployeeName;
        private ComboBox cmbEmployeeName;
        private Label lblNotificationType;
        private ComboBox cmbNotificationType;
        private Label lblMessage;
        private TextBox txtMessage;
        private Label lblSentDate;
        private DateTimePicker dtSentDate;
        private Label lblPriority;
        private ComboBox cmbPriority;
        private CheckBox chkIsRead;
        private Button btnAdd;
        private Button btnUpdate;
        private Button btnDelete;
        private Button btnClear;
        private Button btnExportExcel;
        private Button btnSendBulkNotifications;
        private DataGridView dataGridView1;
        private ToolTip toolTip1;
    }
}