using System;
using System.Data.SqlClient;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class ReportSettingsForm : Form
    {
        private string logoPath = "";

        public ReportSettingsForm()
        {
            InitializeComponent();
            LoadCurrentSettings();
        }

        private void LoadCurrentSettings()
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string query = @"
    SELECT TOP 1 ArabicHeaderText, EnglishHeaderText, CompanyLogo, ReportTitle, 
           ManagerName, SecurityOfficerName, CommanderName, DirectorateName
    FROM ReportSettings 
    ORDER BY Id DESC";


                    using (SqlCommand command = new SqlCommand(query, connection))
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            txtArabicHeader.Text = reader["ArabicHeaderText"]?.ToString() ?? txtArabicHeader.Text;
                            txtEnglishHeader.Text = reader["EnglishHeaderText"]?.ToString() ?? txtEnglishHeader.Text;
                            txtReportTitle.Text = reader["ReportTitle"]?.ToString() ?? txtReportTitle.Text;
                            txtManagerName.Text = reader["ManagerName"]?.ToString() ?? txtManagerName.Text;
                            txtSecurityOfficerName.Text = reader["SecurityOfficerName"]?.ToString() ?? txtSecurityOfficerName.Text;
                            txtCommanderName.Text = reader["CommanderName"]?.ToString() ?? txtCommanderName.Text;
                            txtDirectorate.Text = reader["DirectorateName"]?.ToString() ?? txtDirectorate.Text;

                            logoPath = reader["CompanyLogo"]?.ToString() ?? "";
                            if (!string.IsNullOrEmpty(logoPath) && File.Exists(logoPath))
                            {
                                picLogo.Image = Image.FromFile(logoPath);
                                lblLogoPath.Text = Path.GetFileName(logoPath);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnBrowseLogo_Click(object sender, EventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Filter = "Image Files|*.jpg;*.jpeg;*.png;*.bmp;*.gif",
                Title = "اختر شعار المؤسسة"
            };

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    picLogo.Image = Image.FromFile(openFileDialog.FileName);
                    logoPath = openFileDialog.FileName;
                    lblLogoPath.Text = Path.GetFileName(logoPath);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحميل الصورة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    
                    // حذف الإعدادات السابقة
                    string deleteQuery = "DELETE FROM ReportSettings";
                    using (SqlCommand deleteCommand = new SqlCommand(deleteQuery, connection))
                    {
                        deleteCommand.ExecuteNonQuery();
                    }

                    // إدراج الإعدادات الجديدة
                    string insertQuery = @"
    INSERT INTO ReportSettings 
        (ArabicHeaderText, EnglishHeaderText, CompanyLogo, ReportTitle, 
         ManagerName, SecurityOfficerName, CommanderName, DirectorateName, ModifiedBy)
    VALUES 
        (@ArabicHeaderText, @EnglishHeaderText, @CompanyLogo, @ReportTitle, 
         @ManagerName, @SecurityOfficerName, @CommanderName, @DirectorateName, @ModifiedBy)";


                    using (SqlCommand insertCommand = new SqlCommand(insertQuery, connection))
                    {
                        insertCommand.Parameters.AddWithValue("@ArabicHeaderText", txtArabicHeader.Text);
                        insertCommand.Parameters.AddWithValue("@EnglishHeaderText", txtEnglishHeader.Text);
                        insertCommand.Parameters.AddWithValue("@CompanyLogo", logoPath ?? "");
                        insertCommand.Parameters.AddWithValue("@ReportTitle", txtReportTitle.Text);
                        insertCommand.Parameters.AddWithValue("@ManagerName", txtManagerName.Text);
                        insertCommand.Parameters.AddWithValue("@SecurityOfficerName", txtSecurityOfficerName.Text);
                        insertCommand.Parameters.AddWithValue("@CommanderName", txtCommanderName.Text);
                        insertCommand.Parameters.AddWithValue("@ModifiedBy", Environment.UserName);
                        insertCommand.Parameters.AddWithValue("@DirectorateName", txtDirectorate.Text);

                        insertCommand.ExecuteNonQuery();
                    }
                }
                
                MessageBox.Show("تم حفظ الإعدادات بنجاح!", "نجح الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnResetToDefault_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد استعادة الإعدادات الافتراضية؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                txtArabicHeader.Text = "جـمهوية العـراق\nرئـاسـة الـوزراء\nهـيأة الحـشــد الشــعبـي\nالـدائرة الادارية و الـمـاليـة الـعـامـة";
                txtEnglishHeader.Text = "Republic Of Iraq\nPrime Minister\nPopular Mobilization Commission\nAdministrative & Public Finance Department";
                txtReportTitle.Text = "جدول الحضور والغياب";
                txtManagerName.Text = "مدير الادارة";
                txtSecurityOfficerName.Text = "ضابط الامن";
                txtCommanderName.Text = "امر التشكيل";
                txtDirectorate.Text = "اسم المديرية او التشكيل";
                picLogo.Image = null;
                logoPath = "";
                lblLogoPath.Text = "لم يتم اختيار شعار";
            }
        }
    }
}