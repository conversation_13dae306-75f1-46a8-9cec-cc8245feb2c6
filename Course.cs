using System;

namespace EmployeeManagementSystem
{
    public class Course
    {
        public int CourseId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string CourseType { get; set; } = string.Empty;
        public string CourseNumber { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int DaysCount { get; set; }
        public string GraduationGrade { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public int MaxParticipants { get; set; }
        public int CurrentParticipants { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Prerequisites { get; set; } = string.Empty;
        public bool CertificateIssued { get; set; }
        public decimal Cost { get; set; }
        public string Priority { get; set; } = string.Empty;
        public bool NotificationSent { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;
        public DateTime UpdatedDate { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public class CourseAttendance
    {
        public int AttendanceId { get; set; }
        public int CourseId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public DateTime AttendanceDate { get; set; }
        public bool IsPresent { get; set; }
        public string Notes { get; set; } = string.Empty;
        public string RecordedBy { get; set; } = string.Empty;
        public DateTime RecordedDate { get; set; }
    }

    public class CourseEvaluation
    {
        public int EvaluationId { get; set; }
        public int CourseId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public int Rating { get; set; }
        public string Comments { get; set; } = string.Empty;
        public DateTime EvaluationDate { get; set; }
    }

    public class CourseNotification
    {
        public int NotificationId { get; set; }
        public int CourseId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string NotificationType { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public DateTime SentDate { get; set; }
        public bool IsRead { get; set; }
        public string Priority { get; set; } = string.Empty;
    }
}