﻿++Solution 'HRMSDB' ‎ (1 of 1 project)
i:{00000000-0000-0000-0000-000000000000}:HRMSDB.sln
++EmployeeManagementSystem
i:{00000000-0000-0000-0000-000000000000}:EmployeeManagementSystem
++Dependencies
i:{************************************}:>2614
++Properties
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\properties\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\properties\
++HRMSWebApp
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\
++bin
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\
++Controllers
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\controllers\
++Database
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\database\
++Models
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\models\
++obj
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\
++Services
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\services\
++Views
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\scopedcss\views\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\scopedcss\views\
++wwwroot
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\
++css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\css\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\css\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\
++js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\js\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\js\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\js\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\js\
++lib
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\browser\lib\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\unix\lib\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\win\lib\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\
++favicon.ico
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\favicon.ico
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\favicon.ico
++appsettings.Development.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\appsettings.development.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\appsettings.development.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\appsettings.development.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\appsettings.development.json
++appsettings.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\appsettings.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\appsettings.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\appsettings.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\appsettings.json
++COURSES_FEATURE.md
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\courses_feature.md
++Print_Feature_Guide.md
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\print_feature_guide.md
++Program.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\program.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\program.cs
++QUICK_START.md
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\quick_start.md
++README.md
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\readme.md
++LoadingGui
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\loadinggui\
++Resources
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\
++ToastGui
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\toastgui\
++ToastForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\toastgui\toastform.cs
++AboutForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\aboutform.cs
++Accord.dll.config
i:{************************************}:c:\users\<USER>\.nuget\packages\accord\3.8.0\build\accord.dll.config
++activationForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\activationform.cs
++ActivationHelper.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\activationhelper.cs
++ActivityLog.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\activitylog.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\models\activitylog.cs
++ActivityLogDetailsForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\activitylogdetailsform.cs
++ActivityLogHelper.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\activityloghelper.cs
++ActivityLogService.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\activitylogservice.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\services\activitylogservice.cs
++ActivityLogViewerForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\activitylogviewerform.cs
++App.config
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\app.config
++Attendance.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\attendance.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\models\attendance.cs
++AttendanceForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\attendanceform.cs
++AttendanceReportForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\attendancereportform.cs
++BackupHelper.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\backuphelper.cs
++CameraForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\cameraform.cs
++ConStringHelper.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\constringhelper.cs
++ContactForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\contactform.cs
++Course.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\course.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\models\course.cs
++CourseForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\courseform.cs
++DailyDetailsForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\dailydetailsform.cs
++DashboardForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\dashboardform.cs
++DatabaseHelper.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\databasehelper.cs
++DocumentManagementForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\documentmanagementform.cs
++DocumentViewerForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\documentviewerform.cs
++Employee.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\employee.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\models\employee.cs
++Form1.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\form1.cs
++global.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\global.json
++GroupWorkPeriodForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\groupworkperiodform.cs
++HomeForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\homeform.cs
++hrms_errors.log
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrms_errors.log
++HRMSDB.ico
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmsdb.ico
++LoginForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\loginform.cs
++LoginForm.cs.backup
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\loginform.cs.backup
++MainForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\mainform.cs
++MonthlyOccurrencesForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\monthlyoccurrencesform.cs
++Notification.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\notification.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\models\notification.cs
++NotificationService.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\notificationservice.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\services\notificationservice.cs
++NotificationsForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\notificationsform.cs
++ReportSettings.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\reportsettings.cs
++ReportSettingsForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\reportsettingsform.cs
++SettingsForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\settingsform.cs
++SimpleDailyTrackingForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\simpledailytrackingform.cs
++SimpleEmployeeStatusForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\simpleemployeestatusform.cs
++SimpleEmployeeStatusFormV2.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\simpleemployeestatusformv2.cs
++StartForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\startform.cs
++ThemeManager.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\thememanager.cs
++ToastHelper.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\toasthelper.cs
++UIHelper.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\uihelper.cs
++UpdateVacations.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\updatevacations.cs
++User.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\user.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\models\user.cs
++UserForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\userform.cs
++Vacation.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\vacation.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\models\vacation.cs
++VacationForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\vacationform.cs
++WorkPeriod.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\workperiod.cs
++WorkPeriodForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\workperiodform.cs
++Analyzers
i:{************************************}:>2615
++Frameworks
i:{************************************}:>2621
++Packages
i:{************************************}:>2624
++Resources.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\properties\resources.resx
++Settings.settings
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\properties\settings.settings
++Debug
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\
++Release
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\
++AccountController.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\controllers\accountcontroller.cs
++AdminController.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\controllers\admincontroller.cs
++AttendanceController.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\controllers\attendancecontroller.cs
++CourseController.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\controllers\coursecontroller.cs
++EmployeeController.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\controllers\employeecontroller.cs
++HomeController.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\controllers\homecontroller.cs
++ImageController.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\controllers\imagecontroller.cs
++NotificationController.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\controllers\notificationcontroller.cs
++TestActivityLogController.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\controllers\testactivitylogcontroller.cs
++TestController.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\controllers\testcontroller.cs
++VacationController.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\controllers\vacationcontroller.cs
++AddBanFields.sql
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\database\addbanfields.sql
++CreateUsersTable.sql
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\database\createuserstable.sql
++FixUsersTable.sql
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\database\fixuserstable.sql
++ActivityLogsViewModel.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\models\activitylogsviewmodel.cs
++Document.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\models\document.cs
++ErrorViewModel.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\models\errorviewmodel.cs
++HRMSWebApp.csproj.nuget.dgspec.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\hrmswebapp.csproj.nuget.dgspec.json
++HRMSWebApp.csproj.nuget.g.props
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\hrmswebapp.csproj.nuget.g.props
++HRMSWebApp.csproj.nuget.g.targets
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\hrmswebapp.csproj.nuget.g.targets
++project.assets.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\project.assets.json
++project.nuget.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\project.nuget.cache
++staticwebassets.pack.sentinel
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\staticwebassets.pack.sentinel
++launchSettings.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\properties\launchsettings.json
++AttendanceService.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\services\attendanceservice.cs
++BanManagementService.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\services\banmanagementservice.cs
++CourseService.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\services\courseservice.cs
++DatabaseService.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\services\databaseservice.cs
++EmployeeService.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\services\employeeservice.cs
++NotificationBackgroundService.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\services\notificationbackgroundservice.cs
++UserService.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\services\userservice.cs
++VacationService.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\services\vacationservice.cs
++Account
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\account\
++Admin
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\admin\
++Attendance
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\attendance\
++Course
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\course\
++Employee
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\employee\
++Home
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\home\
++Notification
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\notification\
++Shared
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\shared\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\scopedcss\views\shared\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\scopedcss\views\shared\
++Vacation
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\vacation\
++_ViewImports.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\_viewimports.cshtml
++_ViewStart.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\_viewstart.cshtml
++attendance.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\css\attendance.css
++site.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\css\site.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\css\site.css
++site.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\js\site.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\js\site.js
++bootstrap
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\
++jquery
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery\
++jquery-validation
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery-validation\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery-validation\
++jquery-validation-unobtrusive
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery-validation-unobtrusive\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery-validation-unobtrusive\
++LoadingForm.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\loadinggui\loadingform.cs
++add_file_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\add_file_32px.png
++add_user_group_man_man_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\add_user_group_man_man_32px.png
++alarm_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\alarm_32px.png
++available_updates_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\available_updates_32px.png
++calendar_31_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\calendar_31_32px.png
++camera_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\camera_32px.png
++chart_increasing_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\chart_increasing_32px.png
++checked_checkbox_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\checked_checkbox_32px.png
++classroom_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\classroom_32px.png
++clear_formatting_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\clear_formatting_32px.png
++create_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\create_32px.png
++create_32px1.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\create_32px1.png
++crowd_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\crowd_32px.png
++data_backup_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\data_backup_32px.png
++database_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\database_32px.png
++database_restore_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\database_restore_32px.png
++delete_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\delete_32px.png
++documents_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\documents_32px.png
++edit_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\edit_32px.png
++edit_profile_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\edit_profile_32px.png
++filter_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\filter_32px.png
++hand_with_pen_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\hand_with_pen_32px.png
++help_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\help_32px.png
++home_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\home_32px.png
++image_file_add_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\image_file_add_32px.png
++info_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\info_32px.png
++key_2_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\key_2_32px.png
++management_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\management_32px.png
++microsoft_excel_2019_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\microsoft_excel_2019_32px.png
++notification_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\notification_32px.png
++ok_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\ok_32px.png
++phone_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\phone_32px.png
++photo.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\photo.png
++picture.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\picture.png
++picture_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\picture_32px.png
++print_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\print_32px.png
++remove_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\remove_32px.png
++remove_property_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\remove_property_32px.png
++save_close_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\save_close_32px.png
++save_close_32px1.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\save_close_32px1.png
++scanner_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\scanner_32px.png
++search_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\search_32px.png
++settings_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\settings_32px.png
++stop_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\stop_32px.png
++submit_progress_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\submit_progress_32px.png
++system_task_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\system_task_32px.png
++team.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\team.png
++time_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\time_32px.png
++traveler_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\traveler_32px.png
++update_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\update_32px.png
++user_groups_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\user_groups_32px.png
++Welcome.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\welcome.png
++Welcome1.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\welcome1.png
++whatsapp_32px.png
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\resources\whatsapp_32px.png
++ToastForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\toastgui\toastform.designer.cs
++ToastForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\toastgui\toastform.resx
++AboutForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\aboutform.designer.cs
++AboutForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\aboutform.resx
++activationForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\activationform.designer.cs
++activationForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\activationform.resx
++ActivityLogDetailsForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\activitylogdetailsform.designer.cs
++ActivityLogDetailsForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\activitylogdetailsform.resx
++ActivityLogViewerForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\activitylogviewerform.designer.cs
++ActivityLogViewerForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\activitylogviewerform.resx
++AttendanceForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\attendanceform.designer.cs
++AttendanceForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\attendanceform.resx
++AttendanceReportForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\attendancereportform.designer.cs
++AttendanceReportForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\attendancereportform.resx
++CameraForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\cameraform.designer.cs
++CameraForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\cameraform.resx
++ContactForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\contactform.designer.cs
++ContactForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\contactform.resx
++CourseForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\courseform.designer.cs
++CourseForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\courseform.resx
++DailyDetailsForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\dailydetailsform.designer.cs
++DailyDetailsForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\dailydetailsform.resx
++DashboardForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\dashboardform.designer.cs
++DashboardForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\dashboardform.resx
++DocumentManagementForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\documentmanagementform.designer.cs
++DocumentManagementForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\documentmanagementform.resx
++DocumentViewerForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\documentviewerform.designer.cs
++DocumentViewerForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\documentviewerform.resx
++Form1.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\form1.designer.cs
++Form1.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\form1.resx
++GroupWorkPeriodForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\groupworkperiodform.designer.cs
++GroupWorkPeriodForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\groupworkperiodform.resx
++HomeForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\homeform.designer.cs
++HomeForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\homeform.resx
++LoginForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\loginform.designer.cs
++LoginForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\loginform.resx
++MainForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\mainform.designer.cs
++MainForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\mainform.resx
++MonthlyOccurrencesForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\monthlyoccurrencesform.designer.cs
++MonthlyOccurrencesForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\monthlyoccurrencesform.resx
++NotificationsForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\notificationsform.designer.cs
++NotificationsForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\notificationsform.resx
++ReportSettingsForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\reportsettingsform.designer.cs
++ReportSettingsForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\reportsettingsform.resx
++SettingsForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\settingsform.designer.cs
++SettingsForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\settingsform.resx
++SimpleDailyTrackingForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\simpledailytrackingform.designer.cs
++SimpleDailyTrackingForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\simpledailytrackingform.resx
++SimpleEmployeeStatusForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\simpleemployeestatusform.designer.cs
++SimpleEmployeeStatusFormV2.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\simpleemployeestatusformv2.designer.cs
++SimpleEmployeeStatusFormV2.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\simpleemployeestatusformv2.resx
++StartForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\startform.designer.cs
++StartForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\startform.resx
++UserForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\userform.designer.cs
++UserForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\userform.resx
++VacationForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\vacationform.designer.cs
++VacationForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\vacationform.resx
++WorkPeriodForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\workperiodform.designer.cs
++WorkPeriodForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\workperiodform.resx
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{************************************}:c:\program files\dotnet\sdk\6.0.428\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{************************************}:c:\program files\dotnet\sdk\6.0.428\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++System.Text.Json.SourceGeneration
i:{************************************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\6.0.36\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Windows.Forms.Analyzers
i:{************************************}:c:\program files\dotnet\packs\microsoft.windowsdesktop.app.ref\6.0.36\analyzers\dotnet\system.windows.forms.analyzers.dll
++System.Windows.Forms.Analyzers.CSharp
i:{************************************}:c:\program files\dotnet\packs\microsoft.windowsdesktop.app.ref\6.0.36\analyzers\dotnet\cs\system.windows.forms.analyzers.csharp.dll
++Microsoft.NETCore.App
i:{************************************}:>2622
++Microsoft.WindowsDesktop.App.WindowsForms
i:{************************************}:>2623
++Accord (3.8.0)
i:{************************************}:>2625
++Accord.Imaging (3.8.0)
i:{************************************}:>2636
++Accord.Vision (3.8.0)
i:{************************************}:>2634
++AForge (2.2.5)
i:{************************************}:>2632
++AForge.Video.DirectShow (2.2.5)
i:{************************************}:>2627
++ClosedXML (0.104.2)
i:{************************************}:>2635
++Hardware.Info (101.0.1)
i:{************************************}:>2628
++OpenCvSharp4 (4.11.0.20250507)
i:{************************************}:>2629
++OpenCvSharp4.Extensions (4.11.0.20250507)
i:{************************************}:>2633
++OpenCvSharp4.runtime.win (4.11.0.20250507)
i:{************************************}:>2631
++ScottPlot.WinForms (4.1.68)
i:{************************************}:>2626
++System.Data.SqlClient (4.9.0)
i:{************************************}:>2630
++Resources.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\properties\resources.designer.cs
++Settings.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\properties\settings.designer.cs
++net6.0
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\browser\lib\net6.0\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\unix\lib\net6.0\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\win\lib\net6.0\
++net8.0
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net8.0\
++Login.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\account\login.cshtml
++ActivityLogs.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\admin\activitylogs.cshtml
++BannedEmployees.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\admin\bannedemployees.cshtml
++Dashboard.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\admin\dashboard.cshtml
++EmployeeDetails.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\admin\employeedetails.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\attendance\employeedetails.cshtml
++Employees.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\admin\employees.cshtml
++PendingVacations.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\admin\pendingvacations.cshtml
++PrintVacationRequest.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\admin\printvacationrequest.cshtml
++Users.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\admin\users.cshtml
++VacationDetails.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\admin\vacationdetails.cshtml
++Vacations.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\admin\vacations.cshtml
++AllAttendance.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\attendance\allattendance.cshtml
++MyAttendance.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\attendance\myattendance.cshtml
++ActiveCourses.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\course\activecourses.cshtml
++AllCourses.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\course\allcourses.cshtml
++CompletedCourses.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\course\completedcourses.cshtml
++Details.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\course\details.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\employee\details.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\vacation\details.cshtml
++MyCourses.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\course\mycourses.cshtml
++UpcomingCourses.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\course\upcomingcourses.cshtml
++ChangePassword.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\employee\changepassword.cshtml
++Edit.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\employee\edit.cshtml
++EditAdmin.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\employee\editadmin.cshtml
++Profile.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\employee\profile.cshtml
++UploadDocument.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\employee\uploaddocument.cshtml
++Index.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\home\index.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\notification\index.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\vacation\index.cshtml
++Privacy.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\home\privacy.cshtml
++_Layout.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\shared\_layout.cshtml
++_Layout.cshtml.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\shared\_layout.cshtml.css
++_LoginLayout.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\shared\_loginlayout.cshtml
++_ValidationScriptsPartial.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\shared\_validationscriptspartial.cshtml
++Error.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\shared\error.cshtml
++Request.cshtml
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\views\vacation\request.cshtml
++dist
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery\dist\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery-validation\dist\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery\dist\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery-validation\dist\
++LICENSE
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\license
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\license
++LICENSE.txt
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery\license.txt
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery-validation-unobtrusive\license.txt
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery\license.txt
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery-validation-unobtrusive\license.txt
++LICENSE.md
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery-validation\license.md
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery-validation\license.md
++jquery.validate.unobtrusive.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.js
++jquery.validate.unobtrusive.min.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js
++LoadingForm.Designer.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\loadinggui\loadingform.designer.cs
++LoadingForm.resx
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\loadinggui\loadingform.resx
++runtimes
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\
++HRMSWebApp.deps.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\hrmswebapp.deps.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\hrmswebapp.deps.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\hrmswebapp.deps.json
++HRMSWebApp.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\hrmswebapp.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\hrmswebapp.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\hrmswebapp.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\ref\hrmswebapp.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\refint\hrmswebapp.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\hrmswebapp.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\ref\hrmswebapp.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\refint\hrmswebapp.dll
++HRMSWebApp.exe
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\hrmswebapp.exe
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\hrmswebapp.exe
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\hrmswebapp.exe
++HRMSWebApp.pdb
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\hrmswebapp.pdb
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\hrmswebapp.pdb
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\hrmswebapp.pdb
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\hrmswebapp.pdb
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\hrmswebapp.pdb
++HRMSWebApp.runtimeconfig.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\hrmswebapp.runtimeconfig.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\hrmswebapp.runtimeconfig.json
++HRMSWebApp.staticwebassets.runtime.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\hrmswebapp.staticwebassets.runtime.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\hrmswebapp.staticwebassets.runtime.json
++Microsoft.Data.SqlClient.SNI.arm64.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\microsoft.data.sqlclient.sni.arm64.dll
++Microsoft.Data.SqlClient.SNI.arm64.pdb
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\microsoft.data.sqlclient.sni.arm64.pdb
++Microsoft.Data.SqlClient.SNI.x64.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\microsoft.data.sqlclient.sni.x64.dll
++Microsoft.Data.SqlClient.SNI.x64.pdb
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\microsoft.data.sqlclient.sni.x64.pdb
++Microsoft.Data.SqlClient.SNI.x86.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\microsoft.data.sqlclient.sni.x86.dll
++Microsoft.Data.SqlClient.SNI.x86.pdb
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\microsoft.data.sqlclient.sni.x86.pdb
++Microsoft.Extensions.Caching.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\microsoft.extensions.caching.abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.caching.abstractions.dll
++Microsoft.Extensions.Configuration.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\microsoft.extensions.configuration.abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.configuration.abstractions.dll
++Microsoft.Extensions.DependencyInjection.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\microsoft.extensions.dependencyinjection.abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.dependencyinjection.abstractions.dll
++Microsoft.Extensions.Diagnostics.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\microsoft.extensions.diagnostics.abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.diagnostics.abstractions.dll
++Microsoft.Extensions.FileProviders.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\microsoft.extensions.fileproviders.abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.fileproviders.abstractions.dll
++Microsoft.Extensions.Hosting.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\microsoft.extensions.hosting.abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.hosting.abstractions.dll
++Microsoft.Extensions.Logging.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\microsoft.extensions.logging.abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.logging.abstractions.dll
++Microsoft.Extensions.Options.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\microsoft.extensions.options.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.options.dll
++Microsoft.Extensions.Primitives.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\microsoft.extensions.primitives.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.primitives.dll
++System.Data.SqlClient.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\system.data.sqlclient.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.data.sqlclient.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\unix\lib\net6.0\system.data.sqlclient.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\win\lib\net6.0\system.data.sqlclient.dll
++System.Diagnostics.DiagnosticSource.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\system.diagnostics.diagnosticsource.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.diagnostics.diagnosticsource.dll
++System.Formats.Asn1.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\system.formats.asn1.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.formats.asn1.dll
++System.Security.Cryptography.Pkcs.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\system.security.cryptography.pkcs.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.security.cryptography.pkcs.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\win\lib\net6.0\system.security.cryptography.pkcs.dll
++System.Security.Cryptography.Xml.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\system.security.cryptography.xml.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.security.cryptography.xml.dll
++System.Text.Encodings.Web.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\system.text.encodings.web.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.text.encodings.web.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\browser\lib\net6.0\system.text.encodings.web.dll
++win-x64
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net8.0\win-x64\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\win-x64\
++ref
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\ref\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\ref\
++refint
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\refint\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\refint\
++scopedcss
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\scopedcss\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\scopedcss\
++.NETCoreApp,Version=v6.0.AssemblyAttributes.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\.netcoreapp,version=v6.0.assemblyattributes.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\.netcoreapp,version=v6.0.assemblyattributes.cs
++ApiEndpoints.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\apiendpoints.json
++apphost.exe
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\apphost.exe
++HRMSWebApp.AssemblyInfo.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\hrmswebapp.assemblyinfo.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\hrmswebapp.assemblyinfo.cs
++HRMSWebApp.AssemblyInfoInputs.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\hrmswebapp.assemblyinfoinputs.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\hrmswebapp.assemblyinfoinputs.cache
++HRMSWebApp.assets.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\hrmswebapp.assets.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net8.0\win-x64\hrmswebapp.assets.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\hrmswebapp.assets.cache
++HRMSWebApp.csproj.AssemblyReference.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\hrmswebapp.csproj.assemblyreference.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net8.0\win-x64\hrmswebapp.csproj.assemblyreference.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\hrmswebapp.csproj.assemblyreference.cache
++HRMSWebApp.csproj.BuildWithSkipAnalyzers
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\hrmswebapp.csproj.buildwithskipanalyzers
++HRMSWebApp.csproj.CopyComplete
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\hrmswebapp.csproj.copycomplete
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\hrmswebapp.csproj.copycomplete
++HRMSWebApp.csproj.CoreCompileInputs.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\hrmswebapp.csproj.corecompileinputs.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\hrmswebapp.csproj.corecompileinputs.cache
++HRMSWebApp.csproj.FileListAbsolute.txt
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\hrmswebapp.csproj.filelistabsolute.txt
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\hrmswebapp.csproj.filelistabsolute.txt
++HRMSWebApp.GeneratedMSBuildEditorConfig.editorconfig
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\hrmswebapp.generatedmsbuildeditorconfig.editorconfig
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\hrmswebapp.generatedmsbuildeditorconfig.editorconfig
++HRMSWebApp.genruntimeconfig.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\hrmswebapp.genruntimeconfig.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\hrmswebapp.genruntimeconfig.cache
++HRMSWebApp.GlobalUsings.g.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\hrmswebapp.globalusings.g.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net8.0\win-x64\hrmswebapp.globalusings.g.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\hrmswebapp.globalusings.g.cs
++HRMSWebApp.MvcApplicationPartsAssemblyInfo.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\hrmswebapp.mvcapplicationpartsassemblyinfo.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\hrmswebapp.mvcapplicationpartsassemblyinfo.cache
++HRMSWebApp.RazorAssemblyInfo.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\hrmswebapp.razorassemblyinfo.cache
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\hrmswebapp.razorassemblyinfo.cache
++HRMSWebApp.RazorAssemblyInfo.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\hrmswebapp.razorassemblyinfo.cs
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\hrmswebapp.razorassemblyinfo.cs
++staticwebassets.build.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\staticwebassets.build.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\staticwebassets.build.json
++staticwebassets.development.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\staticwebassets.development.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\staticwebassets.development.json
++jquery.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery\dist\jquery.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery\dist\jquery.js
++jquery.min.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery\dist\jquery.min.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery\dist\jquery.min.js
++jquery.min.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery\dist\jquery.min.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery\dist\jquery.min.map
++additional-methods.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery-validation\dist\additional-methods.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery-validation\dist\additional-methods.js
++additional-methods.min.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery-validation\dist\additional-methods.min.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery-validation\dist\additional-methods.min.js
++jquery.validate.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery-validation\dist\jquery.validate.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery-validation\dist\jquery.validate.js
++jquery.validate.min.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js
++browser
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\browser\
++unix
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\unix\
++win
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\win\
++win-arm64
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\win-arm64\
++win-x86
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\win-x86\
++publish
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\
++api-ms-win-core-console-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-console-l1-1-0.dll
++api-ms-win-core-console-l1-2-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-console-l1-2-0.dll
++api-ms-win-core-datetime-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-datetime-l1-1-0.dll
++api-ms-win-core-debug-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-debug-l1-1-0.dll
++api-ms-win-core-errorhandling-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-errorhandling-l1-1-0.dll
++api-ms-win-core-fibers-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-fibers-l1-1-0.dll
++api-ms-win-core-file-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-file-l1-1-0.dll
++api-ms-win-core-file-l1-2-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-file-l1-2-0.dll
++api-ms-win-core-file-l2-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-file-l2-1-0.dll
++api-ms-win-core-handle-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-handle-l1-1-0.dll
++api-ms-win-core-heap-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-heap-l1-1-0.dll
++api-ms-win-core-interlocked-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-interlocked-l1-1-0.dll
++api-ms-win-core-libraryloader-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-libraryloader-l1-1-0.dll
++api-ms-win-core-localization-l1-2-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-localization-l1-2-0.dll
++api-ms-win-core-memory-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-memory-l1-1-0.dll
++api-ms-win-core-namedpipe-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-namedpipe-l1-1-0.dll
++api-ms-win-core-processenvironment-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-processenvironment-l1-1-0.dll
++api-ms-win-core-processthreads-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-processthreads-l1-1-0.dll
++api-ms-win-core-processthreads-l1-1-1.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-processthreads-l1-1-1.dll
++api-ms-win-core-profile-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-profile-l1-1-0.dll
++api-ms-win-core-rtlsupport-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-rtlsupport-l1-1-0.dll
++api-ms-win-core-string-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-string-l1-1-0.dll
++api-ms-win-core-synch-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-synch-l1-1-0.dll
++api-ms-win-core-synch-l1-2-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-synch-l1-2-0.dll
++api-ms-win-core-sysinfo-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-sysinfo-l1-1-0.dll
++api-ms-win-core-timezone-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-timezone-l1-1-0.dll
++api-ms-win-core-util-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-core-util-l1-1-0.dll
++api-ms-win-crt-conio-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-crt-conio-l1-1-0.dll
++api-ms-win-crt-convert-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-crt-convert-l1-1-0.dll
++api-ms-win-crt-environment-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-crt-environment-l1-1-0.dll
++api-ms-win-crt-filesystem-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-crt-filesystem-l1-1-0.dll
++api-ms-win-crt-heap-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-crt-heap-l1-1-0.dll
++api-ms-win-crt-locale-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-crt-locale-l1-1-0.dll
++api-ms-win-crt-math-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-crt-math-l1-1-0.dll
++api-ms-win-crt-multibyte-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-crt-multibyte-l1-1-0.dll
++api-ms-win-crt-private-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-crt-private-l1-1-0.dll
++api-ms-win-crt-process-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-crt-process-l1-1-0.dll
++api-ms-win-crt-runtime-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-crt-runtime-l1-1-0.dll
++api-ms-win-crt-stdio-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-crt-stdio-l1-1-0.dll
++api-ms-win-crt-string-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-crt-string-l1-1-0.dll
++api-ms-win-crt-time-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-crt-time-l1-1-0.dll
++api-ms-win-crt-utility-l1-1-0.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\api-ms-win-crt-utility-l1-1-0.dll
++aspnetcorev2_inprocess.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\aspnetcorev2_inprocess.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\aspnetcorev2_inprocess.dll
++clretwrc.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\clretwrc.dll
++clrjit.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\clrjit.dll
++coreclr.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\coreclr.dll
++createdump.exe
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\createdump.exe
++dbgshim.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\dbgshim.dll
++hostfxr.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\hostfxr.dll
++hostpolicy.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\hostpolicy.dll
++Microsoft.AspNetCore.Antiforgery.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.antiforgery.dll
++Microsoft.AspNetCore.Authentication.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.authentication.abstractions.dll
++Microsoft.AspNetCore.Authentication.Cookies.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.authentication.cookies.dll
++Microsoft.AspNetCore.Authentication.Core.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.authentication.core.dll
++Microsoft.AspNetCore.Authentication.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.authentication.dll
++Microsoft.AspNetCore.Authentication.OAuth.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.authentication.oauth.dll
++Microsoft.AspNetCore.Authorization.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.authorization.dll
++Microsoft.AspNetCore.Authorization.Policy.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.authorization.policy.dll
++Microsoft.AspNetCore.Components.Authorization.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.components.authorization.dll
++Microsoft.AspNetCore.Components.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.components.dll
++Microsoft.AspNetCore.Components.Forms.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.components.forms.dll
++Microsoft.AspNetCore.Components.Server.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.components.server.dll
++Microsoft.AspNetCore.Components.Web.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.components.web.dll
++Microsoft.AspNetCore.Connections.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.connections.abstractions.dll
++Microsoft.AspNetCore.CookiePolicy.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.cookiepolicy.dll
++Microsoft.AspNetCore.Cors.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.cors.dll
++Microsoft.AspNetCore.Cryptography.Internal.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.cryptography.internal.dll
++Microsoft.AspNetCore.Cryptography.KeyDerivation.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.cryptography.keyderivation.dll
++Microsoft.AspNetCore.DataProtection.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.dataprotection.abstractions.dll
++Microsoft.AspNetCore.DataProtection.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.dataprotection.dll
++Microsoft.AspNetCore.DataProtection.Extensions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.dataprotection.extensions.dll
++Microsoft.AspNetCore.Diagnostics.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.diagnostics.abstractions.dll
++Microsoft.AspNetCore.Diagnostics.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.diagnostics.dll
++Microsoft.AspNetCore.Diagnostics.HealthChecks.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.diagnostics.healthchecks.dll
++Microsoft.AspNetCore.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.dll
++Microsoft.AspNetCore.HostFiltering.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.hostfiltering.dll
++Microsoft.AspNetCore.Hosting.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.hosting.abstractions.dll
++Microsoft.AspNetCore.Hosting.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.hosting.dll
++Microsoft.AspNetCore.Hosting.Server.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.hosting.server.abstractions.dll
++Microsoft.AspNetCore.Html.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.html.abstractions.dll
++Microsoft.AspNetCore.Http.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.http.abstractions.dll
++Microsoft.AspNetCore.Http.Connections.Common.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.http.connections.common.dll
++Microsoft.AspNetCore.Http.Connections.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.http.connections.dll
++Microsoft.AspNetCore.Http.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.http.dll
++Microsoft.AspNetCore.Http.Extensions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.http.extensions.dll
++Microsoft.AspNetCore.Http.Features.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.http.features.dll
++Microsoft.AspNetCore.Http.Results.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.http.results.dll
++Microsoft.AspNetCore.HttpLogging.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.httplogging.dll
++Microsoft.AspNetCore.HttpOverrides.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.httpoverrides.dll
++Microsoft.AspNetCore.HttpsPolicy.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.httpspolicy.dll
++Microsoft.AspNetCore.Identity.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.identity.dll
++Microsoft.AspNetCore.Localization.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.localization.dll
++Microsoft.AspNetCore.Localization.Routing.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.localization.routing.dll
++Microsoft.AspNetCore.Metadata.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.metadata.dll
++Microsoft.AspNetCore.Mvc.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.mvc.abstractions.dll
++Microsoft.AspNetCore.Mvc.ApiExplorer.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.mvc.apiexplorer.dll
++Microsoft.AspNetCore.Mvc.Core.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.mvc.core.dll
++Microsoft.AspNetCore.Mvc.Cors.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.mvc.cors.dll
++Microsoft.AspNetCore.Mvc.DataAnnotations.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.mvc.dataannotations.dll
++Microsoft.AspNetCore.Mvc.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.mvc.dll
++Microsoft.AspNetCore.Mvc.Formatters.Json.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.mvc.formatters.json.dll
++Microsoft.AspNetCore.Mvc.Formatters.Xml.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.mvc.formatters.xml.dll
++Microsoft.AspNetCore.Mvc.Localization.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.mvc.localization.dll
++Microsoft.AspNetCore.Mvc.Razor.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.mvc.razor.dll
++Microsoft.AspNetCore.Mvc.RazorPages.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.mvc.razorpages.dll
++Microsoft.AspNetCore.Mvc.TagHelpers.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.mvc.taghelpers.dll
++Microsoft.AspNetCore.Mvc.ViewFeatures.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.mvc.viewfeatures.dll
++Microsoft.AspNetCore.Razor.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.razor.dll
++Microsoft.AspNetCore.Razor.Runtime.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.razor.runtime.dll
++Microsoft.AspNetCore.ResponseCaching.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.responsecaching.abstractions.dll
++Microsoft.AspNetCore.ResponseCaching.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.responsecaching.dll
++Microsoft.AspNetCore.ResponseCompression.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.responsecompression.dll
++Microsoft.AspNetCore.Rewrite.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.rewrite.dll
++Microsoft.AspNetCore.Routing.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.routing.abstractions.dll
++Microsoft.AspNetCore.Routing.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.routing.dll
++Microsoft.AspNetCore.Server.HttpSys.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.server.httpsys.dll
++Microsoft.AspNetCore.Server.IIS.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.server.iis.dll
++Microsoft.AspNetCore.Server.IISIntegration.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.server.iisintegration.dll
++Microsoft.AspNetCore.Server.Kestrel.Core.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.server.kestrel.core.dll
++Microsoft.AspNetCore.Server.Kestrel.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.server.kestrel.dll
++Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.server.kestrel.transport.quic.dll
++Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.server.kestrel.transport.sockets.dll
++Microsoft.AspNetCore.Session.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.session.dll
++Microsoft.AspNetCore.SignalR.Common.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.signalr.common.dll
++Microsoft.AspNetCore.SignalR.Core.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.signalr.core.dll
++Microsoft.AspNetCore.SignalR.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.signalr.dll
++Microsoft.AspNetCore.SignalR.Protocols.Json.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.signalr.protocols.json.dll
++Microsoft.AspNetCore.StaticFiles.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.staticfiles.dll
++Microsoft.AspNetCore.WebSockets.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.websockets.dll
++Microsoft.AspNetCore.WebUtilities.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.aspnetcore.webutilities.dll
++Microsoft.CSharp.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.csharp.dll
++Microsoft.DiaSymReader.Native.amd64.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.diasymreader.native.amd64.dll
++Microsoft.Extensions.Caching.Memory.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.caching.memory.dll
++Microsoft.Extensions.Configuration.Binder.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.configuration.binder.dll
++Microsoft.Extensions.Configuration.CommandLine.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.configuration.commandline.dll
++Microsoft.Extensions.Configuration.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.configuration.dll
++Microsoft.Extensions.Configuration.EnvironmentVariables.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.configuration.environmentvariables.dll
++Microsoft.Extensions.Configuration.FileExtensions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.configuration.fileextensions.dll
++Microsoft.Extensions.Configuration.Ini.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.configuration.ini.dll
++Microsoft.Extensions.Configuration.Json.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.configuration.json.dll
++Microsoft.Extensions.Configuration.KeyPerFile.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.configuration.keyperfile.dll
++Microsoft.Extensions.Configuration.UserSecrets.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.configuration.usersecrets.dll
++Microsoft.Extensions.Configuration.Xml.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.configuration.xml.dll
++Microsoft.Extensions.DependencyInjection.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.dependencyinjection.dll
++Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.diagnostics.healthchecks.abstractions.dll
++Microsoft.Extensions.Diagnostics.HealthChecks.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.diagnostics.healthchecks.dll
++Microsoft.Extensions.Features.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.features.dll
++Microsoft.Extensions.FileProviders.Composite.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.fileproviders.composite.dll
++Microsoft.Extensions.FileProviders.Embedded.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.fileproviders.embedded.dll
++Microsoft.Extensions.FileProviders.Physical.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.fileproviders.physical.dll
++Microsoft.Extensions.FileSystemGlobbing.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.filesystemglobbing.dll
++Microsoft.Extensions.Hosting.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.hosting.dll
++Microsoft.Extensions.Http.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.http.dll
++Microsoft.Extensions.Identity.Core.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.identity.core.dll
++Microsoft.Extensions.Identity.Stores.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.identity.stores.dll
++Microsoft.Extensions.Localization.Abstractions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.localization.abstractions.dll
++Microsoft.Extensions.Localization.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.localization.dll
++Microsoft.Extensions.Logging.Configuration.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.logging.configuration.dll
++Microsoft.Extensions.Logging.Console.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.logging.console.dll
++Microsoft.Extensions.Logging.Debug.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.logging.debug.dll
++Microsoft.Extensions.Logging.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.logging.dll
++Microsoft.Extensions.Logging.EventLog.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.logging.eventlog.dll
++Microsoft.Extensions.Logging.EventSource.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.logging.eventsource.dll
++Microsoft.Extensions.Logging.TraceSource.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.logging.tracesource.dll
++Microsoft.Extensions.ObjectPool.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.objectpool.dll
++Microsoft.Extensions.Options.ConfigurationExtensions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.options.configurationextensions.dll
++Microsoft.Extensions.Options.DataAnnotations.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.options.dataannotations.dll
++Microsoft.Extensions.WebEncoders.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.extensions.webencoders.dll
++Microsoft.JSInterop.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.jsinterop.dll
++Microsoft.Net.Http.Headers.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.net.http.headers.dll
++Microsoft.VisualBasic.Core.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.visualbasic.core.dll
++Microsoft.VisualBasic.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.visualbasic.dll
++Microsoft.Win32.Primitives.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.win32.primitives.dll
++Microsoft.Win32.Registry.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\microsoft.win32.registry.dll
++mscordaccore.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\mscordaccore.dll
++mscordaccore_amd64_amd64_6.0.3624.51421.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\mscordaccore_amd64_amd64_6.0.3624.51421.dll
++mscordbi.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\mscordbi.dll
++mscorlib.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\mscorlib.dll
++mscorrc.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\mscorrc.dll
++msquic.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\msquic.dll
++netstandard.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\netstandard.dll
++sni.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\sni.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\sni.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\win-arm64\native\sni.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\win-x64\native\sni.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\win-x86\native\sni.dll
++System.AppContext.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.appcontext.dll
++System.Buffers.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.buffers.dll
++System.Collections.Concurrent.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.collections.concurrent.dll
++System.Collections.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.collections.dll
++System.Collections.Immutable.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.collections.immutable.dll
++System.Collections.NonGeneric.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.collections.nongeneric.dll
++System.Collections.Specialized.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.collections.specialized.dll
++System.ComponentModel.Annotations.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.componentmodel.annotations.dll
++System.ComponentModel.DataAnnotations.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.componentmodel.dataannotations.dll
++System.ComponentModel.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.componentmodel.dll
++System.ComponentModel.EventBasedAsync.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.componentmodel.eventbasedasync.dll
++System.ComponentModel.Primitives.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.componentmodel.primitives.dll
++System.ComponentModel.TypeConverter.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.componentmodel.typeconverter.dll
++System.Configuration.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.configuration.dll
++System.Console.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.console.dll
++System.Core.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.core.dll
++System.Data.Common.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.data.common.dll
++System.Data.DataSetExtensions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.data.datasetextensions.dll
++System.Data.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.data.dll
++System.Diagnostics.Contracts.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.diagnostics.contracts.dll
++System.Diagnostics.Debug.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.diagnostics.debug.dll
++System.Diagnostics.EventLog.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.diagnostics.eventlog.dll
++System.Diagnostics.EventLog.Messages.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.diagnostics.eventlog.messages.dll
++System.Diagnostics.FileVersionInfo.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.diagnostics.fileversioninfo.dll
++System.Diagnostics.Process.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.diagnostics.process.dll
++System.Diagnostics.StackTrace.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.diagnostics.stacktrace.dll
++System.Diagnostics.TextWriterTraceListener.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.diagnostics.textwritertracelistener.dll
++System.Diagnostics.Tools.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.diagnostics.tools.dll
++System.Diagnostics.TraceSource.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.diagnostics.tracesource.dll
++System.Diagnostics.Tracing.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.diagnostics.tracing.dll
++System.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.dll
++System.Drawing.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.drawing.dll
++System.Drawing.Primitives.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.drawing.primitives.dll
++System.Dynamic.Runtime.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.dynamic.runtime.dll
++System.Globalization.Calendars.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.globalization.calendars.dll
++System.Globalization.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.globalization.dll
++System.Globalization.Extensions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.globalization.extensions.dll
++System.IO.Compression.Brotli.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.compression.brotli.dll
++System.IO.Compression.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.compression.dll
++System.IO.Compression.FileSystem.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.compression.filesystem.dll
++System.IO.Compression.Native.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.compression.native.dll
++System.IO.Compression.ZipFile.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.compression.zipfile.dll
++System.IO.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.dll
++System.IO.FileSystem.AccessControl.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.filesystem.accesscontrol.dll
++System.IO.FileSystem.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.filesystem.dll
++System.IO.FileSystem.DriveInfo.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.filesystem.driveinfo.dll
++System.IO.FileSystem.Primitives.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.filesystem.primitives.dll
++System.IO.FileSystem.Watcher.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.filesystem.watcher.dll
++System.IO.IsolatedStorage.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.isolatedstorage.dll
++System.IO.MemoryMappedFiles.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.memorymappedfiles.dll
++System.IO.Pipelines.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.pipelines.dll
++System.IO.Pipes.AccessControl.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.pipes.accesscontrol.dll
++System.IO.Pipes.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.pipes.dll
++System.IO.UnmanagedMemoryStream.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.io.unmanagedmemorystream.dll
++System.Linq.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.linq.dll
++System.Linq.Expressions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.linq.expressions.dll
++System.Linq.Parallel.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.linq.parallel.dll
++System.Linq.Queryable.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.linq.queryable.dll
++System.Memory.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.memory.dll
++System.Net.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.dll
++System.Net.Http.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.http.dll
++System.Net.Http.Json.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.http.json.dll
++System.Net.HttpListener.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.httplistener.dll
++System.Net.Mail.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.mail.dll
++System.Net.NameResolution.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.nameresolution.dll
++System.Net.NetworkInformation.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.networkinformation.dll
++System.Net.Ping.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.ping.dll
++System.Net.Primitives.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.primitives.dll
++System.Net.Quic.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.quic.dll
++System.Net.Requests.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.requests.dll
++System.Net.Security.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.security.dll
++System.Net.ServicePoint.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.servicepoint.dll
++System.Net.Sockets.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.sockets.dll
++System.Net.WebClient.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.webclient.dll
++System.Net.WebHeaderCollection.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.webheadercollection.dll
++System.Net.WebProxy.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.webproxy.dll
++System.Net.WebSockets.Client.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.websockets.client.dll
++System.Net.WebSockets.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.net.websockets.dll
++System.Numerics.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.numerics.dll
++System.Numerics.Vectors.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.numerics.vectors.dll
++System.ObjectModel.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.objectmodel.dll
++System.Private.CoreLib.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.private.corelib.dll
++System.Private.DataContractSerialization.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.private.datacontractserialization.dll
++System.Private.Uri.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.private.uri.dll
++System.Private.Xml.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.private.xml.dll
++System.Private.Xml.Linq.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.private.xml.linq.dll
++System.Reflection.DispatchProxy.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.reflection.dispatchproxy.dll
++System.Reflection.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.reflection.dll
++System.Reflection.Emit.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.reflection.emit.dll
++System.Reflection.Emit.ILGeneration.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.reflection.emit.ilgeneration.dll
++System.Reflection.Emit.Lightweight.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.reflection.emit.lightweight.dll
++System.Reflection.Extensions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.reflection.extensions.dll
++System.Reflection.Metadata.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.reflection.metadata.dll
++System.Reflection.Primitives.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.reflection.primitives.dll
++System.Reflection.TypeExtensions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.reflection.typeextensions.dll
++System.Resources.Reader.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.resources.reader.dll
++System.Resources.ResourceManager.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.resources.resourcemanager.dll
++System.Resources.Writer.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.resources.writer.dll
++System.Runtime.CompilerServices.Unsafe.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.runtime.compilerservices.unsafe.dll
++System.Runtime.CompilerServices.VisualC.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.runtime.compilerservices.visualc.dll
++System.Runtime.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.runtime.dll
++System.Runtime.Extensions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.runtime.extensions.dll
++System.Runtime.Handles.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.runtime.handles.dll
++System.Runtime.InteropServices.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.runtime.interopservices.dll
++System.Runtime.InteropServices.RuntimeInformation.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.runtime.interopservices.runtimeinformation.dll
++System.Runtime.Intrinsics.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.runtime.intrinsics.dll
++System.Runtime.Loader.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.runtime.loader.dll
++System.Runtime.Numerics.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.runtime.numerics.dll
++System.Runtime.Serialization.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.runtime.serialization.dll
++System.Runtime.Serialization.Formatters.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.runtime.serialization.formatters.dll
++System.Runtime.Serialization.Json.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.runtime.serialization.json.dll
++System.Runtime.Serialization.Primitives.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.runtime.serialization.primitives.dll
++System.Runtime.Serialization.Xml.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.runtime.serialization.xml.dll
++System.Security.AccessControl.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.security.accesscontrol.dll
++System.Security.Claims.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.security.claims.dll
++System.Security.Cryptography.Algorithms.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.security.cryptography.algorithms.dll
++System.Security.Cryptography.Cng.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.security.cryptography.cng.dll
++System.Security.Cryptography.Csp.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.security.cryptography.csp.dll
++System.Security.Cryptography.Encoding.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.security.cryptography.encoding.dll
++System.Security.Cryptography.OpenSsl.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.security.cryptography.openssl.dll
++System.Security.Cryptography.Primitives.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.security.cryptography.primitives.dll
++System.Security.Cryptography.X509Certificates.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.security.cryptography.x509certificates.dll
++System.Security.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.security.dll
++System.Security.Principal.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.security.principal.dll
++System.Security.Principal.Windows.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.security.principal.windows.dll
++System.Security.SecureString.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.security.securestring.dll
++System.ServiceModel.Web.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.servicemodel.web.dll
++System.ServiceProcess.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.serviceprocess.dll
++System.Text.Encoding.CodePages.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.text.encoding.codepages.dll
++System.Text.Encoding.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.text.encoding.dll
++System.Text.Encoding.Extensions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.text.encoding.extensions.dll
++System.Text.Json.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.text.json.dll
++System.Text.RegularExpressions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.text.regularexpressions.dll
++System.Threading.Channels.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.threading.channels.dll
++System.Threading.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.threading.dll
++System.Threading.Overlapped.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.threading.overlapped.dll
++System.Threading.Tasks.Dataflow.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.threading.tasks.dataflow.dll
++System.Threading.Tasks.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.threading.tasks.dll
++System.Threading.Tasks.Extensions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.threading.tasks.extensions.dll
++System.Threading.Tasks.Parallel.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.threading.tasks.parallel.dll
++System.Threading.Thread.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.threading.thread.dll
++System.Threading.ThreadPool.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.threading.threadpool.dll
++System.Threading.Timer.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.threading.timer.dll
++System.Transactions.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.transactions.dll
++System.Transactions.Local.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.transactions.local.dll
++System.ValueTuple.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.valuetuple.dll
++System.Web.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.web.dll
++System.Web.HttpUtility.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.web.httputility.dll
++System.Windows.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.windows.dll
++System.Xml.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.xml.dll
++System.Xml.Linq.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.xml.linq.dll
++System.Xml.ReaderWriter.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.xml.readerwriter.dll
++System.Xml.Serialization.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.xml.serialization.dll
++System.Xml.XDocument.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.xml.xdocument.dll
++System.Xml.XmlDocument.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.xml.xmldocument.dll
++System.Xml.XmlSerializer.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.xml.xmlserializer.dll
++System.Xml.XPath.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.xml.xpath.dll
++System.Xml.XPath.XDocument.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\system.xml.xpath.xdocument.dll
++ucrtbase.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\ucrtbase.dll
++WindowsBase.dll
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\windowsbase.dll
++bundle
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\scopedcss\bundle\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\scopedcss\bundle\
++projectbundle
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\scopedcss\projectbundle\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\scopedcss\projectbundle\
++PublishOutputs.62e8fe971d.txt
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\publishoutputs.62e8fe971d.txt
++singlefilehost.exe
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\singlefilehost.exe
++staticwebassets.publish.json
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\staticwebassets.publish.json
++bootstrap.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap.css
++bootstrap.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap.css.map
++bootstrap.min.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap.min.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap.min.css
++bootstrap.min.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap.min.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap.min.css.map
++bootstrap.rtl.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.css
++bootstrap.rtl.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.css.map
++bootstrap.rtl.min.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.min.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.min.css
++bootstrap.rtl.min.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.min.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap.rtl.min.css.map
++bootstrap-grid.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.css
++bootstrap-grid.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.css.map
++bootstrap-grid.min.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.min.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.min.css
++bootstrap-grid.min.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.min.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.min.css.map
++bootstrap-grid.rtl.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.css
++bootstrap-grid.rtl.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.css.map
++bootstrap-grid.rtl.min.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css
++bootstrap-grid.rtl.min.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-grid.rtl.min.css.map
++bootstrap-reboot.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.css
++bootstrap-reboot.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.css.map
++bootstrap-reboot.min.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.min.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.min.css
++bootstrap-reboot.min.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.min.css.map
++bootstrap-reboot.rtl.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css
++bootstrap-reboot.rtl.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.css.map
++bootstrap-reboot.rtl.min.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css
++bootstrap-reboot.rtl.min.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-reboot.rtl.min.css.map
++bootstrap-utilities.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.css
++bootstrap-utilities.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.css.map
++bootstrap-utilities.min.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.min.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.min.css
++bootstrap-utilities.min.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.min.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.min.css.map
++bootstrap-utilities.rtl.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css
++bootstrap-utilities.rtl.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.css.map
++bootstrap-utilities.rtl.min.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css
++bootstrap-utilities.rtl.min.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\css\bootstrap-utilities.rtl.min.css.map
++bootstrap.bundle.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.js
++bootstrap.bundle.js.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.js.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.js.map
++bootstrap.bundle.min.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js
++bootstrap.bundle.min.js.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js.map
++bootstrap.esm.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.js
++bootstrap.esm.js.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.js.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.js.map
++bootstrap.esm.min.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.min.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.min.js
++bootstrap.esm.min.js.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.min.js.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.esm.min.js.map
++bootstrap.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\js\bootstrap.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.js
++bootstrap.js.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\js\bootstrap.js.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.js.map
++bootstrap.min.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\js\bootstrap.min.js
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.min.js
++bootstrap.min.js.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\wwwroot\lib\bootstrap\dist\js\bootstrap.min.js.map
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\lib\bootstrap\dist\js\bootstrap.min.js.map
++native
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\win-arm64\native\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\win-x64\native\
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\debug\net6.0\runtimes\win-x86\native\
++web.config
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\web.config
++HRMSWebApp.styles.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\scopedcss\bundle\hrmswebapp.styles.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\bin\release\net6.0\win-x64\publish\wwwroot\hrmswebapp.styles.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\scopedcss\bundle\hrmswebapp.styles.css
++HRMSWebApp.bundle.scp.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\scopedcss\projectbundle\hrmswebapp.bundle.scp.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\scopedcss\projectbundle\hrmswebapp.bundle.scp.css
++_Layout.cshtml.rz.scp.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\debug\net6.0\scopedcss\views\shared\_layout.cshtml.rz.scp.css
i:{************************************}:c:\users\<USER>\source\repos\hrmsdb\hrmswebapp\obj\release\net6.0\win-x64\scopedcss\views\shared\_layout.cshtml.rz.scp.css
++Microsoft.AspNetCore.Http.Abstractions (2.3.0)
