using System;
using System.Windows.Forms;
using System.Drawing;
using System.Reflection.Emit;
using Label = System.Windows.Forms.Label;
using System.IO;
using System.Drawing.Drawing2D;
using System.Data.SqlClient;


namespace EmployeeManagementSystem
{
    public partial class HomeForm : Form
    {
        private readonly string userFullName;
        private readonly string userType;
        private readonly List<string> educationalTips = new List<string>();
        private int currentTipIndex;
        private Image defaultLogo; // لحفظ الصورة الافتراضية

        public HomeForm(string userType, string username)  // جعل username إلزامي
        {
            InitializeComponent();
            InitializeRoundedPanels();
            this.userType = userType;
            
            // حفظ الصورة الافتراضية
            defaultLogo = Properties.Resources.photo;
            
            // التأكد من تهيئة connection string
            ConStringHelper.SetConString();
          
            // إعداد المخطط مباشرة بعد InitializeComponent
            InitializeDiagram();
            ShowDatabaseSize();
            LoadData();
            timer1.Tick += new EventHandler(ShowEducationalTip);
            timer1.Start();
            // إعداد المؤقت لتحديث الوقت
            timer.Interval = 1000; // تحديث كل ثانية
            timer.Tick += Timer_Tick;
            timer.Start();
            
            // إعداد مؤقت لتحديث حجم قاعدة البيانات كل 30 ثانية
            System.Windows.Forms.Timer dbSizeTimer = new System.Windows.Forms.Timer();
            dbSizeTimer.Interval = 30000; // تحديث كل 30 ثانية
            dbSizeTimer.Tick += (s, e) => 
            {
                ShowDatabaseSize();
                // إيقاف Timer إذا لم تكن قاعدة البيانات موجودة لتوفير الموارد
                if (lbl_DBSize.Text.Contains("غير موجودة") || lbl_DBSize.Text.Contains("غير متاحة"))
                {
                    dbSizeTimer.Stop();
                    System.Diagnostics.Debug.WriteLine("تم إيقاف Timer حجم قاعدة البيانات - قاعدة البيانات غير متاحة");
                }
            };
            dbSizeTimer.Start();
            
            // إضافة حدث النقر على lbl_DBSize لإظهار التفاصيل
            lbl_DBSize.Click += ShowDatabaseDetails;

            // تحميل اسم المستخدم الكامل
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                var cmd = connection.CreateCommand();
                cmd.CommandText = "SELECT FullName FROM Users WHERE Username = @Username";
                cmd.Parameters.AddWithValue("@Username", username);
                userFullName = cmd.ExecuteScalar()?.ToString() ?? username;
            }

            UpdateDateTime();
        }
        private void ShowDatabaseSize()
        {
            try
            {
                string connectionString = ConStringHelper.GetConnectionString();
                if (string.IsNullOrEmpty(connectionString))
                {
                    lbl_DBSize.Text = "لم يتم تكوين الاتصال";
                    lbl_DBSize.ForeColor = Color.Red;
                    return;
                }
                
                using (SqlConnection con = new SqlConnection(connectionString))
                {
                    // التحقق من وجود قاعدة البيانات أولاً
                    if (!IsDatabaseExists(con))
                    {
                        lbl_DBSize.Text = "قاعدة البيانات غير موجودة";
                        lbl_DBSize.ForeColor = Color.Orange;
                        return;
                    }

                    con.Open();

                    // التحقق من وجود الجداول
                    if (!HasTables(con))
                    {
                        lbl_DBSize.Text = "قاعدة البيانات فارغة";
                        lbl_DBSize.ForeColor = Color.LightBlue;
                        
                        ToolTip dbSizeTooltip = new ToolTip();
                        dbSizeTooltip.SetToolTip(lbl_DBSize, "قاعدة البيانات موجودة ولكن لا تحتوي على جداول بعد");
                        return;
                    }

                    // استعلام أكثر دقة لحساب حجم قاعدة البيانات
                    string query = @"
                    SELECT 
                        CAST(
                            (
                                SELECT SUM(size) * 8.0 / 1024
                                FROM sys.master_files 
                                WHERE database_id = DB_ID()
                            ) AS DECIMAL(10,2)
                        ) AS TotalSizeMB,
                        CAST(
                            (
                                SELECT SUM(FILEPROPERTY(name, 'SpaceUsed')) * 8.0 / 1024
                                FROM sys.database_files
                                WHERE type_desc = 'ROWS'
                            ) AS DECIMAL(10,2)
                        ) AS UsedSizeMB";

                    SqlCommand cmd = new SqlCommand(query, con);
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read() && !reader.IsDBNull(0))
                        {
                            decimal totalSize = reader.GetDecimal(0);
                            decimal usedSize = reader.IsDBNull(1) ? 0 : reader.GetDecimal(1);
                            decimal freeSpace = totalSize - usedSize;
                            
                            // عرض الحجم الإجمالي والمستخدم
                            lbl_DBSize.Text = $"{totalSize:F1} MB";
                            lbl_DBSize.ForeColor = Color.White; // إعادة اللون الأصلي
                            
                            // إضافة Tooltip لإظهار التفاصيل
                            ToolTip dbSizeTooltip = new ToolTip();
                            dbSizeTooltip.SetToolTip(lbl_DBSize, 
                                $"الحجم الإجمالي: {totalSize:F1} MB\n" +
                                $"المستخدم: {usedSize:F1} MB\n" +
                                $"المساحة الفارغة: {freeSpace:F1} MB");
                        }
                        else
                        {
                            lbl_DBSize.Text = "0.0 MB";
                            lbl_DBSize.ForeColor = Color.LightGray;
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                // معالجة أخطاء SQL المحددة
                switch (sqlEx.Number)
                {
                    case 2: // Cannot open database
                        lbl_DBSize.Text = "قاعدة البيانات غير متاحة";
                        lbl_DBSize.ForeColor = Color.Orange;
                        break;
                    case 18456: // Login failed
                        lbl_DBSize.Text = "خطأ في المصادقة";
                        lbl_DBSize.ForeColor = Color.Red;
                        break;
                    case 53: // Network path not found
                        lbl_DBSize.Text = "لا يوجد اتصال بالخادم";
                        lbl_DBSize.ForeColor = Color.Red;
                        break;
                    default:
                        lbl_DBSize.Text = "خطأ في قاعدة البيانات";
                        lbl_DBSize.ForeColor = Color.Red;
                        break;
                }
                
                // إضافة Tooltip للخطأ
                ToolTip errorTooltip = new ToolTip();
                errorTooltip.SetToolTip(lbl_DBSize, $"تفاصيل الخطأ: {sqlEx.Message}");
                
                System.Diagnostics.Debug.WriteLine($"خطأ SQL في حساب حجم قاعدة البيانات: {sqlEx.Message}");
            }
            catch (Exception ex)
            {
                lbl_DBSize.Text = "خطأ غير متوقع";
                lbl_DBSize.ForeColor = Color.Red;
                System.Diagnostics.Debug.WriteLine($"خطأ عام في حساب حجم قاعدة البيانات: {ex.Message}");
            }
        }

        private bool IsDatabaseExists(SqlConnection connection)
        {
            try
            {
                // تغيير الاتصال إلى master للتحقق من وجود قاعدة البيانات
                string masterConnectionString = ConStringHelper.GetConnectionString().Replace("Database=HRMSDB", "Database=master");
                using (SqlConnection masterCon = new SqlConnection(masterConnectionString))
                {
                    masterCon.Open();
                    SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM sys.databases WHERE name = 'HRMSDB'", masterCon);
                    int count = (int)cmd.ExecuteScalar();
                    return count > 0;
                }
            }
            catch
            {
                return false;
            }
        }

        private bool HasTables(SqlConnection connection)
        {
            try
            {
                SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'", connection);
                int tableCount = (int)cmd.ExecuteScalar();
                return tableCount > 0;
            }
            catch
            {
                return false;
            }
        }

        private void ShowDatabaseDetails(object? sender, EventArgs e)
        {
            try
            {
                using (SqlConnection con = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    // التحقق من وجود قاعدة البيانات أولاً
                    if (!IsDatabaseExists(con))
                    {
                        MessageBox.Show("قاعدة البيانات غير موجودة.\nيرجى إنشاء قاعدة البيانات أولاً.", 
                            "قاعدة البيانات غير موجودة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    con.Open();

                    // التحقق من وجود الجداول
                    if (!HasTables(con))
                    {
                        MessageBox.Show("قاعدة البيانات موجودة ولكنها فارغة.\nلا توجد جداول بعد.", 
                            "قاعدة البيانات فارغة", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    // استعلام تفصيلي لحجم قاعدة البيانات
                    string query = @"
                    SELECT 
                        name AS FileName,
                        type_desc AS FileType,
                        CAST(size * 8.0 / 1024 AS DECIMAL(10,2)) AS FileSizeMB,
                        CAST(FILEPROPERTY(name, 'SpaceUsed') * 8.0 / 1024 AS DECIMAL(10,2)) AS UsedSpaceMB,
                        CAST((size - FILEPROPERTY(name, 'SpaceUsed')) * 8.0 / 1024 AS DECIMAL(10,2)) AS FreeSpaceMB
                    FROM sys.database_files
                    ORDER BY type_desc, name";

                    SqlCommand cmd = new SqlCommand(query, con);
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        string details = "تفاصيل قاعدة البيانات:\n\n";
                        bool hasData = false;
                        
                        while (reader.Read())
                        {
                            hasData = true;
                            string fileName = reader.GetString(0);
                            string fileType = reader.GetString(1);
                            decimal fileSize = reader.GetDecimal(2);
                            decimal usedSpace = reader.GetDecimal(3);
                            decimal freeSpace = reader.GetDecimal(4);
                            
                            details += $"الملف: {fileName}\n";
                            details += $"النوع: {fileType}\n";
                            details += $"الحجم: {fileSize:F1} MB\n";
                            details += $"المستخدم: {usedSpace:F1} MB\n";
                            details += $"الفارغ: {freeSpace:F1} MB\n\n";
                        }
                        
                        if (hasData)
                        {
                            MessageBox.Show(details, "تفاصيل قاعدة البيانات", 
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("لا يمكن الحصول على تفاصيل قاعدة البيانات.", 
                                "لا توجد بيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                }
            }
            catch (SqlException sqlEx)
            {
                string errorMessage = sqlEx.Number switch
                {
                    2 => "لا يمكن الوصول إلى قاعدة البيانات.\nقد تكون غير موجودة أو غير متاحة.",
                    18456 => "خطأ في المصادقة.\nتحقق من إعدادات الاتصال.",
                    53 => "لا يمكن الاتصال بخادم قاعدة البيانات.\nتأكد من تشغيل SQL Server.",
                    _ => $"خطأ في قاعدة البيانات:\n{sqlEx.Message}"
                };
                
                MessageBox.Show(errorMessage, "خطأ في قاعدة البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ غير متوقع:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        private void ShowEducationalTip(object? sender, EventArgs e)
        {
            if (educationalTips.Count == 0) return;

            lab_el1.Text = educationalTips[currentTipIndex];
            currentTipIndex = (currentTipIndex + 1) % educationalTips.Count;
        }
        private void Timer_Tick(object? sender, EventArgs e)
        {
            UpdateDateTime();
        }

        private void UpdateDateTime()
        {
            var now = DateTime.Now;
            string greeting = now.Hour >= 12 ? "مساء الخير" : "صباح الخير";
            string userTitle = userType == "مدير" ? "مدير" : "موظف";
            lblUserGreeting.Text = $"{greeting}، {userTitle} {userFullName}";

            lblDateTime.Text = DateTime.Now.ToString("dddd: yyyy/MM/dd" + " " + "hh:mm:ss tt",
                new System.Globalization.CultureInfo("ar-IQ"));

        }

        private void LoadData()
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();

                // عدد الموظفين
                var cmdEmployees = connection.CreateCommand();
                cmdEmployees.CommandText = "SELECT COUNT(*) FROM Employees";
                lblEmployeesCount.Text = $" {cmdEmployees.ExecuteScalar()}";

                // عدد المستخدمين
                var cmdUsers = connection.CreateCommand();
                cmdUsers.CommandText = "SELECT COUNT(*) FROM Users";
                lblUsersCount.Text = $" {cmdUsers.ExecuteScalar()}";

                // عدد الإجازات
                var cmdVacations = connection.CreateCommand();
                cmdVacations.CommandText = "SELECT COUNT(*) FROM Vacations";
                lblVacationsCount.Text = $" {cmdVacations.ExecuteScalar()}";

                // عدد الدورات
                var cmdCourses = connection.CreateCommand();
                cmdCourses.CommandText = "SELECT COUNT(*) FROM Courses";
                lblCoursesCount.Text = $" {cmdCourses.ExecuteScalar()}";

                // تحميل اسم المؤسسة والشعار
                var cmdSettings = connection.CreateCommand();
                cmdSettings.CommandText = "SELECT TOP 1 CompanyName, CompanyLogo FROM Settings";
                using var reader = cmdSettings.ExecuteReader();
                if (reader.Read())
                {
                    lblCompanyName.Text = reader["CompanyName"]?.ToString();

                    string? logoPath = reader["CompanyLogo"]?.ToString();
                    if (!string.IsNullOrEmpty(logoPath) && File.Exists(logoPath))
                    {
                        try
                        {
                            using var stream = new FileStream(logoPath, FileMode.Open, FileAccess.Read);
                            pictureBoxLogo.Image = Image.FromStream(stream);
                        }
                        catch
                        {
                            // إذا فشل تحميل الصورة، نستخدم الصورة الافتراضية
                            pictureBoxLogo.Image = defaultLogo;
                        }
                    }
                    else
                    {
                        // إذا لم تكن هناك صورة محفوظة، نستخدم الصورة الافتراضية
                        pictureBoxLogo.Image = defaultLogo;
                    }

                }
                else
                {
                    // إذا لم توجد إعدادات محفوظة، نستخدم الصورة الافتراضية
                    pictureBoxLogo.Image = defaultLogo;
                }
                }
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، نستخدم الصورة الافتراضية
                pictureBoxLogo.Image = defaultLogo;
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات: {ex.Message}");
            }
        }


        private void InitializeDiagram()
        {
            // إعداد تخطيط الجدول
            diagramTableLayout.ColumnStyles.Clear();
            diagramTableLayout.RowStyles.Clear();

            for (int i = 0; i < 3; i++)
            {
                diagramTableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33F));
            }
            for (int i = 0; i < 2; i++)
            {
                diagramTableLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            }

            // إضافة عنوان المخطط
            Label lblDiagramTitle = new()
            {
                Text = "مخطط نظام إدارة الموظفين",
                Font = new Font("Cairo", 16, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 40,
                ForeColor = Color.FromArgb(45, 66, 91)
            };
            diagramPanel.Controls.Add(lblDiagramTitle);

            CreateSystemDiagram();
        }
        private void CreateSystemDiagram()
        {
            // تعريف وحدات النظام مع وصفها وأيقوناتها
            var modules = new[]
            {
              new { Title = "إدارة الموظفين", Description = "إدارة بيانات وملفات الموظفين", IconResource = Properties.Resources.user_groups_32px, Color = Color.FromArgb(52, 152, 219) },
              new { Title = "إدارة المستخدمين", Description = "إدارة صلاحيات وحسابات المستخدمين", IconResource = Properties.Resources.management_32px, Color = Color.FromArgb(41, 128, 185) },
              new { Title = "إدارة الإجازات", Description = "إدارة طلبات وسجلات الإجازات", IconResource = Properties.Resources.traveler_32px, Color = Color.FromArgb(66, 194, 123) },
              new { Title = "إدارة الوثائق", Description = "إدارة وأرشفة وثائق الموظفين", IconResource = Properties.Resources.documents_32px, Color = Color.FromArgb(54, 162, 162) },
              new { Title = "إدارة الدورات", Description = "إدارة بيانات الدورات للموظفين", IconResource = Properties.Resources.classroom_32px, Color = Color.FromArgb(230, 126, 34) },
              new { Title = "إعدادات النظام", Description = "تخصيص واجهة وإعدادات النظام", IconResource = Properties.Resources.settings_32px, Color = Color.FromArgb(92, 113, 134) }
            };

            int moduleIndex = 0;
            for (int row = 0; row < 2 && moduleIndex < modules.Length; row++)
            {
                for (int col = 0; col < 3 && moduleIndex < modules.Length; col++)
                {
                    var module = modules[moduleIndex];
                    var modulePanel = new Panel
                    {
                        BackColor = module.Color,
                        Margin = new Padding(8),
                        Padding = new Padding(10),
                        Dock = DockStyle.Fill
                    };

                    modulePanel.Paint += (s, e) =>
                    {
                        if (s is not Panel panel) return;
                        using var path = new GraphicsPath();
                        int radius = 15;
                        path.AddArc(0, 0, radius * 2, radius * 2, 180, 90);
                        path.AddArc(panel.Width - radius * 2, 0, radius * 2, radius * 2, 270, 90);
                        path.AddArc(panel.Width - radius * 2, panel.Height - radius * 2, radius * 2, radius * 2, 0, 90);
                        path.AddArc(0, panel.Height - radius * 2, radius * 2, radius * 2, 90, 90);
                        path.CloseFigure();

                        panel.Region = new Region(path);

                        e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
                        using var shadowBrush = new SolidBrush(Color.FromArgb(30, 0, 0, 0));
                        e.Graphics.FillPath(shadowBrush, path);
                    };

                    var iconPictureBox = new PictureBox
                    {
                        Image = module.IconResource,
                        SizeMode = PictureBoxSizeMode.Zoom,
                        Size = new Size(32, 32),
                        Dock = DockStyle.Top,
                        BackColor = Color.Transparent,
                        Margin = new Padding(0, 5, 0, 5)
                    };

                    var titleLabel = new Label
                    {
                        Text = module.Title,
                        ForeColor = Color.White,
                        Font = new Font("Cairo", 11, FontStyle.Bold),
                        TextAlign = ContentAlignment.MiddleCenter,
                        Dock = DockStyle.Top,
                        Height = 30,
                        BackColor = Color.Transparent
                    };

                    var descLabel = new Label
                    {
                        Text = module.Description,
                        ForeColor = Color.White,
                        Font = new Font("Cairo", 9),
                        TextAlign = ContentAlignment.TopCenter,
                        Dock = DockStyle.Fill,
                        AutoSize = false,
                        BackColor = Color.Transparent
                    };

                    modulePanel.MouseEnter += (s, e) =>
                    {
                        if (s is not Panel panel) return;
                        panel.BackColor = Color.FromArgb(
                            (int)(module.Color.R * 0.9),
                            (int)(module.Color.G * 0.9),
                            (int)(module.Color.B * 0.9)
                        );
                        Cursor = Cursors.Hand;
                    };

                    modulePanel.MouseLeave += (s, e) =>
                    {
                        if (s is not Panel panel) return;
                        panel.BackColor = module.Color;
                        Cursor = Cursors.Default;
                    };

                    modulePanel.Controls.Add(descLabel);
                    modulePanel.Controls.Add(titleLabel);
                    modulePanel.Controls.Add(iconPictureBox);
                    diagramTableLayout.Controls.Add(modulePanel, col, row);

                    moduleIndex++;
                }
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);
            timer?.Stop();
            timer?.Dispose();
        }

        private void HomeForm_Load(object sender, EventArgs e)
        {
            educationalTips.Add("لا تنس أن تقوم بأخذ نسخة احتياطية للبيانات بين الحين والآخر.");
            educationalTips.Add("لإجراء النسخ الاحتياطي، انتقل إلى الإعدادات ثم إلى قسم النسخ الاحتياطي والتعيين.");
            educationalTips.Add("يمكنك تحقيق نتائج أفضل من خلال البحث .");
            educationalTips.Add("في حال حدوث خطأ لك في إضافة الملفات، لا تقلق؛ البرنامج يتيح لك تعديل ما قمت بإضافته.");
            educationalTips.Add("لا تنس أنه يمكنك حذف ملف من الملفات المضمنة من خلال النقر على زر ارفاق الوثائق وعرضها .");
            educationalTips.Add(" JPG.البرنامج يدعم إضافة ملفات الصور بصيغة");
            educationalTips.Add("PDF.البرنامج يدعم اضافة ملفات بصيغة");
            educationalTips.Add("هناك اثنين من الصلاحيات متاحة وهي: المدير والمستخدم .");
            educationalTips.Add("صلاحية المدير تتمثل في الوصول إلى كافة نقاط النظام.");
            educationalTips.Add("صلاحيات المستخدم تتمثل في القراءة والكتابة على بيانات الموظفين والاجازات والدورات التدريبية.");
            educationalTips.Add("يمكنك إعطاء عدد المستندات في خانة إضافة ملف حتى يتمكن الماسح من إضافتها دفعة واحدة.");
            educationalTips.Add("إذا كان لديك أكثر من ماسح يمكنك اختيار أحدها من خلال القائمة المنسدلة.");
            educationalTips.Add("نحن نستخدم أفضل التكنولوجيا للحصول على صورة ممسوحة وواضحة.");
            educationalTips.Add("يمكنك طباعة كافة شبكة عرض البيانات من خلال زر الطباعة وهذا يمكنك الحصول على تقرير في كافة العمل المضاف.");
            educationalTips.Add("نحن نقدر ملاحظاتك حول عمل النظام. يمكنك تسجيلها وإرسالها إلى المطور.");
            educationalTips.Add("إذا كان لديك أي خاصية جديدة تود إضافتها ضمن البرنامج يمكنك إرسالها إلى المطور. سنكون سعداء لتلقي مقترحاتك.");
            educationalTips.Add("كن على تواصل كامل مع حجم قاعدة البيانات المضافة من خلال خانة حجم البيانات في الصفحة الرئيسية.");
            educationalTips.Add("بياناتك مؤمنة؛ نحن نحتفظ بها في مكان آمن.");
            educationalTips.Add("نحن نحمي بياناتك واتصالك من خلال تشفير نص الاتصال وعدم السماح بأخذ نسخة احتياطية إلا من قبل مالك النظام نفسه.");
            educationalTips.Add("لا تنس أن إجراءات النسخ الاحتياطي ستكون متاحة فقط لمالك قاعدة البيانات،والذي يمتلك صلاحيات المدير");
        }          
        private void InitializeRoundedPanels()
        {
            Panel[] panels = { panel2, panel3, panel4, panel5, panel6 };

            foreach (var panel in panels)
            {
                if (panel == null) continue;

                // تعيين خصائص البانل الأساسية
                panel.BackColor = Color.White;
                panel.Margin = new Padding(20);
                panel.BorderStyle = BorderStyle.None;
                panel.Padding = new Padding(7);


                panel.Paint += (s, e) =>
                {
                    if (s is not Panel currentPanel) return;

                    e.Graphics.SmoothingMode = SmoothingMode.AntiAlias;
                    
                    // رسم الظل
                    using (var shadowPath = new GraphicsPath())
                    {
                        int radius = 20;
                        int shadowOffset = 7;
                        
                        shadowPath.AddArc(shadowOffset, shadowOffset, radius * 2, radius * 2, 180, 90);
                        shadowPath.AddArc(currentPanel.Width - (radius * 2 + shadowOffset), shadowOffset, radius * 2, radius * 2, 270, 90);
                        shadowPath.AddArc(currentPanel.Width - (radius * 2 + shadowOffset), currentPanel.Height - (radius * 2 + shadowOffset), radius * 2, radius * 2, 0, 90);
                        shadowPath.AddArc(shadowOffset, currentPanel.Height - (radius * 2 + shadowOffset), radius * 2, radius * 2, 90, 90);
                        shadowPath.CloseFigure();

                        using var shadowBrush = new SolidBrush(Color.FromArgb(100, 64, 64, 64));
                        e.Graphics.FillPath(shadowBrush, shadowPath);
                        using var darkShadowBrush = new SolidBrush(Color.FromArgb(40, 0, 0, 0));
                        e.Graphics.FillPath(darkShadowBrush, shadowPath);
                    }

                    // رسم البانل الأساسي
                    using (var path = new GraphicsPath())
                    {
                        int radius = 15;
                        path.AddArc(0, 0, radius * 2, radius * 2, 180, 90);
                        path.AddArc(currentPanel.Width - radius * 2, 0, radius * 2, radius * 2, 270, 90);
                        path.AddArc(currentPanel.Width - radius * 2, currentPanel.Height - radius * 2, radius * 2, radius * 2, 0, 90);
                        path.AddArc(0, currentPanel.Height - radius * 2, radius * 2, radius * 2, 90, 90);
                        path.CloseFigure();

                        //currentPanel.Region = new Region(path);

                        using (var gradientBrush = new LinearGradientBrush(
                            currentPanel.ClientRectangle,
                            currentPanel.BackColor,
                            Color.FromArgb(
                                Math.Max(0, currentPanel.BackColor.R - 40),
                                Math.Max(0, currentPanel.BackColor.G - 40),
                                Math.Max(0, currentPanel.BackColor.B - 40)
                            ),
                            LinearGradientMode.ForwardDiagonal))
                        {
                            e.Graphics.FillPath(gradientBrush, path);
                        }

                        using var pen = new Pen(Color.FromArgb(200, 200, 200), 1);
                        e.Graphics.DrawPath(pen, path);
                    }
                };

                // تحديث عند تغيير الحجم
                panel.Resize += (s, e) => panel.Invalidate();
                
                // تحديث مبدئي
                panel.Invalidate();
            }
        }
    }
}