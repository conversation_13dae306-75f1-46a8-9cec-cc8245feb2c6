using System;
using System.Data;
using System.Data.SqlClient;

namespace EmployeeManagementSystem
{
    public partial class LoginForm : Form
    {
        public LoginForm()
        {
            InitializeComponent();
            this.labelCopyright.Text = "جميع الحقوق محفوظة  © 2024-" + DateTime.Now.Year.ToString();
            txtPassword.PasswordChar = '*';
            this.AcceptButton = btnLogin;
            this.CancelButton = btnCancel;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterScreen;

            // تطبيق الثيم المحفوظ
            string theme = "default";
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT TOP 1 Theme FROM Settings";
                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            theme = reader["Theme"]?.ToString() ?? "default";
                        }
                    }
                }
            }
            catch
            {
                theme = "default";
            }

            ThemeManager.ApplyThemeToForm(this);



        }

        private void AddRememberMeColumns()
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    // فحص وجود الأعمدة عبر INFORMATION_SCHEMA
                    string checkColumnSql = @"SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Settings' AND COLUMN_NAME = 'RememberUsername'";
                    using (var checkCommand = new SqlCommand(checkColumnSql, connection))
                    {
                        int hasRememberUsername = (int)checkCommand.ExecuteScalar();
                        if (hasRememberUsername == 0)
                        {
                            string alterSql = "ALTER TABLE Settings ADD RememberUsername NVARCHAR(100) NULL;";
                            using (var alterCommand = new SqlCommand(alterSql, connection))
                            {
                                alterCommand.ExecuteNonQuery();
                            }
                        }
                    }
                    checkColumnSql = @"SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Settings' AND COLUMN_NAME = 'RememberPassword'";
                    using (var checkCommand = new SqlCommand(checkColumnSql, connection))
                    {
                        int hasRememberPassword = (int)checkCommand.ExecuteScalar();
                        if (hasRememberPassword == 0)
                        {
                            string alterSql = "ALTER TABLE Settings ADD RememberPassword NVARCHAR(100) NULL;";
                            using (var alterCommand = new SqlCommand(alterSql, connection))
                            {
                                alterCommand.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في تهيئة إعدادات حفظ بيانات الدخول: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void LoadSavedCredentials()
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = @"SELECT RememberUsername, RememberPassword FROM Settings 
                                 WHERE RememberUsername IS NOT NULL 
                                 AND RememberUsername != '' 
                                 AND RememberPassword IS NOT NULL 
                                 AND RememberPassword != ''";

                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            var savedUsername = reader["RememberUsername"]?.ToString();
                            var savedPassword = reader["RememberPassword"]?.ToString();

                            if (!string.IsNullOrEmpty(savedUsername) && !string.IsNullOrEmpty(savedPassword))
                            {
                                txtUsername.Text = savedUsername;
                                txtPassword.Text = savedPassword;
                                chkRememberMe.Checked = true;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في استرجاع البيانات المحفوظة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void SaveCredentials(string username, string password)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // أولاً، تأكد من وجود سجل في جدول Settings
                            string checkSql = "SELECT COUNT(*) FROM Settings";
                            using (var checkCommand = new SqlCommand(checkSql, connection))
                            {
                                checkCommand.Transaction = transaction;
                                int count = Convert.ToInt32(checkCommand.ExecuteScalar());

                                if (count == 0)
                                {
                                    // إذا لم يكن هناك سجل، قم بإنشاء واحد
                                    string insertSql = @"INSERT INTO Settings (CompanyName, Theme, Language, LastModified, RememberUsername, RememberPassword) 
                                                       VALUES ('اسم المؤسسة', 'Default', 'ar', GETDATE(), @Username, @Password)";
                                    using (var insertCommand = new SqlCommand(insertSql, connection))
                                    {
                                        insertCommand.Transaction = transaction;
                                        insertCommand.Parameters.AddWithValue("@Username", username ?? string.Empty);
                                        insertCommand.Parameters.AddWithValue("@Password", password ?? string.Empty);
                                        insertCommand.ExecuteNonQuery();
                                    }
                                }
                                else
                                {
                                    // إذا كان هناك سجل، قم بتحديثه
                                    string updateSql = @"UPDATE Settings SET 
                                                    RememberUsername = @Username,
                                                    RememberPassword = @Password";
                                    using (var updateCommand = new SqlCommand(updateSql, connection))
                                    {
                                        updateCommand.Transaction = transaction;
                                        updateCommand.Parameters.AddWithValue("@Username", username ?? string.Empty);
                                        updateCommand.Parameters.AddWithValue("@Password", password ?? string.Empty);
                                        updateCommand.ExecuteNonQuery();
                                    }
                                }
                            }

                            transaction.Commit();
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في حفظ البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearSavedCredentials()
        {
            SaveCredentials(string.Empty, string.Empty);
        }

        private async void btnLogin_Click(object sender, EventArgs e)
        {
            string username = txtUsername.Text;
            string password = txtPassword.Text;
            picloading.Visible = true;
            await Task.Delay(100); // يسمح بتحديث الواجهة قبل البدء
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT UserId, Username, FullName, UserType, DepartmentId FROM Users WHERE Username = @Username AND Password = @Password";
                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@Username", username);
                        command.Parameters.AddWithValue("@Password", password);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                // إنشاء كائن المستخدم
                                var user = new User
                                {
                                    UserId = reader.GetInt32("UserId"),
                                    Username = reader.GetString("Username"),
                                    FullName = reader.IsDBNull("FullName") ? null : reader.GetString("FullName"),
                                    UserType = reader.GetString("UserType"),
                                    DepartmentId = reader.IsDBNull("DepartmentId") ? null : reader.GetInt32("DepartmentId")
                                };

                                reader.Close();

                                // تسجيل عملية تسجيل الدخول الناجحة
                                await ActivityLogService.LogLoginAsync(user, true);

                                // حفظ أو حذف بيانات تسجيل الدخول حسب حالة مربع "تذكرني"
                                if (chkRememberMe.Checked)
                                {
                                    SaveCredentials(username, password);
                                }
                                else
                                {
                                    ClearSavedCredentials();
                                }

                                // تعيين المستخدم الحالي في خدمة تسجيل الأنشطة
                                ActivityLogService.SetCurrentUser(user);

                                var mainForm = new MainForm(user.UserType);
                                mainForm.CurrentUsername = username;
                                mainForm.CurrentUser = user; // تمرير بيانات المستخدم
                                Hide();
                                mainForm.Show();
                                DialogResult = DialogResult.OK;
                            }
                            else
                            {
                                // تسجيل محاولة تسجيل دخول فاشلة
                                var tempUser = new User
                                {
                                    UserId = 0,
                                    Username = username,
                                    FullName = "مستخدم غير معروف",
                                    UserType = "غير محدد"
                                };
                                await ActivityLogService.LogLoginAsync(tempUser, false, "اسم المستخدم أو كلمة المرور غير صحيحة");

                                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ",
                                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // تسجيل خطأ تسجيل الدخول
                var tempUser = new User
                {
                    UserId = 0,
                    Username = username,
                    FullName = "مستخدم غير معروف",
                    UserType = "غير محدد"
                };
                await ActivityLogService.LogLoginAsync(tempUser, false, ex.Message);

                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                picloading.Visible = false;
            }
        }


        private void btnCancel_Click(object sender, EventArgs e)
        {
            Application.Exit();
            //DialogResult = DialogResult.Cancel;
            //Close();
        }

        private void LoginForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (DialogResult != DialogResult.OK)
            {
                DialogResult = DialogResult.Cancel;
            }
            Application.Exit();
        }
        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);

            // تحقق مما إذا كان هناك بيانات محفوظة
            LoadSavedCredentials();

            // إذا لم يتم تعبئة البيانات من LoadSavedCredentials، قم بمسح الحقول
            if (string.IsNullOrEmpty(txtUsername.Text))
            {
                txtUsername.Clear();
                txtPassword.Clear();
            }

            txtUsername.Focus();
        }

        private void LoginForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            if (DialogResult != DialogResult.OK)
            {
                DialogResult = DialogResult.Cancel;
                Application.Exit();
            }
        }
    }
}