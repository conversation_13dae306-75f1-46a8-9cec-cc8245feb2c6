using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using ScottPlot;
using System.Linq;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace EmployeeManagementSystem
{
    public partial class DashboardForm : Form
    {
        private readonly System.Windows.Forms.Timer refreshTimer;
        private string currentTheme = "light"; // Default theme

        public DashboardForm()
        {
            InitializeComponent();
            refreshTimer = new System.Windows.Forms.Timer();
            SetupDashboardControls();
            InitializeTimer();
            UpdateDashboard();
        }

        private void SetupDashboardControls()
        {
            // Set Tags for theme support
            foreach (Control control in Controls)
            {
                if (control is Panel && control.Name.Contains("Card"))
                {
                    control.Tag = "StatisticCard";
                }
                else if (control is Label && (control.Name.StartsWith("lbl") || control.Name.StartsWith("title")))
                {
                    control.Tag = "StatisticLabel";
                }
            }

            // Setup chart appearance
            if (vacationsPlot != null)
            {
                vacationsPlot.BackColor = ThemeManager.ChartBackgroundColor;
                vacationsPlot.ForeColor = ThemeManager.ChartForegroundColor;
            }
            if (coursesPlot != null)
            {
                coursesPlot.BackColor = ThemeManager.ChartBackgroundColor;
                coursesPlot.ForeColor = ThemeManager.ChartForegroundColor;
            }
        }

        private void InitializeTimer()
        {
            refreshTimer.Interval = 30000; // تحديث كل 30 ثانية
            refreshTimer.Tick += (s, e) => RefreshTimer_Tick(s!, e);
            refreshTimer.Start();
        }

        private void RefreshTimer_Tick(object sender, EventArgs e)
        {
            UpdateDashboard();
        }

      
        private void UpdateDashboard()
        {
            try
            {
                Console.WriteLine("بدء تحديث لوحة المعلومات...");
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    Console.WriteLine("تم فتح الاتصال بقاعدة البيانات");

                    Console.WriteLine("جاري تحديث إحصائيات الموظفين...");
                    UpdateEmployeeStats(connection);

                    Console.WriteLine("جاري تحديث إحصائيات الإجازات...");
                    UpdateVacationStats(connection);

                    Console.WriteLine("جاري تحديث إحصائيات الدورات...");
                    UpdateCourseStats(connection);

                    UpdateCharts();
                }
                Console.WriteLine("اكتمل تحديث لوحة المعلومات");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في تحديث لوحة المعلومات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                Console.WriteLine($"خطأ في تحديث لوحة المعلومات: {ex.Message}");
            }
        }

        private void UpdateEmployeeStats(SqlConnection connection)
        {
            try
            {
                // التحقق من وجود جدول Employees في SQL Server
                var checkTable = new SqlCommand(
                    "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Employees'", connection);
                var tableName = checkTable.ExecuteScalar()?.ToString();
                Console.WriteLine($"Employees table exists: {!string.IsNullOrEmpty(tableName)}");

                if (!string.IsNullOrEmpty(tableName))
                {
                    // طباعة أسماء الأعمدة للتصحيح إن أردت
                    var columnsCmd = new SqlCommand(
                        "SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees'", connection);
                    using var columnReader = columnsCmd.ExecuteReader();
                    while (columnReader.Read())
                    {
                        Console.WriteLine($"Column: {columnReader["COLUMN_NAME"]}, Type: {columnReader["DATA_TYPE"]}");
                    }
                    columnReader.Close();

                    // حساب عدد الموظفين النشطين (BadgeExpiryDate >= اليوم الحالي)
                    var command = new SqlCommand(@"
                SELECT COUNT(*) FROM Employees 
                WHERE BadgeExpiryDate >= CAST(GETDATE() AS DATE)", connection);
                    var result = command.ExecuteScalar();
                    Console.WriteLine($"Active employees count result: {result}");
                    lblActiveEmployees.Text = (result != DBNull.Value ? Convert.ToInt32(result) : 0).ToString();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in UpdateEmployeeStats: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                lblActiveEmployees.Text = "0";
            }
        }

        private void UpdateVacationStats(SqlConnection connection)
        {
            try
            {
                // التحقق من وجود جدول Vacations
                var checkTable = new SqlCommand(
                    "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Vacations'", connection);
                var tableName = checkTable.ExecuteScalar()?.ToString();
                Console.WriteLine($"Vacations table exists: {!string.IsNullOrEmpty(tableName)}");

                if (!string.IsNullOrEmpty(tableName))
                {
                    // طباعة الأعمدة إن أردت (اختياري)
                    var columnsCmd = new SqlCommand(
                        "SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Vacations'", connection);
                    using var columnReader = columnsCmd.ExecuteReader();
                    while (columnReader.Read())
                    {
                        Console.WriteLine($"Column: {columnReader["COLUMN_NAME"]}, Type: {columnReader["DATA_TYPE"]}");
                    }
                    columnReader.Close();

                    // عد الإجازات الحالية (التاريخ الحالي بين StartDate و EndDate)
                    var command = new SqlCommand(@"
                SELECT COUNT(*) FROM Vacations 
                WHERE CAST(StartDate AS DATE) <= CAST(GETDATE() AS DATE)
                  AND CAST(EndDate AS DATE) >= CAST(GETDATE() AS DATE)", connection);

                    var result = command.ExecuteScalar();
                    Console.WriteLine($"Current vacations count result: {result}");
                    lblCurrentVacations.Text = (result != DBNull.Value ? Convert.ToInt32(result) : 0).ToString();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in UpdateVacationStats: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                lblCurrentVacations.Text = "0";
            }
        }

        private void UpdateCourseStats(SqlConnection connection)
        {
            try
            {
                // التحقق من وجود جدول Courses
                var checkTable = new SqlCommand(
                    "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Courses'", connection);
                var tableName = checkTable.ExecuteScalar()?.ToString();
                Console.WriteLine($"Courses table exists: {!string.IsNullOrEmpty(tableName)}");

                if (!string.IsNullOrEmpty(tableName))
                {
                    // طباعة الأعمدة إن أردت (اختياري)
                    var columnsCmd = new SqlCommand(
                        "SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Courses'", connection);
                    using var columnReader = columnsCmd.ExecuteReader();
                    while (columnReader.Read())
                    {
                        Console.WriteLine($"Column: {columnReader["COLUMN_NAME"]}, Type: {columnReader["DATA_TYPE"]}");
                    }
                    columnReader.Close();

                    // عد الدورات الحالية
                    var command = new SqlCommand(@"
                SELECT COUNT(*) FROM Courses 
                WHERE CAST(StartDate AS DATE) <= CAST(GETDATE() AS DATE)
                  AND CAST(EndDate AS DATE) >= CAST(GETDATE() AS DATE)", connection);

                    var result = command.ExecuteScalar();
                    Console.WriteLine($"Current courses count result: {result}");
                    lblCurrentCourses.Text = (result != DBNull.Value ? Convert.ToInt32(result) : 0).ToString();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in UpdateCourseStats: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                lblCurrentCourses.Text = "0";
            }
        }

        private void UpdateCharts()
        {
            UpdateVacationsChart();
            UpdateCoursesChart();
        }

        private void UpdateVacationsChart()
        {
            var dt = DatabaseHelper.GetAllVacations();
            var types = new Dictionary<string, int>();
        
            foreach (DataRow row in dt.Rows)
            {
                string? type = row["VacationType"]?.ToString();
                if (!string.IsNullOrEmpty(type))
                {
                    if (!types.ContainsKey(type))
                        types[type] = 0;
                    types[type]++;
                }
            }

            if (types.Count == 0) return;

            double[] values = types.Values.Select(x => (double)x).ToArray();
            string[] labels = types.Keys.ToArray();            vacationsPlot.Plot.Clear();
            
            var pie = vacationsPlot.Plot.AddPie(values);
            string[] formattedLabels = new string[labels.Length];
            for (int i = 0; i < labels.Length; i++)
            {
                formattedLabels[i] = $"{labels[i]}: {values[i]} ({(values[i] / values.Sum() * 100):F1}%)";
            }
            pie.SliceLabels = formattedLabels;
            pie.ShowLabels = true;
            pie.ShowPercentages = false; // We're showing percentages in the labels already
            pie.Size = 0.80; // تصغير حجم الدائرة لإفساح المجال للتسميات

            // Customize label style
            pie.Explode = false;

            vacationsPlot.Plot.Legend(enable: true, location: ScottPlot.Alignment.LowerRight);
        
            // Apply theme colors
            var title = "توزيع الإجازات حسب النوع";
            vacationsPlot.Plot.Title(label: title, color: ThemeManager.ChartForegroundColor);
            vacationsPlot.Plot.Style(
                figureBackground: ThemeManager.ChartBackgroundColor,
                dataBackground: ThemeManager.ChartBackgroundColor);
            vacationsPlot.Plot.XAxis.TickLabelStyle(color: ThemeManager.ChartForegroundColor);
            vacationsPlot.Plot.YAxis.TickLabelStyle(color: ThemeManager.ChartForegroundColor);
            
            vacationsPlot.Refresh();
        }

        private void UpdateCoursesChart()
        {
            var courses = DatabaseHelper.LoadCourses();
            var monthly = new Dictionary<DateTime, int>();
            var startDate = DateTime.Now.AddMonths(-5);

            // تجهيز الأشهر الستة الأخيرة
            for (int i = 0; i <= 5; i++)
            {
                var monthKey = new DateTime(startDate.AddMonths(i).Year, startDate.AddMonths(i).Month, 1);
                monthly[monthKey] = 0;
            }

            // توزيع الدورات على الشهور
            foreach (var course in courses)
            {
                var key = new DateTime(course.StartDate.Year, course.StartDate.Month, 1);
                if (monthly.ContainsKey(key))
                    monthly[key]++;
            }

            // تحويل البيانات للرسم
            var sorted = monthly.OrderBy(x => x.Key).ToList();
            double[] xValues = sorted.Select(x => x.Key.ToOADate()).ToArray();
            double[] yValues = sorted.Select(x => (double)x.Value).ToArray();

            if (yValues.Length == 0)
                return;

            // مسح الرسم السابق
            coursesPlot.Plot.Clear();

            // رسم الأعمدة
            var bar = coursesPlot.Plot.AddBar(yValues);
            bar.Positions = xValues; // هذا المتاح في ScottPlot 4
            bar.FillColor = Color.FromArgb(72, 133, 237);
            bar.BorderColor = Color.FromArgb(51, 103, 214);
            bar.BarWidth = 20;

            // تنسيق المحور X كتاريخ
            coursesPlot.Plot.XAxis.DateTimeFormat(true);
            coursesPlot.Plot.XAxis.TickLabelFormat(x => DateTime.FromOADate(x).ToString("MMM yyyy"));

            // عنوان ووسوم المحاور
            coursesPlot.Plot.Title("توزيع الدورات التدريبية خلال 6 أشهر", color: ThemeManager.ChartForegroundColor);
            coursesPlot.Plot.XAxis.Label("الشهر");
            coursesPlot.Plot.YAxis.Label("عدد الدورات");

            // عرض القيم فوق الأعمدة
            for (int i = 0; i < yValues.Length; i++)
            {
                if (yValues[i] > 0)
                {
                    coursesPlot.Plot.AddText(
                        yValues[i].ToString(),
                        xValues[i],
                        yValues[i] + 0.1,
                        color: ThemeManager.ChartForegroundColor
                    );
                }
            }

            // حدود المحاور
            double yMax = yValues.Max();
            if (yMax == 0) yMax = 1;

            coursesPlot.Plot.SetAxisLimits(
                xMin: xValues[0] - 15,
                xMax: xValues[^1] + 15,
                yMin: 0,
                yMax: yMax * 1.4
            );

            // ضبط مظهر المحور X
            coursesPlot.Plot.XAxis.ManualTickSpacing(30);
            coursesPlot.Plot.XAxis.TickLabelStyle(rotation: 45);

            // ألوان السمة العامة
            coursesPlot.Plot.Style(
                figureBackground: ThemeManager.ChartBackgroundColor,
                dataBackground: ThemeManager.ChartBackgroundColor
            );
            coursesPlot.Plot.XAxis.TickLabelStyle(color: ThemeManager.ChartForegroundColor);
            coursesPlot.Plot.YAxis.TickLabelStyle(color: ThemeManager.ChartForegroundColor);

            coursesPlot.Refresh();
        }



        public void UpdateTheme(string theme)
        {
            currentTheme = theme;
            SetupDashboardControls();
            ThemeManager.ApplyThemeToForm(this);
            UpdateCharts(); // Refresh charts with new theme colors
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            refreshTimer?.Stop();
            refreshTimer?.Dispose();
            base.OnFormClosing(e);
        }
    }
}
