using System;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class SimpleEmployeeStatusFormV2 : Form
    {
        private int workPeriodId;
        private int employeeCode;
        private string employeeName;
        private DateTime startDate;
        private DateTime endDate;
        private bool isMultiSelectMode = false;

        private DataTable dailyData;

        public SimpleEmployeeStatusFormV2()
        {
            InitializeComponent();
            ThemeManager.ApplyThemeToForm(this);
        }

        public SimpleEmployeeStatusFormV2(int workPeriodId, int employeeCode, string employeeName, DateTime startDate, DateTime endDate)
        {
            this.workPeriodId = workPeriodId;
            this.employeeCode = employeeCode;
            this.employeeName = employeeName;
            this.startDate = startDate;
            this.endDate = endDate;

            InitializeComponent();
            SetupForm();
            LoadDailyStatus();
            ThemeManager.ApplyThemeToForm(this);
        }

        private void SetupForm()
        {
            this.Text = $"تعديل الحالة اليومية - {employeeName}";
            lblTitle.Text = $"تعديل الحالة اليومية للموظف: {employeeName}";
            cmbStatus.Items.AddRange(new[] { "حضور", "غياب", "إجازة" });

            // إضافة زر تحديث الأيام المحددة
            btnUpdateSelected = new Button();
            btnUpdateSelected.Text = "تحديث الأيام المحددة";
            btnUpdateSelected.AutoSize = true;
            btnUpdateSelected.Location = new Point(btnUpdate.Left, btnUpdate.Bottom + 10);
            btnUpdateSelected.Click += BtnUpdateSelected_Click;
            btnUpdateSelected.Visible = false;
            this.Controls.Add(btnUpdateSelected);
        }

        private void ChkMultiSelect_CheckedChanged(object sender, EventArgs e)
        {
            isMultiSelectMode = chkMultiSelect.Checked;
            listBoxDays.SelectionMode = isMultiSelectMode ?
                SelectionMode.MultiExtended : SelectionMode.One;
            btnUpdateSelected.Visible = isMultiSelectMode;
        }

        private void BtnUpdateSelected_Click(object sender, EventArgs e)
        {
            if (listBoxDays.SelectedIndices.Count == 0 || cmbStatus.SelectedItem == null)
            {
                MessageBox.Show("الرجاء تحديد يوم واحد على الأقل والحالة المطلوبة", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show(
                $"هل أنت متأكد من تحديث حالة {listBoxDays.SelectedIndices.Count} يوم إلى \"{cmbStatus.SelectedItem}\"؟",
                "تأكيد التحديث",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                foreach (int index in listBoxDays.SelectedIndices)
                {
                    var selectedRow = dailyData.Rows[index];
                    selectedRow["الحالة"] = cmbStatus.SelectedItem.ToString();
                    selectedRow["الملاحظات"] = txtNotes.Text;

                    // تحديث النص المعروض
                    DateTime date = (DateTime)selectedRow["التاريخ"];
                    string dayName = selectedRow["اليوم"].ToString();
                    string status = selectedRow["الحالة"].ToString();
                    selectedRow["العرض"] = $"{date:dd/MM/yyyy} - {dayName} - {status}";
                }

                UpdateDaysList();
                MessageBox.Show("تم تحديث الأيام المحددة محلياً. اضغط 'حفظ الكل' لحفظ التغييرات", "تم التحديث",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void LoadDailyStatus()
        {
            try
            {
                // إنشاء جدول للبيانات
                dailyData = new DataTable();
                dailyData.Columns.Add("التاريخ", typeof(DateTime));
                dailyData.Columns.Add("اليوم", typeof(string));
                dailyData.Columns.Add("الحالة", typeof(string));
                dailyData.Columns.Add("الملاحظات", typeof(string));
                dailyData.Columns.Add("العرض", typeof(string));

                // إضافة الأيام
                for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
                {
                    var statusData = GetDailyStatus(date);
                    string dayName = GetDayName(date.DayOfWeek);
                    string displayText = $"{date:dd/MM/yyyy} - {dayName} - {statusData.Status}";

                    var newRow = dailyData.NewRow();
                    newRow["التاريخ"] = date;
                    newRow["اليوم"] = dayName;
                    newRow["الحالة"] = statusData.Status;
                    newRow["الملاحظات"] = statusData.Notes;
                    newRow["العرض"] = displayText;

                    dailyData.Rows.Add(newRow);
                }

                // تحديث قائمة الأيام
                UpdateDaysList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateDaysList()
        {
            listBoxDays.Items.Clear();
            foreach (DataRow row in dailyData.Rows)
            {
                listBoxDays.Items.Add(row["العرض"].ToString());
            }
        }

        private (string Status, string Notes) GetDailyStatus(DateTime date)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = @"SELECT Status, Notes FROM DailyWorkStatus 
                          WHERE WorkPeriodId = @WorkPeriodId 
                          AND EmployeeCode = @EmployeeCode 
                          AND CAST(Date AS DATE) = @Date";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                        command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                        command.Parameters.AddWithValue("@Date", date.Date);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                int statusOrdinal = reader.GetOrdinal("Status");
                                int notesOrdinal = reader.GetOrdinal("Notes");

                                string status = reader.IsDBNull(statusOrdinal) ? "حضور" : reader.GetString(statusOrdinal);
                                string notes = reader.IsDBNull(notesOrdinal) ? "" : reader.GetString(notesOrdinal);

                                return (status, notes);
                            }
                        }
                    }
                }

                return ("حضور", ""); // افتراضي إذا لم يوجد سجل
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("خطأ في GetDailyStatus: " + ex.Message);
                return ("حضور", ""); // افتراضي في حالة الخطأ
            }
        }


        private string GetDayName(DayOfWeek dayOfWeek)
        {
            switch (dayOfWeek)
            {
                case DayOfWeek.Sunday: return "الأحد";
                case DayOfWeek.Monday: return "الاثنين";
                case DayOfWeek.Tuesday: return "الثلاثاء";
                case DayOfWeek.Wednesday: return "الأربعاء";
                case DayOfWeek.Thursday: return "الخميس";
                case DayOfWeek.Friday: return "الجمعة";
                case DayOfWeek.Saturday: return "السبت";
                default: return "";
            }
        }

        private void ListBoxDays_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (!isMultiSelectMode && listBoxDays.SelectedIndex >= 0 &&
                listBoxDays.SelectedIndex < dailyData.Rows.Count)
            {
                var selectedRow = dailyData.Rows[listBoxDays.SelectedIndex];
                DateTime selectedDate = (DateTime)selectedRow["التاريخ"];

                lblSelectedDay.Text = $"اليوم المحدد: {selectedDate:dd/MM/yyyy} - {selectedRow["اليوم"]}";
                cmbStatus.SelectedItem = selectedRow["الحالة"].ToString();
                txtNotes.Text = selectedRow["الملاحظات"].ToString();
            }
            else if (isMultiSelectMode && listBoxDays.SelectedIndices.Count > 0)
            {
                lblSelectedDay.Text = $"تم تحديد {listBoxDays.SelectedIndices.Count} يوم";
                txtNotes.Clear();
            }
        }

        private void BtnUpdate_Click(object sender, EventArgs e)
        {
            if (listBoxDays.SelectedIndex >= 0 && cmbStatus.SelectedItem != null)
            {
                var selectedRow = dailyData.Rows[listBoxDays.SelectedIndex];
                selectedRow["الحالة"] = cmbStatus.SelectedItem.ToString();
                selectedRow["الملاحظات"] = txtNotes.Text;

                // تحديث النص المعروض
                DateTime date = (DateTime)selectedRow["التاريخ"];
                string dayName = selectedRow["اليوم"].ToString();
                string status = selectedRow["الحالة"].ToString();
                selectedRow["العرض"] = $"{date:dd/MM/yyyy} - {dayName} - {status}";

                UpdateDaysList();
                listBoxDays.SelectedIndex = listBoxDays.SelectedIndex; // الحفاظ على التحديد

                MessageBox.Show("تم تحديث اليوم محلياً. اضغط 'حفظ الكل' لحفظ التغييرات", "تم التحديث",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                int updatedCount = 0;

                foreach (DataRow row in dailyData.Rows)
                {
                    DateTime date = (DateTime)row["التاريخ"];
                    string status = row["الحالة"].ToString();
                    string notes = row["الملاحظات"].ToString();

                    // تحديث قاعدة البيانات
                    DatabaseHelper.UpdateDailyWorkStatus(workPeriodId, employeeCode, date, status, notes);
                    updatedCount++;
                }

                MessageBox.Show($"تم تحديث {updatedCount} يوم بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التغييرات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
