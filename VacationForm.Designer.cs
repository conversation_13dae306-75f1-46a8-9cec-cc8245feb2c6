namespace EmployeeManagementSystem
{
    partial class VacationForm
    {
        private System.ComponentModel.IContainer components = null;
        private CheckBox chkSelectAll;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            DataGridViewCellStyle dataGridViewCellStyle11 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle12 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle13 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle14 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle15 = new DataGridViewCellStyle();
            txtSearch = new TextBox();
            dataGridView1 = new DataGridView();
            chkSelectAll = new CheckBox();
            label1 = new Label();
            label2 = new Label();
            label3 = new Label();
            label4 = new Label();
            label5 = new Label();
            cmbEmployeeName = new ComboBox();
            cmbVacationType = new ComboBox();
            dtStartDate = new DateTimePicker();
            dtEndDate = new DateTimePicker();
            txtReason = new TextBox();
            btnAdd = new Button();
            btnUpdate = new Button();
            btnDelete = new Button();
            btnPrint = new Button();
            btnClear = new Button();
            btnApprove = new Button();
            btnReject = new Button();
            groupBox1 = new GroupBox();
            btnViewRequests = new Button();
            btnPrintHtml = new Button();
            label7 = new Label();
            cmbMonthFilter = new ComboBox();
            button1 = new Button();
            btnSearchv = new Button();
            vacationsPlot = new ScottPlot.FormsPlot();
            plotPanel = new Panel();
            plotTypeComboBox = new ComboBox();
            lblChartType = new Label();
            toolTip1 = new ToolTip(components);
            lbl_NoDocuments = new Label();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).BeginInit();
            groupBox1.SuspendLayout();
            plotPanel.SuspendLayout();
            SuspendLayout();
            // 
            // txtSearch
            // 
            txtSearch.Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            txtSearch.Location = new Point(107, 273);
            txtSearch.Multiline = true;
            txtSearch.Name = "txtSearch";
            txtSearch.PlaceholderText = "ابحث عن اجازة...";
            txtSearch.Size = new Size(395, 36);
            txtSearch.TabIndex = 9;
            toolTip1.SetToolTip(txtSearch, "البحث عن اجازة موظف");
            txtSearch.KeyDown += txtSearch_KeyDown;
            // 
            // dataGridView1
            // 
            dataGridView1.AllowUserToAddRows = false;
            dataGridView1.AllowUserToDeleteRows = false;
            dataGridViewCellStyle11.Font = new Font("Cairo", 12F, FontStyle.Bold);
            dataGridView1.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle11;
            dataGridView1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridViewCellStyle12.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle12.BackColor = SystemColors.Control;
            dataGridViewCellStyle12.Font = new Font("Cairo", 12F, FontStyle.Bold);
            dataGridViewCellStyle12.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle12.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle12.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle12.WrapMode = DataGridViewTriState.True;
            dataGridView1.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle12;
            dataGridView1.ColumnHeadersHeight = 35;
            dataGridViewCellStyle13.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle13.BackColor = SystemColors.Window;
            dataGridViewCellStyle13.Font = new Font("Cairo", 12F, FontStyle.Bold);
            dataGridViewCellStyle13.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle13.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle13.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle13.WrapMode = DataGridViewTriState.False;
            dataGridView1.DefaultCellStyle = dataGridViewCellStyle13;
            dataGridView1.Location = new Point(0, 313);
            dataGridView1.Name = "dataGridView1";
            dataGridView1.ReadOnly = true;
            dataGridViewCellStyle14.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle14.BackColor = SystemColors.Control;
            dataGridViewCellStyle14.Font = new Font("Cairo", 12F, FontStyle.Bold);
            dataGridViewCellStyle14.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle14.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle14.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle14.WrapMode = DataGridViewTriState.True;
            dataGridView1.RowHeadersDefaultCellStyle = dataGridViewCellStyle14;
            dataGridViewCellStyle15.Font = new Font("Cairo", 12F, FontStyle.Bold);
            dataGridView1.RowsDefaultCellStyle = dataGridViewCellStyle15;
            dataGridView1.Size = new Size(1191, 102);
            dataGridView1.TabIndex = 13;
            dataGridView1.CellClick += dataGridView1_CellClick;
            // 
            // chkSelectAll
            // 
            chkSelectAll.Font = new Font("Cairo", 12F, FontStyle.Bold);
            chkSelectAll.Image = Properties.Resources.checked_checkbox_32px;
            chkSelectAll.ImageAlign = ContentAlignment.MiddleRight;
            chkSelectAll.Location = new Point(513, 272);
            chkSelectAll.Name = "chkSelectAll";
            chkSelectAll.RightToLeft = RightToLeft.No;
            chkSelectAll.Size = new Size(100, 36);
            chkSelectAll.TabIndex = 14;
            chkSelectAll.Text = "تحديد";
            toolTip1.SetToolTip(chkSelectAll, "تحديد الكل");
            chkSelectAll.UseVisualStyleBackColor = true;
            chkSelectAll.CheckedChanged += chkSelectAll_CheckedChanged;
            // 
            // label1
            // 
            label1.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label1.AutoSize = true;
            label1.Location = new Point(1038, 38);
            label1.Name = "label1";
            label1.Size = new Size(113, 30);
            label1.TabIndex = 2;
            label1.Text = "اسم الموظف:";
            // 
            // label2
            // 
            label2.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label2.AutoSize = true;
            label2.Location = new Point(1061, 78);
            label2.Name = "label2";
            label2.Size = new Size(90, 30);
            label2.TabIndex = 3;
            label2.Text = "نوع الإجازة:";
            // 
            // label3
            // 
            label3.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label3.AutoSize = true;
            label3.Location = new Point(1049, 122);
            label3.Name = "label3";
            label3.Size = new Size(102, 30);
            label3.TabIndex = 4;
            label3.Text = "تاريخ البداية:";
            // 
            // label4
            // 
            label4.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label4.AutoSize = true;
            label4.Location = new Point(1046, 163);
            label4.Name = "label4";
            label4.Size = new Size(105, 30);
            label4.TabIndex = 5;
            label4.Text = "تاريخ النهاية:";
            // 
            // label5
            // 
            label5.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label5.AutoSize = true;
            label5.Location = new Point(565, 35);
            label5.Name = "label5";
            label5.Size = new Size(101, 30);
            label5.TabIndex = 6;
            label5.Text = "سبب الإجازة:";
            // 
            // cmbEmployeeName
            // 
            cmbEmployeeName.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbEmployeeName.Font = new Font("Cairo", 12F, FontStyle.Bold);
            cmbEmployeeName.FormattingEnabled = true;
            cmbEmployeeName.Location = new Point(672, 35);
            cmbEmployeeName.Name = "cmbEmployeeName";
            cmbEmployeeName.RightToLeft = RightToLeft.No;
            cmbEmployeeName.Size = new Size(356, 38);
            cmbEmployeeName.TabIndex = 0;
            // 
            // cmbVacationType
            // 
            cmbVacationType.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbVacationType.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbVacationType.FormattingEnabled = true;
            cmbVacationType.Location = new Point(672, 75);
            cmbVacationType.Name = "cmbVacationType";
            cmbVacationType.RightToLeft = RightToLeft.No;
            cmbVacationType.Size = new Size(356, 38);
            cmbVacationType.TabIndex = 1;
            // 
            // dtStartDate
            // 
            dtStartDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            dtStartDate.Format = DateTimePickerFormat.Short;
            dtStartDate.Location = new Point(672, 117);
            dtStartDate.Name = "dtStartDate";
            dtStartDate.Size = new Size(356, 37);
            dtStartDate.TabIndex = 2;
            // 
            // dtEndDate
            // 
            dtEndDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            dtEndDate.Format = DateTimePickerFormat.Short;
            dtEndDate.Location = new Point(672, 158);
            dtEndDate.Name = "dtEndDate";
            dtEndDate.Size = new Size(356, 37);
            dtEndDate.TabIndex = 3;
            // 
            // txtReason
            // 
            txtReason.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtReason.Location = new Point(19, 34);
            txtReason.Multiline = true;
            txtReason.Name = "txtReason";
            txtReason.Size = new Size(540, 123);
            txtReason.TabIndex = 4;
            // 
            // btnAdd
            // 
            btnAdd.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnAdd.Image = Properties.Resources.ok_32px;
            btnAdd.ImageAlign = ContentAlignment.MiddleRight;
            btnAdd.Location = new Point(1058, 205);
            btnAdd.Name = "btnAdd";
            btnAdd.Size = new Size(100, 46);
            btnAdd.TabIndex = 5;
            btnAdd.Text = "إضافة";
            btnAdd.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnAdd, "إضافة");
            btnAdd.UseVisualStyleBackColor = true;
            btnAdd.Click += btnAdd_Click;
            // 
            // btnUpdate
            // 
            btnUpdate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnUpdate.Image = Properties.Resources.edit_profile_32px;
            btnUpdate.ImageAlign = ContentAlignment.MiddleRight;
            btnUpdate.Location = new Point(960, 205);
            btnUpdate.Name = "btnUpdate";
            btnUpdate.Size = new Size(93, 46);
            btnUpdate.TabIndex = 6;
            btnUpdate.Text = "تعديل";
            btnUpdate.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnUpdate, "تعديل");
            btnUpdate.UseVisualStyleBackColor = true;
            btnUpdate.Click += btnUpdate_Click;
            // 
            // btnDelete
            // 
            btnDelete.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnDelete.Image = Properties.Resources.remove_32px;
            btnDelete.ImageAlign = ContentAlignment.MiddleRight;
            btnDelete.Location = new Point(872, 205);
            btnDelete.Name = "btnDelete";
            btnDelete.Size = new Size(82, 46);
            btnDelete.TabIndex = 7;
            btnDelete.Text = "حذف";
            btnDelete.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnDelete, "حذف");
            btnDelete.UseVisualStyleBackColor = true;
            btnDelete.Click += btnDelete_Click;
            // 
            // btnPrint
            // 
            btnPrint.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnPrint.Image = Properties.Resources.print_32px;
            btnPrint.ImageAlign = ContentAlignment.MiddleRight;
            btnPrint.Location = new Point(219, 208);
            btnPrint.Name = "btnPrint";
            btnPrint.Size = new Size(95, 46);
            btnPrint.TabIndex = 10;
            btnPrint.Text = "طباعة";
            btnPrint.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnPrint, "طباعة موظف");
            btnPrint.UseVisualStyleBackColor = true;
            btnPrint.Click += btnPrint_Click;
            // 
            // btnClear
            // 
            btnClear.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnClear.Image = Properties.Resources.clear_formatting_32px;
            btnClear.ImageAlign = ContentAlignment.MiddleRight;
            btnClear.Location = new Point(777, 205);
            btnClear.Name = "btnClear";
            btnClear.Size = new Size(90, 46);
            btnClear.TabIndex = 8;
            btnClear.Text = "إفراغ";
            btnClear.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnClear, "إفراغ الحقول");
            btnClear.UseVisualStyleBackColor = true;
            btnClear.Click += btnClear_Click;
            // 
            // btnApprove
            // 
            btnApprove.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnApprove.BackColor = Color.FromArgb(40, 167, 69);
            btnApprove.Enabled = false;
            btnApprove.ForeColor = Color.White;
            btnApprove.Image = Properties.Resources.ok_32px;
            btnApprove.ImageAlign = ContentAlignment.MiddleRight;
            btnApprove.Location = new Point(513, 204);
            btnApprove.Name = "btnApprove";
            btnApprove.Size = new Size(109, 46);
            btnApprove.TabIndex = 12;
            btnApprove.Text = "معتمدة";
            btnApprove.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnApprove, "اعتماد الإجازة");
            btnApprove.UseVisualStyleBackColor = false;
            btnApprove.Visible = false;
            btnApprove.Click += btnApprove_Click;
            // 
            // btnReject
            // 
            btnReject.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnReject.BackColor = Color.FromArgb(220, 53, 69);
            btnReject.Enabled = false;
            btnReject.ForeColor = Color.White;
            btnReject.Image = Properties.Resources.remove_32px;
            btnReject.ImageAlign = ContentAlignment.MiddleRight;
            btnReject.Location = new Point(417, 204);
            btnReject.Name = "btnReject";
            btnReject.Size = new Size(90, 46);
            btnReject.TabIndex = 13;
            btnReject.Text = "رفض";
            btnReject.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnReject, "رفض الإجازة");
            btnReject.UseVisualStyleBackColor = false;
            btnReject.Visible = false;
            btnReject.Click += btnReject_Click;
            // 
            // groupBox1
            // 
            groupBox1.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBox1.Controls.Add(btnApprove);
            groupBox1.Controls.Add(btnReject);
            groupBox1.Controls.Add(btnViewRequests);
            groupBox1.Controls.Add(btnPrintHtml);
            groupBox1.Controls.Add(label7);
            groupBox1.Controls.Add(cmbMonthFilter);
            groupBox1.Controls.Add(cmbEmployeeName);
            groupBox1.Controls.Add(button1);
            groupBox1.Controls.Add(btnPrint);
            groupBox1.Controls.Add(btnClear);
            groupBox1.Controls.Add(label1);
            groupBox1.Controls.Add(btnDelete);
            groupBox1.Controls.Add(label2);
            groupBox1.Controls.Add(btnUpdate);
            groupBox1.Controls.Add(label3);
            groupBox1.Controls.Add(btnAdd);
            groupBox1.Controls.Add(label4);
            groupBox1.Controls.Add(txtReason);
            groupBox1.Controls.Add(label5);
            groupBox1.Controls.Add(dtEndDate);
            groupBox1.Controls.Add(cmbVacationType);
            groupBox1.Controls.Add(dtStartDate);
            groupBox1.Font = new Font("Cairo", 12F, FontStyle.Bold);
            groupBox1.Location = new Point(10, 9);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(1169, 260);
            groupBox1.TabIndex = 16;
            groupBox1.TabStop = false;
            groupBox1.Text = "بيانات الإجازة";
            // 
            // btnViewRequests
            // 
            btnViewRequests.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnViewRequests.BackColor = Color.FromArgb(0, 123, 255);
            btnViewRequests.ForeColor = Color.White;
            btnViewRequests.Image = Properties.Resources.view_32px;
            btnViewRequests.ImageAlign = ContentAlignment.MiddleRight;
            btnViewRequests.Location = new Point(626, 205);
            btnViewRequests.Name = "btnViewRequests";
            btnViewRequests.Size = new Size(145, 46);
            btnViewRequests.TabIndex = 14;
            btnViewRequests.Text = "عرض الطلبات";
            btnViewRequests.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnViewRequests, "عرض طلبات الإجازات المرسلة من الموظفين");
            btnViewRequests.UseVisualStyleBackColor = false;
            btnViewRequests.Click += btnViewRequests_Click;
            // 
            // btnPrintHtml
            // 
            btnPrintHtml.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnPrintHtml.Image = Properties.Resources.print_32px;
            btnPrintHtml.ImageAlign = ContentAlignment.MiddleRight;
            btnPrintHtml.Location = new Point(123, 208);
            btnPrintHtml.Name = "btnPrintHtml";
            btnPrintHtml.Size = new Size(90, 46);
            btnPrintHtml.TabIndex = 18;
            btnPrintHtml.Text = " الكل";
            btnPrintHtml.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnPrintHtml, "طباعة كل الموظفين");
            btnPrintHtml.UseVisualStyleBackColor = true;
            btnPrintHtml.Click += btnPrintHtml_Click;
            // 
            // label7
            // 
            label7.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label7.Image = Properties.Resources.filter_32px;
            label7.ImageAlign = ContentAlignment.MiddleRight;
            label7.Location = new Point(278, 165);
            label7.Name = "label7";
            label7.Size = new Size(185, 36);
            label7.TabIndex = 19;
            label7.Text = "تصفية حسب الشهر:";
            label7.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // cmbMonthFilter
            // 
            cmbMonthFilter.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbMonthFilter.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbMonthFilter.FormattingEnabled = true;
            cmbMonthFilter.Location = new Point(19, 163);
            cmbMonthFilter.Name = "cmbMonthFilter";
            cmbMonthFilter.RightToLeft = RightToLeft.No;
            cmbMonthFilter.Size = new Size(253, 38);
            cmbMonthFilter.TabIndex = 20;
            toolTip1.SetToolTip(cmbMonthFilter, "اختيار الشهر");
            cmbMonthFilter.SelectedIndexChanged += cmbMonthFilter_SelectedIndexChanged;
            // 
            // button1
            // 
            button1.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            button1.Image = Properties.Resources.microsoft_excel_2019_32px;
            button1.ImageAlign = ContentAlignment.MiddleRight;
            button1.Location = new Point(18, 208);
            button1.Name = "button1";
            button1.Size = new Size(99, 46);
            button1.TabIndex = 11;
            button1.Text = "تصدير";
            button1.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(button1, "تصدير");
            button1.UseVisualStyleBackColor = true;
            button1.Click += button1_Click;
            // 
            // btnSearchv
            // 
            btnSearchv.Font = new Font("Cairo", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnSearchv.Image = Properties.Resources.search_32px;
            btnSearchv.ImageAlign = ContentAlignment.MiddleRight;
            btnSearchv.Location = new Point(10, 269);
            btnSearchv.Name = "btnSearchv";
            btnSearchv.Size = new Size(98, 43);
            btnSearchv.TabIndex = 21;
            btnSearchv.Text = "بحث";
            btnSearchv.TextAlign = ContentAlignment.MiddleLeft;
            btnSearchv.UseVisualStyleBackColor = true;
            btnSearchv.Click += btnSearchv_Click;
            // 
            // vacationsPlot
            // 
            vacationsPlot.Dock = DockStyle.Fill;
            vacationsPlot.Location = new Point(0, 0);
            vacationsPlot.Margin = new Padding(4, 3, 4, 3);
            vacationsPlot.Name = "vacationsPlot";
            vacationsPlot.Size = new Size(1191, 240);
            vacationsPlot.TabIndex = 0;
            // 
            // plotPanel
            // 
            plotPanel.Controls.Add(vacationsPlot);
            plotPanel.Dock = DockStyle.Bottom;
            plotPanel.Location = new Point(0, 421);
            plotPanel.Name = "plotPanel";
            plotPanel.Size = new Size(1191, 240);
            plotPanel.TabIndex = 18;
            // 
            // plotTypeComboBox
            // 
            plotTypeComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            plotTypeComboBox.Font = new Font("Cairo", 10F);
            plotTypeComboBox.FormattingEnabled = true;
            plotTypeComboBox.Items.AddRange(new object[] { "رسم بياني شريطي للإجازات حسب النوع", "رسم بياني دائري لتوزيع الإجازات", "رسم بياني خطي للإجازات حسب الشهر" });
            plotTypeComboBox.Location = new Point(926, 275);
            plotTypeComboBox.Name = "plotTypeComboBox";
            plotTypeComboBox.RightToLeft = RightToLeft.No;
            plotTypeComboBox.Size = new Size(253, 32);
            plotTypeComboBox.TabIndex = 1;
            plotTypeComboBox.SelectedIndexChanged += plotTypeComboBox_SelectedIndexChanged;
            // 
            // lblChartType
            // 
            lblChartType.Font = new Font("Cairo", 10F, FontStyle.Bold);
            lblChartType.Image = Properties.Resources.chart_increasing_32px;
            lblChartType.ImageAlign = ContentAlignment.MiddleRight;
            lblChartType.Location = new Point(800, 277);
            lblChartType.Name = "lblChartType";
            lblChartType.Size = new Size(120, 30);
            lblChartType.TabIndex = 2;
            lblChartType.Text = "نوع المخطط:";
            lblChartType.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // lbl_NoDocuments
            // 
            lbl_NoDocuments.Anchor = AnchorStyles.None;
            lbl_NoDocuments.AutoSize = true;
            lbl_NoDocuments.Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lbl_NoDocuments.Location = new Point(568, 353);
            lbl_NoDocuments.Name = "lbl_NoDocuments";
            lbl_NoDocuments.Size = new Size(54, 30);
            lbl_NoDocuments.TabIndex = 36;
            lbl_NoDocuments.Text = "label6";
            lbl_NoDocuments.Visible = false;
            // 
            // VacationForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1191, 661);
            Controls.Add(btnSearchv);
            Controls.Add(plotTypeComboBox);
            Controls.Add(lblChartType);
            Controls.Add(chkSelectAll);
            Controls.Add(txtSearch);
            Controls.Add(lbl_NoDocuments);
            Controls.Add(plotPanel);
            Controls.Add(dataGridView1);
            Controls.Add(groupBox1);
            Name = "VacationForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            Text = "إدارة الإجازات";
            Load += VacationForm_Load;
            ((System.ComponentModel.ISupportInitialize)dataGridView1).EndInit();
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            plotPanel.ResumeLayout(false);
            ResumeLayout(false);
            PerformLayout();
        }

        private TextBox txtSearch;
        private DataGridView dataGridView1;
        private Label label1;
        private Label label2;
        private Label label3;
        private Label label4;
        private Label label5;
        private ComboBox cmbEmployeeName;
        private ComboBox cmbVacationType;
        private DateTimePicker dtStartDate;
        private DateTimePicker dtEndDate;
        private TextBox txtReason;
        private Button btnAdd;
        private Button btnUpdate;
        private Button btnDelete;
        private Button btnPrint;
        private Button btnClear;
        private Button btnApprove;
        private Button btnReject;
        private GroupBox groupBox1;
        private ScottPlot.FormsPlot vacationsPlot;
        private Panel plotPanel;
        private ComboBox plotTypeComboBox;
        private Label lblChartType;
        private Button button1;
        private Button btnPrintHtml;
        private Label label7;
        private ComboBox cmbMonthFilter;
        private ToolTip toolTip1;
        private Label lbl_NoDocuments;
        private Button btnSearchv;
        private Button btnViewRequests;
    }
}