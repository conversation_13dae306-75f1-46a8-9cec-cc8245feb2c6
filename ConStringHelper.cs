﻿using System;

namespace EmployeeManagementSystem
{
    public static class ConStringHelper
    {
        private static string _conStringValue = string.Empty;

        public static string GetConnectionString()
        {
            return _conStringValue;
        }

        public static void SetConString()
        {
            string server = Properties.Settings.Default.Server;
            string db = Properties.Settings.Default.DataBase;

            if (Properties.Settings.Default.ServerType == "Local")
            {
                _conStringValue = $"Server={server};Database={db};Encrypt=false;Trusted_Connection=True;";
            }
            else
            {
                string AUserName = Properties.Settings.Default.AUserName;
                string APassword = Properties.Settings.Default.APassword;
                string duration = Properties.Settings.Default.Timeout.ToString();

                _conStringValue = $"Server={server};Database={db};User Id={AUserName};Password={APassword};Encrypt=false;Timeout={duration};";
            }
        }
    }
}
