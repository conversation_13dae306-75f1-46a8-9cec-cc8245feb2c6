using System;
using System.Data;
using System.Windows.Forms;
using System.Drawing;
using System.Threading.Tasks;
using ClosedXML.Excel;

namespace EmployeeManagementSystem
{
    public partial class CourseStatisticsForm : Form
    {
        private User? currentUser = null; // إضافة المستخدم الحالي

        public CourseStatisticsForm()
        {
            InitializeComponent();
            LoadStatistics();
            ThemeManager.ApplyThemeToForm(this);
        }

        // Constructor جديد يستقبل المستخدم الحالي
        public CourseStatisticsForm(User currentUser) : this()
        {
            this.currentUser = currentUser;

            // عرض رسالة تحذيرية حول الفلترة
            if (EmployeeDepartmentHelper.IsDepartmentManager(currentUser))
            {
                string warningMessage = EmployeeDepartmentHelper.GetFilterWarningMessage(currentUser);
                MessageBox.Show(warningMessage, "معلومات الفلترة", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // تحديث عنوان النافذة
                this.Text = "🔒 إحصائيات الدورات - مدير القسم";
            }
            else if (EmployeeDepartmentHelper.IsGeneralManager(currentUser))
            {
                this.Text = "🌐 إحصائيات الدورات - مدير عام";
            }
        }

        private async void LoadStatistics()
        {
            try
            {
                // تحميل الإحصائيات الأساسية مع تطبيق الفلترة
                var stats = await Task.Run(() => GetFilteredCourseStatistics());

                lblTotalCoursesValue.Text = stats.TotalCourses.ToString();
                lblActiveCoursesValue.Text = stats.ActiveCourses.ToString();
                lblCompletedCoursesValue.Text = stats.CompletedCourses.ToString();
                lblScheduledCoursesValue.Text = stats.ScheduledCourses.ToString();
                lblAverageRatingValue.Text = stats.AverageRating.ToString("F2");
                lblAttendanceRateValue.Text = stats.AttendanceRate.ToString("F1") + "%";

                // تحميل التقرير الشامل مع الفلترة
                var comprehensiveReport = await Task.Run(() => GetFilteredComprehensiveCourseReport());
                dataGridViewComprehensive.DataSource = comprehensiveReport;

                // تحميل الدورات المنتهية مع الفلترة
                var expiredCourses = await Task.Run(() => GetFilteredExpiredCourses());
                dataGridViewExpired.DataSource = expiredCourses;

                // تطبيق التنسيق
                ApplyGridFormatting();
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تحميل الإحصائيات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private CourseStatistics GetFilteredCourseStatistics()
        {
            // إرجاع الإحصائيات العامة (يمكن تطوير الفلترة لاحقاً)
            return CourseManagementHelper.GetCourseStatistics();
        }

        private DataTable GetFilteredComprehensiveCourseReport()
        {
            var report = CourseManagementHelper.GetComprehensiveCourseReport();

            if (EmployeeDepartmentHelper.IsDepartmentManager(currentUser))
            {
                var accessibleCodes = EmployeeDepartmentHelper.GetAccessibleEmployeeCodes(currentUser);
                var filteredTable = report.Clone();

                foreach (DataRow row in report.Rows)
                {
                    if (int.TryParse(row["EmployeeCode"]?.ToString(), out int empCode) &&
                        accessibleCodes.Contains(empCode))
                    {
                        filteredTable.ImportRow(row);
                    }
                }
                return filteredTable;
            }

            return report;
        }

        private DataTable GetFilteredExpiredCourses()
        {
            var expiredCourses = CourseManagementHelper.GetExpiredCourses();

            if (EmployeeDepartmentHelper.IsDepartmentManager(currentUser))
            {
                var accessibleCodes = EmployeeDepartmentHelper.GetAccessibleEmployeeCodes(currentUser);
                var filteredTable = expiredCourses.Clone();

                foreach (DataRow row in expiredCourses.Rows)
                {
                    if (int.TryParse(row["EmployeeCode"]?.ToString(), out int empCode) &&
                        accessibleCodes.Contains(empCode))
                    {
                        filteredTable.ImportRow(row);
                    }
                }
                return filteredTable;
            }

            return expiredCourses;
        }

        private void ApplyGridFormatting()
        {
            // تنسيق الجدول الشامل
            if (dataGridViewComprehensive.Columns.Count > 0)
            {
                dataGridViewComprehensive.RightToLeft = RightToLeft.Yes;
                dataGridViewComprehensive.EnableHeadersVisualStyles = false;
                dataGridViewComprehensive.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(45, 66, 91);
                dataGridViewComprehensive.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
                dataGridViewComprehensive.ColumnHeadersDefaultCellStyle.Font = new Font("Cairo", 10F, FontStyle.Bold);
                dataGridViewComprehensive.DefaultCellStyle.Font = new Font("Cairo", 9F);
                dataGridViewComprehensive.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(237, 243, 247);
            }

            // تنسيق جدول الدورات المنتهية
            if (dataGridViewExpired.Columns.Count > 0)
            {
                dataGridViewExpired.RightToLeft = RightToLeft.Yes;
                dataGridViewExpired.EnableHeadersVisualStyles = false;
                dataGridViewExpired.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(231, 76, 60);
                dataGridViewExpired.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
                dataGridViewExpired.ColumnHeadersDefaultCellStyle.Font = new Font("Cairo", 10F, FontStyle.Bold);
                dataGridViewExpired.DefaultCellStyle.Font = new Font("Cairo", 9F);
                dataGridViewExpired.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(253, 237, 236);
            }
        }

        private async void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadStatistics();
        }

        private async void btnUpdateExpiredCourses_Click(object sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل تريد تحديث حالة الدورات المنتهية تلقائياً؟", 
                    "تأكيد التحديث", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await CourseManagementHelper.UpdateExpiredCoursesStatusAsync();
                    MessageBox.Show("تم تحديث حالة الدورات المنتهية بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadStatistics();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تحديث الدورات المنتهية: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnSendNotifications_Click(object sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل تريد إرسال إشعارات للدورات التي تبدأ قريباً؟", 
                    "تأكيد الإرسال", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await CourseManagementHelper.SendUpcomingCourseNotificationsAsync();
                    MessageBox.Show("تم إرسال الإشعارات بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء إرسال الإشعارات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnExportComprehensive_Click(object sender, EventArgs e)
        {
            try
            {
                using (SaveFileDialog sfd = new SaveFileDialog())
                {
                    sfd.Filter = "Excel Workbook|*.xlsx";
                    sfd.FileName = "التقرير_الشامل_للدورات_" + DateTime.Now.ToString("yyyy-MM-dd") + ".xlsx";

                    if (sfd.ShowDialog() == DialogResult.OK)
                    {
                        using (XLWorkbook workbook = new XLWorkbook())
                        {
                            var worksheet = workbook.Worksheets.Add("التقرير الشامل");

                            // Add headers
                            for (int i = 0; i < dataGridViewComprehensive.Columns.Count; i++)
                            {
                                worksheet.Cell(1, i + 1).Value = dataGridViewComprehensive.Columns[i].HeaderText;
                            }

                            // Add data
                            for (int i = 0; i < dataGridViewComprehensive.Rows.Count; i++)
                            {
                                for (int j = 0; j < dataGridViewComprehensive.Columns.Count; j++)
                                {
                                    worksheet.Cell(i + 2, j + 1).Value = dataGridViewComprehensive.Rows[i].Cells[j].Value?.ToString() ?? "";
                                }
                            }

                            // Format as table
                            var range = worksheet.Range(1, 1, dataGridViewComprehensive.Rows.Count + 1, dataGridViewComprehensive.Columns.Count);
                            range.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            range.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                            worksheet.Row(1).Style.Fill.BackgroundColor = XLColor.LightGray;
                            worksheet.Row(1).Style.Font.Bold = true;

                            worksheet.Columns().AdjustToContents();
                            workbook.SaveAs(sfd.FileName);
                        }

                        MessageBox.Show("تم تصدير التقرير الشامل بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تصدير التقرير: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnExportExpired_Click(object sender, EventArgs e)
        {
            try
            {
                using (SaveFileDialog sfd = new SaveFileDialog())
                {
                    sfd.Filter = "Excel Workbook|*.xlsx";
                    sfd.FileName = "الدورات_المنتهية_" + DateTime.Now.ToString("yyyy-MM-dd") + ".xlsx";

                    if (sfd.ShowDialog() == DialogResult.OK)
                    {
                        using (XLWorkbook workbook = new XLWorkbook())
                        {
                            var worksheet = workbook.Worksheets.Add("الدورات المنتهية");

                            // Add headers
                            for (int i = 0; i < dataGridViewExpired.Columns.Count; i++)
                            {
                                worksheet.Cell(1, i + 1).Value = dataGridViewExpired.Columns[i].HeaderText;
                            }

                            // Add data
                            for (int i = 0; i < dataGridViewExpired.Rows.Count; i++)
                            {
                                for (int j = 0; j < dataGridViewExpired.Columns.Count; j++)
                                {
                                    worksheet.Cell(i + 2, j + 1).Value = dataGridViewExpired.Rows[i].Cells[j].Value?.ToString() ?? "";
                                }
                            }

                            // Format as table
                            var range = worksheet.Range(1, 1, dataGridViewExpired.Rows.Count + 1, dataGridViewExpired.Columns.Count);
                            range.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            range.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                            worksheet.Row(1).Style.Fill.BackgroundColor = XLColor.Red;
                            worksheet.Row(1).Style.Font.Bold = true;
                            worksheet.Row(1).Style.Font.FontColor = XLColor.White;

                            worksheet.Columns().AdjustToContents();
                            workbook.SaveAs(sfd.FileName);
                        }

                        MessageBox.Show("تم تصدير تقرير الدورات المنتهية بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تصدير التقرير: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}