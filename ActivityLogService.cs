using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Net;
using System.Net.NetworkInformation;
using System.Text.Json;
using System.Threading.Tasks;

namespace EmployeeManagementSystem
{
    public static class ActivityLogService
    {
        private static User? _currentUser;
        
        // تعيين المستخدم الحالي
        public static void SetCurrentUser(User user)
        {
            _currentUser = user;
        }

        // تسجيل عملية جديدة
        public static async Task LogActivityAsync(string action, string tableName, int? recordId = null, 
            object? oldValues = null, object? newValues = null, string actionType = "عادي", 
            string priority = "عادي", string notes = null, int? executionTime = null)
        {
            try
            {
                if (_currentUser == null) return;

                var log = new ActivityLog
                {
                    UserId = _currentUser.UserId,
                    Username = _currentUser.Username,
                    FullName = _currentUser.FullName,
                    Action = action,
                    TableName = tableName,
                    RecordId = recordId,
                    OldValues = oldValues != null ? JsonSerializer.Serialize(oldValues) : null,
                    NewValues = newValues != null ? JsonSerializer.Serialize(newValues) : null,
                    ActionDate = DateTime.Now,
                    IpAddress = GetLocalIpAddress(),
                    ComputerName = Environment.MachineName,
                    ActionType = actionType,
                    Priority = priority,
                    Status = "نجح",
                    AdditionalInfo = notes,
                    ApplicationName = "HRMS",
                    SessionId = System.Diagnostics.Process.GetCurrentProcess().SessionId.ToString(),
                    ExecutionTime = executionTime
                };

                await SaveLogAsync(log);
            }
            catch (Exception ex)
            {
                // في حالة فشل تسجيل النشاط، نسجل الخطأ في ملف
                try
                {
                    System.IO.File.AppendAllText("hrms_errors.log", 
                        $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] فشل في تسجيل النشاط: {ex.Message}\n");
                }
                catch { /* تجاهل أخطاء تسجيل الخطأ */ }
            }
        }

        // تسجيل عملية فاشلة
        public static async Task LogFailedActivityAsync(string action, string tableName, string errorMessage, 
            int? recordId = null, string priority = "حرج")
        {
            try
            {
                if (_currentUser == null) return;

                var log = new ActivityLog
                {
                    UserId = _currentUser.UserId,
                    Username = _currentUser.Username,
                    FullName = _currentUser.FullName,
                    Action = action,
                    TableName = tableName,
                    RecordId = recordId,
                    ActionDate = DateTime.Now,
                    IpAddress = GetLocalIpAddress(),
                    ComputerName = Environment.MachineName,
                    ActionType = "خطأ",
                    Priority = priority,
                    Status = "فشل",
                    ErrorMessage = errorMessage,
                    ApplicationName = "HRMS",
                    SessionId = System.Diagnostics.Process.GetCurrentProcess().SessionId.ToString()
                };

                await SaveLogAsync(log);
            }
            catch (Exception ex)
            {
                try
                {
                    System.IO.File.AppendAllText("hrms_errors.log", 
                        $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] فشل في تسجيل النشاط الفاشل: {ex.Message}\n");
                }
                catch { /* تجاهل أخطاء تسجيل الخطأ */ }
            }
        }

        // تسجيل دخول المستخدم
        public static async Task LogLoginAsync(User user, bool success = true, string errorMessage = null)
        {
            try
            {
                var log = new ActivityLog
                {
                    UserId = user.UserId,
                    Username = user.Username,
                    FullName = user.FullName,
                    Action = "تسجيل دخول",
                    TableName = "Users",
                    RecordId = user.UserId,
                    ActionDate = DateTime.Now,
                    IpAddress = GetLocalIpAddress(),
                    ComputerName = Environment.MachineName,
                    ActionType = "تسجيل دخول",
                    Priority = success ? "عادي" : "مهم",
                    Status = success ? "نجح" : "فشل",
                    ErrorMessage = errorMessage,
                    ApplicationName = "HRMS",
                    SessionId = System.Diagnostics.Process.GetCurrentProcess().SessionId.ToString(),
                    AdditionalInfo = success ? "تم تسجيل الدخول بنجاح" : "فشل في تسجيل الدخول"
                };

                await SaveLogAsync(log);
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ في ملف بدلاً من Event Log
                try
                {
                    System.IO.File.AppendAllText("hrms_errors.log", 
                        $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] فشل في تسجيل دخول المستخدم: {ex.Message}\n");
                }
                catch { /* تجاهل أخطاء تسجيل الخطأ */ }
            }
        }

        // تسجيل خروج المستخدم
        public static async Task LogLogoutAsync()
        {
            try
            {
                if (_currentUser == null) return;

                var log = new ActivityLog
                {
                    UserId = _currentUser.UserId,
                    Username = _currentUser.Username,
                    FullName = _currentUser.FullName,
                    Action = "تسجيل خروج",
                    TableName = "Users",
                    RecordId = _currentUser.UserId,
                    ActionDate = DateTime.Now,
                    IpAddress = GetLocalIpAddress(),
                    ComputerName = Environment.MachineName,
                    ActionType = "تسجيل خروج",
                    Priority = "عادي",
                    Status = "نجح",
                    ApplicationName = "HRMS",
                    SessionId = System.Diagnostics.Process.GetCurrentProcess().SessionId.ToString(),
                    AdditionalInfo = "تم تسجيل الخروج بنجاح"
                };

                await SaveLogAsync(log);
            }
            catch (Exception ex)
            {
                try
                {
                    System.IO.File.AppendAllText("hrms_errors.log", 
                        $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] فشل في تسجيل خروج المستخدم: {ex.Message}\n");
                }
                catch { /* تجاهل أخطاء تسجيل الخطأ */ }
            }
        }

        // حفظ السجل في قاعدة البيانات
        private static async Task SaveLogAsync(ActivityLog log)
        {
            try
            {
                
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    
                    var command = new SqlCommand(@"
                        INSERT INTO ActivityLogs (UserId, Username, FullName, Action, TableName, RecordId, 
                                                OldValues, NewValues, ActionDate, IpAddress, ComputerName, 
                                                ApplicationName, ActionType, SessionId, ExecutionTime, Priority, 
                                                ErrorMessage, Status, AdditionalInfo)
                        VALUES (@UserId, @Username, @FullName, @Action, @TableName, @RecordId, 
                                @OldValues, @NewValues, @ActionDate, @IpAddress, @ComputerName, 
                                @ApplicationName, @ActionType, @SessionId, @ExecutionTime, @Priority, 
                                @ErrorMessage, @Status, @AdditionalInfo)", connection);

                    command.Parameters.AddWithValue("@UserId", log.UserId);
                    command.Parameters.AddWithValue("@Username", log.Username ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@FullName", log.FullName ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@Action", log.Action ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@TableName", log.TableName ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@RecordId", log.RecordId ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@OldValues", log.OldValues ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@NewValues", log.NewValues ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ActionDate", log.ActionDate);
                    command.Parameters.AddWithValue("@IpAddress", log.IpAddress ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ComputerName", log.ComputerName ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ApplicationName", log.ApplicationName ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ActionType", log.ActionType ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@SessionId", log.SessionId ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ExecutionTime", log.ExecutionTime ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@Priority", log.Priority ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ErrorMessage", log.ErrorMessage ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@Status", log.Status ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@AdditionalInfo", log.AdditionalInfo ?? (object)DBNull.Value);

                    await command.ExecuteNonQueryAsync();
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل حفظ السجل، نسجل الخطأ في ملف
                try
                {
                    string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] فشل حفظ سجل النشاط: {ex.Message}\n" +
                                     $"المستخدم: {log.Username} - النشاط: {log.Action}\n" +
                                     $"تفاصيل الخطأ: {ex.StackTrace}\n\n";
                    
                    System.IO.File.AppendAllText("hrms_activity_errors.log", logEntry);
                }
                catch { /* تجاهل أخطاء تسجيل الخطأ */ }
            }
        }

        // الحصول على السجلات حسب المستخدم
        public static async Task<List<ActivityLog>> GetUserActivityAsync(int userId, DateTime? fromDate = null, 
            DateTime? toDate = null, string actionType = null)
        {
            var logs = new List<ActivityLog>();
            
            try
            {
                
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    
                    var query = @"SELECT * FROM ActivityLogs WHERE UserId = @UserId";
                    
                    if (fromDate.HasValue)
                        query += " AND ActionDate >= @FromDate";
                    
                    if (toDate.HasValue)
                        query += " AND ActionDate <= @ToDate";
                    
                    if (!string.IsNullOrEmpty(actionType))
                        query += " AND ActionType = @ActionType";
                
                query += " ORDER BY ActionDate DESC";
                
                var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@UserId", userId);
                
                if (fromDate.HasValue)
                    command.Parameters.AddWithValue("@FromDate", fromDate.Value);
                
                if (toDate.HasValue)
                    command.Parameters.AddWithValue("@ToDate", toDate.Value);
                
                if (!string.IsNullOrEmpty(actionType))
                    command.Parameters.AddWithValue("@ActionType", actionType);
                
                using (var reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        logs.Add(new ActivityLog
                        {
                            LogId = reader.GetInt32("LogId"),
                            UserId = reader.GetInt32("UserId"),
                            Username = reader.IsDBNull("Username") ? null : reader.GetString("Username"),
                            FullName = reader.IsDBNull("FullName") ? null : reader.GetString("FullName"),
                            Action = reader.IsDBNull("Action") ? null : reader.GetString("Action"),
                            TableName = reader.IsDBNull("TableName") ? null : reader.GetString("TableName"),
                            RecordId = reader.IsDBNull("RecordId") ? null : reader.GetInt32("RecordId"),
                            OldValues = reader.IsDBNull("OldValues") ? null : reader.GetString("OldValues"),
                            NewValues = reader.IsDBNull("NewValues") ? null : reader.GetString("NewValues"),
                            ActionDate = reader.GetDateTime("ActionDate"),
                            IpAddress = reader.IsDBNull("IpAddress") ? null : reader.GetString("IpAddress"),
                            ComputerName = reader.IsDBNull("ComputerName") ? null : reader.GetString("ComputerName"),
                            ApplicationName = reader.IsDBNull("ApplicationName") ? null : reader.GetString("ApplicationName"),
                            ActionType = reader.IsDBNull("ActionType") ? null : reader.GetString("ActionType"),
                            SessionId = reader.IsDBNull("SessionId") ? null : reader.GetString("SessionId"),
                            ExecutionTime = reader.IsDBNull("ExecutionTime") ? null : reader.GetInt32("ExecutionTime"),
                            Priority = reader.IsDBNull("Priority") ? null : reader.GetString("Priority"),
                            ErrorMessage = reader.IsDBNull("ErrorMessage") ? null : reader.GetString("ErrorMessage"),
                            Status = reader.IsDBNull("Status") ? null : reader.GetString("Status"),
                            AdditionalInfo = reader.IsDBNull("AdditionalInfo") ? null : reader.GetString("AdditionalInfo")
                        });
                    }
                }
            }
            
            return logs;
            }
            catch (Exception ex)
            {
                // في حالة فشل الحصول على السجلات، نسجل الخطأ ونرجع قائمة فارغة
                try
                {
                    System.IO.File.AppendAllText("hrms_activity_errors.log", 
                        $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] فشل الحصول على سجلات النشاط للمستخدم {userId}: {ex.Message}\n");
                }
                catch { /* تجاهل أخطاء تسجيل الخطأ */ }
                
                return new List<ActivityLog>();
            }
        }

        // الحصول على جميع السجلات (للمدير فقط)
        public static async Task<List<ActivityLog>> GetAllActivityAsync(DateTime? fromDate = null, 
            DateTime? toDate = null, string actionType = null, string priority = null)
        {
            var logs = new List<ActivityLog>();
            
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                await connection.OpenAsync();
                
                var query = @"SELECT * FROM ActivityLogs WHERE 1=1";
                
                if (fromDate.HasValue)
                    query += " AND ActionDate >= @FromDate";
                
                if (toDate.HasValue)
                    query += " AND ActionDate <= @ToDate";
                
                if (!string.IsNullOrEmpty(actionType))
                    query += " AND ActionType = @ActionType";
                
                if (!string.IsNullOrEmpty(priority))
                    query += " AND Priority = @Priority";
                
                query += " ORDER BY ActionDate DESC";
                
                var command = new SqlCommand(query, connection);
                
                if (fromDate.HasValue)
                    command.Parameters.AddWithValue("@FromDate", fromDate.Value);
                
                if (toDate.HasValue)
                    command.Parameters.AddWithValue("@ToDate", toDate.Value);
                
                if (!string.IsNullOrEmpty(actionType))
                    command.Parameters.AddWithValue("@ActionType", actionType);
                
                if (!string.IsNullOrEmpty(priority))
                    command.Parameters.AddWithValue("@Priority", priority);
                
                using (var reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        logs.Add(new ActivityLog
                        {
                            LogId = reader.GetInt32("LogId"),
                            UserId = reader.GetInt32("UserId"),
                            Username = reader.IsDBNull("Username") ? null : reader.GetString("Username"),
                            FullName = reader.IsDBNull("FullName") ? null : reader.GetString("FullName"),
                            Action = reader.IsDBNull("Action") ? null : reader.GetString("Action"),
                            TableName = reader.IsDBNull("TableName") ? null : reader.GetString("TableName"),
                            RecordId = reader.IsDBNull("RecordId") ? null : reader.GetInt32("RecordId"),
                            OldValues = reader.IsDBNull("OldValues") ? null : reader.GetString("OldValues"),
                            NewValues = reader.IsDBNull("NewValues") ? null : reader.GetString("NewValues"),
                            ActionDate = reader.GetDateTime("ActionDate"),
                            IpAddress = reader.IsDBNull("IpAddress") ? null : reader.GetString("IpAddress"),
                            ComputerName = reader.IsDBNull("ComputerName") ? null : reader.GetString("ComputerName"),
                            ApplicationName = reader.IsDBNull("ApplicationName") ? null : reader.GetString("ApplicationName"),
                            ActionType = reader.IsDBNull("ActionType") ? null : reader.GetString("ActionType"),
                            SessionId = reader.IsDBNull("SessionId") ? null : reader.GetString("SessionId"),
                            ExecutionTime = reader.IsDBNull("ExecutionTime") ? null : reader.GetInt32("ExecutionTime"),
                            Priority = reader.IsDBNull("Priority") ? null : reader.GetString("Priority"),
                            ErrorMessage = reader.IsDBNull("ErrorMessage") ? null : reader.GetString("ErrorMessage"),
                            Status = reader.IsDBNull("Status") ? null : reader.GetString("Status"),
                            AdditionalInfo = reader.IsDBNull("AdditionalInfo") ? null : reader.GetString("AdditionalInfo")
                        });
                    }
                }
            }
            
            return logs;
        }

        // قياس وقت التنفيذ وتسجيل النشاط
        public static async Task<T> LogTimedActivityAsync<T>(string action, string tableName, 
            Func<Task<T>> operation, int? recordId = null, object? oldValues = null, 
            string actionType = "عادي", string priority = "عادي", string notes = null)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                var result = await operation();
                stopwatch.Stop();
                
                await LogActivityAsync(action, tableName, recordId, oldValues, result, 
                    actionType, priority, notes, (int)stopwatch.ElapsedMilliseconds);
                
                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                await LogFailedActivityAsync(action, tableName, ex.Message, recordId, "حرج");
                throw;
            }
        }

        // الحصول على عنوان IP المحلي
        private static string GetLocalIpAddress()
        {
            try
            {
                var host = Dns.GetHostEntry(Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    {
                        return ip.ToString();
                    }
                }
                return "127.0.0.1";
            }
            catch
            {
                return "غير معروف";
            }
        }

        // حذف السجلات القديمة (للصيانة)
        public static async Task CleanupOldLogsAsync(int daysToKeep = 90)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                await connection.OpenAsync();
                
                var command = new SqlCommand(@"
                    DELETE FROM ActivityLogs 
                    WHERE ActionDate < @CutoffDate", connection);
                
                command.Parameters.AddWithValue("@CutoffDate", DateTime.Now.AddDays(-daysToKeep));
                
                await command.ExecuteNonQueryAsync();
            }
        }

        // احصائيات النشاط
        public static async Task<Dictionary<string, int>> GetActivityStatsAsync(DateTime? fromDate = null, 
            DateTime? toDate = null)
        {
            var stats = new Dictionary<string, int>();
            
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                await connection.OpenAsync();
                
                var query = @"
                    SELECT ActionType, COUNT(*) as Count 
                    FROM ActivityLogs 
                    WHERE 1=1";
                
                if (fromDate.HasValue)
                    query += " AND ActionDate >= @FromDate";
                
                if (toDate.HasValue)
                    query += " AND ActionDate <= @ToDate";
                
                query += " GROUP BY ActionType";
                
                var command = new SqlCommand(query, connection);
                
                if (fromDate.HasValue)
                    command.Parameters.AddWithValue("@FromDate", fromDate.Value);
                
                if (toDate.HasValue)
                    command.Parameters.AddWithValue("@ToDate", toDate.Value);
                
                using (var reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        var actionType = reader.GetString("ActionType");
                        var count = reader.GetInt32("Count");
                        stats[actionType] = count;
                    }
                }
            }
            
            return stats;
        }

        /// <summary>
        /// الحصول على سجل نشاط بالمعرف
        /// </summary>
        public static async Task<ActivityLog?> GetActivityByIdAsync(int logId)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    using (var command = new SqlCommand(
                        "SELECT * FROM ActivityLogs WHERE LogId = @LogId", connection))
                    {
                        command.Parameters.AddWithValue("@LogId", logId);
                        
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                return new ActivityLog
                                {
                                    LogId = reader.GetInt32("LogId"),
                                    UserId = reader.GetInt32("UserId"),
                                    Username = reader.GetString("Username"),
                                    FullName = reader.GetString("FullName"),
                                    Action = reader.GetString("Action"),
                                    TableName = reader.GetString("TableName"),
                                    RecordId = reader.IsDBNull("RecordId") ? null : reader.GetInt32("RecordId"),
                                    OldValues = reader.IsDBNull("OldValues") ? null : reader.GetString("OldValues"),
                                    NewValues = reader.IsDBNull("NewValues") ? null : reader.GetString("NewValues"),
                                    ActionDate = reader.GetDateTime("ActionDate"),
                                    IpAddress = reader.IsDBNull("IpAddress") ? null : reader.GetString("IpAddress"),
                                    ComputerName = reader.IsDBNull("ComputerName") ? null : reader.GetString("ComputerName"),
                                    ActionType = reader.GetString("ActionType"),
                                    Priority = reader.GetString("Priority"),
                                    Status = reader.GetString("Status"),
                                    AdditionalInfo = reader.IsDBNull("AdditionalInfo") ? null : reader.GetString("AdditionalInfo"),
                                    ErrorMessage = reader.IsDBNull("ErrorMessage") ? null : reader.GetString("ErrorMessage")
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetActivityByIdAsync: {ex.Message}");
            }
            
            return null;
        }


    }
}