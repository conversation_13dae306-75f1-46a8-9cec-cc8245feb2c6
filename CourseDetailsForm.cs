using System;
using System.Data;
using System.Windows.Forms;
using System.Drawing;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace EmployeeManagementSystem
{
    public partial class CourseDetailsForm : Form
    {
        private int? selectedCourseId = null;

        public CourseDetailsForm()
        {
            InitializeComponent();
            LoadCourses();
            ClearForm();
            ThemeManager.ApplyThemeToForm(this);
        }

        private void LoadCourses()
        {
            try
            {
                var courses = DatabaseHelper.GetAllCourses();
                cmbCourse.Items.Clear();
                foreach (DataRow row in courses.Rows)
                {
                    cmbCourse.Items.Add($"{row["المعرف"]} - {row["رقم الدورة"]} - {row["نوع الدورة"]}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تحميل الدورات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearForm()
        {
            selectedCourseId = null;
            cmbCourse.SelectedIndex = -1;
            cmbStatus.SelectedIndex = -1;
            txtLocation.Clear();
            numMaxParticipants.Value = 0;
            numCurrentParticipants.Value = 0;
            txtDescription.Clear();
            txtPrerequisites.Clear();
            chkCertificateIssued.Checked = false;
            numCost.Value = 0;
            cmbPriority.SelectedIndex = -1;
            chkNotificationSent.Checked = false;
            txtCreatedBy.Text = Environment.UserName;
            dtCreatedDate.Value = DateTime.Now;
            txtUpdatedBy.Text = Environment.UserName;
            dtUpdatedDate.Value = DateTime.Now;
            chkIsActive.Checked = true;
            btnUpdate.Enabled = false;
        }

        private bool ValidateForm()
        {
            if (cmbCourse.SelectedIndex == -1)
            {
                MessageBox.Show("الرجاء اختيار الدورة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (cmbStatus.SelectedIndex == -1)
            {
                MessageBox.Show("الرجاء اختيار حالة الدورة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtLocation.Text))
            {
                MessageBox.Show("الرجاء إدخال موقع الدورة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (numCurrentParticipants.Value > numMaxParticipants.Value)
            {
                MessageBox.Show("عدد المشاركين الحاليين لا يمكن أن يكون أكبر من العدد الأقصى", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private Course GetFormData()
        {
            return new Course
            {
                CourseId = GetSelectedCourseId(),
                Status = cmbStatus.Text,
                Location = txtLocation.Text,
                MaxParticipants = (int)numMaxParticipants.Value,
                CurrentParticipants = (int)numCurrentParticipants.Value,
                Description = txtDescription.Text,
                Prerequisites = txtPrerequisites.Text,
                CertificateIssued = chkCertificateIssued.Checked,
                Cost = numCost.Value,
                Priority = cmbPriority.Text,
                NotificationSent = chkNotificationSent.Checked,
                CreatedBy = txtCreatedBy.Text,
                CreatedDate = dtCreatedDate.Value,
                UpdatedBy = txtUpdatedBy.Text,
                UpdatedDate = dtUpdatedDate.Value,
                IsActive = chkIsActive.Checked
            };
        }

        private int GetSelectedCourseId()
        {
            if (cmbCourse.SelectedIndex >= 0)
            {
                string selectedText = cmbCourse.Text;
                string[] parts = selectedText.Split('-');
                if (parts.Length > 0 && int.TryParse(parts[0].Trim(), out int courseId))
                {
                    return courseId;
                }
            }
            return 0;
        }

        private async void btnUpdate_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;

                var course = GetFormData();
                course.UpdatedBy = Environment.UserName;
                course.UpdatedDate = DateTime.Now;

                await Task.Run(() => DatabaseHelper.UpdateCourseDetails(course));

                MessageBox.Show("تم تحديث تفاصيل الدورة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تحديث تفاصيل الدورة: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private async void cmbCourse_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbCourse.SelectedIndex >= 0)
            {
                try
                {
                    int courseId = GetSelectedCourseId();
                    var courseDetails = await Task.Run(() => DatabaseHelper.GetCourseDetails(courseId));

                    if (courseDetails != null)
                    {
                        selectedCourseId = courseId;
                        cmbStatus.Text = courseDetails.Status;
                        txtLocation.Text = courseDetails.Location;
                        numMaxParticipants.Value = courseDetails.MaxParticipants;
                        numCurrentParticipants.Value = courseDetails.CurrentParticipants;
                        txtDescription.Text = courseDetails.Description;
                        txtPrerequisites.Text = courseDetails.Prerequisites;
                        chkCertificateIssued.Checked = courseDetails.CertificateIssued;
                        numCost.Value = courseDetails.Cost;
                        cmbPriority.Text = courseDetails.Priority;
                        chkNotificationSent.Checked = courseDetails.NotificationSent;
                        txtCreatedBy.Text = courseDetails.CreatedBy;
                        dtCreatedDate.Value = courseDetails.CreatedDate;
                        txtUpdatedBy.Text = courseDetails.UpdatedBy;
                        dtUpdatedDate.Value = courseDetails.UpdatedDate;
                        chkIsActive.Checked = courseDetails.IsActive;

                        btnUpdate.Enabled = true;
                    }
                    else
                    {
                        // إذا لم توجد تفاصيل، قم بتعيين القيم الافتراضية
                        selectedCourseId = courseId;
                        cmbStatus.SelectedIndex = 0;
                        txtLocation.Clear();
                        numMaxParticipants.Value = 20;
                        numCurrentParticipants.Value = 0;
                        txtDescription.Clear();
                        txtPrerequisites.Clear();
                        chkCertificateIssued.Checked = false;
                        numCost.Value = 0;
                        cmbPriority.SelectedIndex = 1; // عادي
                        chkNotificationSent.Checked = false;
                        txtCreatedBy.Text = Environment.UserName;
                        dtCreatedDate.Value = DateTime.Now;
                        txtUpdatedBy.Text = Environment.UserName;
                        dtUpdatedDate.Value = DateTime.Now;
                        chkIsActive.Checked = true;

                        btnUpdate.Enabled = true;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show("حدث خطأ أثناء تحميل تفاصيل الدورة: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async void btnSendNotification_Click(object sender, EventArgs e)
        {
            try
            {
                if (selectedCourseId == null)
                {
                    MessageBox.Show("الرجاء اختيار الدورة أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show("هل تريد إرسال إشعار لجميع المشاركين في هذه الدورة؟", 
                    "تأكيد الإرسال", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // إرسال الإشعارات
                    var enrolledEmployees = DatabaseHelper.GetCourseEnrolledEmployees(selectedCourseId.Value);
                    int sentCount = 0;

                    foreach (DataRow employee in enrolledEmployees.Rows)
                    {
                        var notification = new CourseNotification
                        {
                            CourseId = selectedCourseId.Value,
                            EmployeeName = employee["اسم الموظف"].ToString(),
                            NotificationType = "تذكير",
                            Message = $"تذكير بالدورة التدريبية في {txtLocation.Text}",
                            SentDate = DateTime.Now,
                            IsRead = false,
                            Priority = cmbPriority.Text ?? "عادي"
                        };

                        await Task.Run(() => DatabaseHelper.AddCourseNotification(notification));
                        sentCount++;
                    }

                    // تحديث حالة الإشعار
                    chkNotificationSent.Checked = true;
                    //await btnUpdate_Click(sender, e);

                    MessageBox.Show($"تم إرسال {sentCount} إشعار بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء إرسال الإشعارات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}