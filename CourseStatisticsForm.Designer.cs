namespace EmployeeManagementSystem
{
    partial class CourseStatisticsForm
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            groupBoxStats = new GroupBox();
            lblAttendanceRateValue = new Label();
            lblAttendanceRate = new Label();
            lblAverageRatingValue = new Label();
            lblAverageRating = new Label();
            lblScheduledCoursesValue = new Label();
            lblScheduledCourses = new Label();
            lblCompletedCoursesValue = new Label();
            lblCompletedCourses = new Label();
            lblActiveCoursesValue = new Label();
            lblActiveCourses = new Label();
            lblTotalCoursesValue = new Label();
            lblTotalCourses = new Label();
            tabControl1 = new TabControl();
            tabPageComprehensive = new TabPage();
            dataGridViewComprehensive = new DataGridView();
            panel1 = new Panel();
            btnExportComprehensive = new Button();
            tabPageExpired = new TabPage();
            dataGridViewExpired = new DataGridView();
            panel2 = new Panel();
            btnUpdateExpiredCourses = new Button();
            btnExportExpired = new Button();
            panel3 = new Panel();
            btnSendNotifications = new Button();
            btnRefresh = new Button();
            toolTip1 = new ToolTip(components);
            groupBoxStats.SuspendLayout();
            tabControl1.SuspendLayout();
            tabPageComprehensive.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridViewComprehensive).BeginInit();
            panel1.SuspendLayout();
            tabPageExpired.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridViewExpired).BeginInit();
            panel2.SuspendLayout();
            panel3.SuspendLayout();
            SuspendLayout();
            // 
            // groupBoxStats
            // 
            groupBoxStats.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxStats.Controls.Add(lblAttendanceRateValue);
            groupBoxStats.Controls.Add(lblAttendanceRate);
            groupBoxStats.Controls.Add(lblAverageRatingValue);
            groupBoxStats.Controls.Add(lblAverageRating);
            groupBoxStats.Controls.Add(lblScheduledCoursesValue);
            groupBoxStats.Controls.Add(lblScheduledCourses);
            groupBoxStats.Controls.Add(lblCompletedCoursesValue);
            groupBoxStats.Controls.Add(lblCompletedCourses);
            groupBoxStats.Controls.Add(lblActiveCoursesValue);
            groupBoxStats.Controls.Add(lblActiveCourses);
            groupBoxStats.Controls.Add(lblTotalCoursesValue);
            groupBoxStats.Controls.Add(lblTotalCourses);
            groupBoxStats.Font = new Font("Cairo", 12F, FontStyle.Bold);
            groupBoxStats.Location = new Point(12, 12);
            groupBoxStats.Name = "groupBoxStats";
            groupBoxStats.Size = new Size(1200, 120);
            groupBoxStats.TabIndex = 0;
            groupBoxStats.TabStop = false;
            groupBoxStats.Text = "إحصائيات الدورات التدريبية";
            // 
            // lblAttendanceRateValue
            // 
            lblAttendanceRateValue.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblAttendanceRateValue.Font = new Font("Cairo", 14F, FontStyle.Bold);
            lblAttendanceRateValue.ForeColor = Color.FromArgb(155, 89, 182);
            lblAttendanceRateValue.Location = new Point(20, 80);
            lblAttendanceRateValue.Name = "lblAttendanceRateValue";
            lblAttendanceRateValue.Size = new Size(100, 30);
            lblAttendanceRateValue.TabIndex = 11;
            lblAttendanceRateValue.Text = "0%";
            lblAttendanceRateValue.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblAttendanceRate
            // 
            lblAttendanceRate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblAttendanceRate.Font = new Font("Cairo", 10F);
            lblAttendanceRate.Location = new Point(20, 50);
            lblAttendanceRate.Name = "lblAttendanceRate";
            lblAttendanceRate.Size = new Size(100, 30);
            lblAttendanceRate.TabIndex = 10;
            lblAttendanceRate.Text = "معدل الحضور";
            lblAttendanceRate.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblAverageRatingValue
            // 
            lblAverageRatingValue.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblAverageRatingValue.Font = new Font("Cairo", 14F, FontStyle.Bold);
            lblAverageRatingValue.ForeColor = Color.FromArgb(230, 126, 34);
            lblAverageRatingValue.Location = new Point(220, 80);
            lblAverageRatingValue.Name = "lblAverageRatingValue";
            lblAverageRatingValue.Size = new Size(100, 30);
            lblAverageRatingValue.TabIndex = 9;
            lblAverageRatingValue.Text = "0.0";
            lblAverageRatingValue.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblAverageRating
            // 
            lblAverageRating.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblAverageRating.Font = new Font("Cairo", 10F);
            lblAverageRating.Location = new Point(220, 50);
            lblAverageRating.Name = "lblAverageRating";
            lblAverageRating.Size = new Size(100, 30);
            lblAverageRating.TabIndex = 8;
            lblAverageRating.Text = "متوسط التقييم";
            lblAverageRating.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblScheduledCoursesValue
            // 
            lblScheduledCoursesValue.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblScheduledCoursesValue.Font = new Font("Cairo", 14F, FontStyle.Bold);
            lblScheduledCoursesValue.ForeColor = Color.FromArgb(52, 152, 219);
            lblScheduledCoursesValue.Location = new Point(420, 80);
            lblScheduledCoursesValue.Name = "lblScheduledCoursesValue";
            lblScheduledCoursesValue.Size = new Size(100, 30);
            lblScheduledCoursesValue.TabIndex = 7;
            lblScheduledCoursesValue.Text = "0";
            lblScheduledCoursesValue.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblScheduledCourses
            // 
            lblScheduledCourses.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblScheduledCourses.Font = new Font("Cairo", 10F);
            lblScheduledCourses.Location = new Point(420, 50);
            lblScheduledCourses.Name = "lblScheduledCourses";
            lblScheduledCourses.Size = new Size(100, 30);
            lblScheduledCourses.TabIndex = 6;
            lblScheduledCourses.Text = "دورات مجدولة";
            lblScheduledCourses.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblCompletedCoursesValue
            // 
            lblCompletedCoursesValue.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblCompletedCoursesValue.Font = new Font("Cairo", 14F, FontStyle.Bold);
            lblCompletedCoursesValue.ForeColor = Color.FromArgb(46, 204, 113);
            lblCompletedCoursesValue.Location = new Point(620, 80);
            lblCompletedCoursesValue.Name = "lblCompletedCoursesValue";
            lblCompletedCoursesValue.Size = new Size(100, 30);
            lblCompletedCoursesValue.TabIndex = 5;
            lblCompletedCoursesValue.Text = "0";
            lblCompletedCoursesValue.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblCompletedCourses
            // 
            lblCompletedCourses.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblCompletedCourses.Font = new Font("Cairo", 10F);
            lblCompletedCourses.Location = new Point(620, 50);
            lblCompletedCourses.Name = "lblCompletedCourses";
            lblCompletedCourses.Size = new Size(100, 30);
            lblCompletedCourses.TabIndex = 4;
            lblCompletedCourses.Text = "دورات مكتملة";
            lblCompletedCourses.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblActiveCoursesValue
            // 
            lblActiveCoursesValue.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblActiveCoursesValue.Font = new Font("Cairo", 14F, FontStyle.Bold);
            lblActiveCoursesValue.ForeColor = Color.FromArgb(231, 76, 60);
            lblActiveCoursesValue.Location = new Point(820, 80);
            lblActiveCoursesValue.Name = "lblActiveCoursesValue";
            lblActiveCoursesValue.Size = new Size(100, 30);
            lblActiveCoursesValue.TabIndex = 3;
            lblActiveCoursesValue.Text = "0";
            lblActiveCoursesValue.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblActiveCourses
            // 
            lblActiveCourses.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblActiveCourses.Font = new Font("Cairo", 10F);
            lblActiveCourses.Location = new Point(820, 50);
            lblActiveCourses.Name = "lblActiveCourses";
            lblActiveCourses.Size = new Size(100, 30);
            lblActiveCourses.TabIndex = 2;
            lblActiveCourses.Text = "دورات نشطة";
            lblActiveCourses.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblTotalCoursesValue
            // 
            lblTotalCoursesValue.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblTotalCoursesValue.Font = new Font("Cairo", 16F, FontStyle.Bold);
            lblTotalCoursesValue.ForeColor = Color.FromArgb(52, 73, 94);
            lblTotalCoursesValue.Location = new Point(1020, 80);
            lblTotalCoursesValue.Name = "lblTotalCoursesValue";
            lblTotalCoursesValue.Size = new Size(120, 30);
            lblTotalCoursesValue.TabIndex = 1;
            lblTotalCoursesValue.Text = "0";
            lblTotalCoursesValue.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblTotalCourses
            // 
            lblTotalCourses.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblTotalCourses.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblTotalCourses.Location = new Point(1020, 50);
            lblTotalCourses.Name = "lblTotalCourses";
            lblTotalCourses.Size = new Size(120, 30);
            lblTotalCourses.TabIndex = 0;
            lblTotalCourses.Text = "إجمالي الدورات";
            lblTotalCourses.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // tabControl1
            // 
            tabControl1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            tabControl1.Controls.Add(tabPageComprehensive);
            tabControl1.Controls.Add(tabPageExpired);
            tabControl1.Font = new Font("Cairo", 12F, FontStyle.Bold);
            tabControl1.Location = new Point(12, 138);
            tabControl1.Name = "tabControl1";
            tabControl1.RightToLeft = RightToLeft.Yes;
            tabControl1.RightToLeftLayout = true;
            tabControl1.SelectedIndex = 0;
            tabControl1.Size = new Size(1200, 450);
            tabControl1.TabIndex = 1;
            // 
            // tabPageComprehensive
            // 
            tabPageComprehensive.Controls.Add(dataGridViewComprehensive);
            tabPageComprehensive.Controls.Add(panel1);
            tabPageComprehensive.Location = new Point(4, 39);
            tabPageComprehensive.Name = "tabPageComprehensive";
            tabPageComprehensive.Padding = new Padding(3);
            tabPageComprehensive.Size = new Size(1192, 407);
            tabPageComprehensive.TabIndex = 0;
            tabPageComprehensive.Text = "التقرير الشامل";
            tabPageComprehensive.UseVisualStyleBackColor = true;
            // 
            // dataGridViewComprehensive
            // 
            dataGridViewComprehensive.AllowUserToAddRows = false;
            dataGridViewComprehensive.AllowUserToDeleteRows = false;
            dataGridViewComprehensive.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dataGridViewComprehensive.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridViewComprehensive.Location = new Point(6, 6);
            dataGridViewComprehensive.Name = "dataGridViewComprehensive";
            dataGridViewComprehensive.ReadOnly = true;
            dataGridViewComprehensive.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridViewComprehensive.Size = new Size(1180, 345);
            dataGridViewComprehensive.TabIndex = 0;
            // 
            // panel1
            // 
            panel1.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            panel1.Controls.Add(btnExportComprehensive);
            panel1.Location = new Point(6, 357);
            panel1.Name = "panel1";
            panel1.Size = new Size(1180, 44);
            panel1.TabIndex = 1;
            // 
            // btnExportComprehensive
            // 
            btnExportComprehensive.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnExportComprehensive.BackColor = Color.FromArgb(46, 204, 113);
            btnExportComprehensive.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnExportComprehensive.ForeColor = Color.White;
            btnExportComprehensive.Image = Properties.Resources.microsoft_excel_2019_32px;
            btnExportComprehensive.ImageAlign = ContentAlignment.MiddleRight;
            btnExportComprehensive.Location = new Point(1050, 3);
            btnExportComprehensive.Name = "btnExportComprehensive";
            btnExportComprehensive.Size = new Size(127, 38);
            btnExportComprehensive.TabIndex = 0;
            btnExportComprehensive.Text = "تصدير Excel";
            btnExportComprehensive.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnExportComprehensive, "تصدير التقرير الشامل إلى Excel");
            btnExportComprehensive.UseVisualStyleBackColor = false;
            btnExportComprehensive.Click += btnExportComprehensive_Click;
            // 
            // tabPageExpired
            // 
            tabPageExpired.Controls.Add(dataGridViewExpired);
            tabPageExpired.Controls.Add(panel2);
            tabPageExpired.Location = new Point(4, 39);
            tabPageExpired.Name = "tabPageExpired";
            tabPageExpired.Padding = new Padding(3);
            tabPageExpired.Size = new Size(1192, 407);
            tabPageExpired.TabIndex = 1;
            tabPageExpired.Text = "الدورات المنتهية";
            tabPageExpired.UseVisualStyleBackColor = true;
            // 
            // dataGridViewExpired
            // 
            dataGridViewExpired.AllowUserToAddRows = false;
            dataGridViewExpired.AllowUserToDeleteRows = false;
            dataGridViewExpired.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dataGridViewExpired.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridViewExpired.Location = new Point(6, 6);
            dataGridViewExpired.Name = "dataGridViewExpired";
            dataGridViewExpired.ReadOnly = true;
            dataGridViewExpired.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridViewExpired.Size = new Size(1180, 345);
            dataGridViewExpired.TabIndex = 0;
            // 
            // panel2
            // 
            panel2.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            panel2.Controls.Add(btnUpdateExpiredCourses);
            panel2.Controls.Add(btnExportExpired);
            panel2.Location = new Point(6, 357);
            panel2.Name = "panel2";
            panel2.Size = new Size(1180, 44);
            panel2.TabIndex = 1;
            // 
            // btnUpdateExpiredCourses
            // 
            btnUpdateExpiredCourses.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnUpdateExpiredCourses.BackColor = Color.FromArgb(52, 152, 219);
            btnUpdateExpiredCourses.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnUpdateExpiredCourses.ForeColor = Color.White;
            btnUpdateExpiredCourses.ImageAlign = ContentAlignment.MiddleRight;
            btnUpdateExpiredCourses.Location = new Point(917, 3);
            btnUpdateExpiredCourses.Name = "btnUpdateExpiredCourses";
            btnUpdateExpiredCourses.Size = new Size(127, 38);
            btnUpdateExpiredCourses.TabIndex = 1;
            btnUpdateExpiredCourses.Text = "تحديث الحالة";
            btnUpdateExpiredCourses.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnUpdateExpiredCourses, "تحديث حالة الدورات المنتهية تلقائياً");
            btnUpdateExpiredCourses.UseVisualStyleBackColor = false;
            btnUpdateExpiredCourses.Click += btnUpdateExpiredCourses_Click;
            // 
            // btnExportExpired
            // 
            btnExportExpired.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnExportExpired.BackColor = Color.FromArgb(231, 76, 60);
            btnExportExpired.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnExportExpired.ForeColor = Color.White;
            btnExportExpired.Image = Properties.Resources.microsoft_excel_2019_32px;
            btnExportExpired.ImageAlign = ContentAlignment.MiddleRight;
            btnExportExpired.Location = new Point(1050, 3);
            btnExportExpired.Name = "btnExportExpired";
            btnExportExpired.Size = new Size(127, 38);
            btnExportExpired.TabIndex = 0;
            btnExportExpired.Text = "تصدير Excel";
            btnExportExpired.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnExportExpired, "تصدير الدورات المنتهية إلى Excel");
            btnExportExpired.UseVisualStyleBackColor = false;
            btnExportExpired.Click += btnExportExpired_Click;
            // 
            // panel3
            // 
            panel3.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            panel3.Controls.Add(btnSendNotifications);
            panel3.Controls.Add(btnRefresh);
            panel3.Location = new Point(12, 594);
            panel3.Name = "panel3";
            panel3.Size = new Size(1200, 50);
            panel3.TabIndex = 2;
            // 
            // btnSendNotifications
            // 
            btnSendNotifications.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnSendNotifications.BackColor = Color.FromArgb(155, 89, 182);
            btnSendNotifications.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnSendNotifications.ForeColor = Color.White;
            btnSendNotifications.Image = Properties.Resources.notification1_32px;
            btnSendNotifications.ImageAlign = ContentAlignment.MiddleRight;
            btnSendNotifications.Location = new Point(1020, 6);
            btnSendNotifications.Name = "btnSendNotifications";
            btnSendNotifications.Size = new Size(170, 38);
            btnSendNotifications.TabIndex = 1;
            btnSendNotifications.Text = "إرسال إشعارات تلقائية";
            btnSendNotifications.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnSendNotifications, "إرسال إشعارات للدورات التي تبدأ قريباً");
            btnSendNotifications.UseVisualStyleBackColor = false;
            btnSendNotifications.Click += btnSendNotifications_Click;
            // 
            // btnRefresh
            // 
            btnRefresh.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            btnRefresh.BackColor = Color.FromArgb(52, 73, 94);
            btnRefresh.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnRefresh.ForeColor = Color.White;
            btnRefresh.Image = Properties.Resources.update_32px;
            btnRefresh.ImageAlign = ContentAlignment.MiddleRight;
            btnRefresh.Location = new Point(10, 6);
            btnRefresh.Name = "btnRefresh";
            btnRefresh.Size = new Size(120, 38);
            btnRefresh.TabIndex = 0;
            btnRefresh.Text = "تحديث";
            btnRefresh.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnRefresh, "تحديث الإحصائيات والبيانات");
            btnRefresh.UseVisualStyleBackColor = false;
            btnRefresh.Click += btnRefresh_Click;
            // 
            // CourseStatisticsForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1224, 656);
            Controls.Add(panel3);
            Controls.Add(tabControl1);
            Controls.Add(groupBoxStats);
            Name = "CourseStatisticsForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "إحصائيات وتقارير الدورات التدريبية";
            groupBoxStats.ResumeLayout(false);
            tabControl1.ResumeLayout(false);
            tabPageComprehensive.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dataGridViewComprehensive).EndInit();
            panel1.ResumeLayout(false);
            tabPageExpired.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)dataGridViewExpired).EndInit();
            panel2.ResumeLayout(false);
            panel3.ResumeLayout(false);
            ResumeLayout(false);
        }

        private GroupBox groupBoxStats;
        private Label lblTotalCourses;
        private Label lblTotalCoursesValue;
        private Label lblActiveCourses;
        private Label lblActiveCoursesValue;
        private Label lblCompletedCourses;
        private Label lblCompletedCoursesValue;
        private Label lblScheduledCourses;
        private Label lblScheduledCoursesValue;
        private Label lblAverageRating;
        private Label lblAverageRatingValue;
        private Label lblAttendanceRate;
        private Label lblAttendanceRateValue;
        private TabControl tabControl1;
        private TabPage tabPageComprehensive;
        private DataGridView dataGridViewComprehensive;
        private Panel panel1;
        private Button btnExportComprehensive;
        private TabPage tabPageExpired;
        private DataGridView dataGridViewExpired;
        private Panel panel2;
        private Button btnExportExpired;
        private Button btnUpdateExpiredCourses;
        private Panel panel3;
        private Button btnRefresh;
        private Button btnSendNotifications;
        private ToolTip toolTip1;
    }
}