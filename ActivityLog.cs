using System;

namespace EmployeeManagementSystem
{
    public class ActivityLog
    {
        public int LogId { get; set; }
        public int UserId { get; set; }
        public string Username { get; set; }
        public string FullName { get; set; }
        public string Action { get; set; }  // نوع العملية مثل "إضافة موظف" أو "تحديث بيانات"
        public string TableName { get; set; }  // اسم الجدول المتأثر
        public int? RecordId { get; set; }  // معرف السجل المتأثر
        public string? OldValues { get; set; }  // القيم القديمة (JSON)
        public string? NewValues { get; set; }  // القيم الجديدة (JSON)
        public DateTime ActionDate { get; set; }
        public string? IpAddress { get; set; }
        public string? ComputerName { get; set; }
        public string? ApplicationName { get; set; }  // اسم التطبيق
        public string? SessionId { get; set; }  // معرف الجلسة
        public int? ExecutionTime { get; set; }  // وقت التنفيذ بالميلي ثانية
        
        // تصنيف العملية
        public string ActionType { get; set; }  // "إضافة", "تحديث", "حذف", "استعلام", "تسجيل دخول", "تسجيل خروج"
        
        // درجة الخطورة
        public string Priority { get; set; }  // "عادي", "مهم", "حرج"
        
        // حالة العملية
        public string Status { get; set; }  // "نجح", "فشل", "تحذير"
        
        // تفاصيل إضافية
        public string? ErrorMessage { get; set; }  // رسالة الخطأ في حالة الفشل
        public string? AdditionalInfo { get; set; }  // معلومات إضافية
    }
}