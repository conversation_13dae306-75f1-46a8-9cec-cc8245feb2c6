using System;
using System.Windows.Forms;
using System.Data;
using System.Data.SqlClient;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.IO;
using System.Diagnostics;
using ClosedXML.Excel;
using EmployeeManagementSystem.LoadingGui;
using Microsoft.VisualBasic;

namespace EmployeeManagementSystem
{
    public partial class VacationForm : Form
    {
        private int? selectedVacationId = null;

        private void chkSelectAll_CheckedChanged(object sender, EventArgs e)
        {
            // تحديد أو إلغاء تحديد جميع الصفوف في DataGridView
            foreach (DataGridViewRow row in dataGridView1.Rows)
            {
                row.Selected = chkSelectAll.Checked;
            }
        }

        public VacationForm()
        {
            InitializeComponent();
            // Update any missing EmployeeIds
            UpdateVacations.UpdateVacationEmployeeIds();
            LoadVacations(null);
            LoadVacations();
            InitializePlot();
            LoadEmployeeNames();
            
            // تسجيل فتح النموذج
            _ = Task.Run(async () => await ActivityLogHelper.LogFormAccessAsync("فتح نموذج الإجازات"));
            
            // تسجيل إغلاق النموذج عند الإغلاق
            this.FormClosing += async (sender, e) =>
            {
                await ActivityLogHelper.LogFormAccessAsync("إغلاق نموذج الإجازات");
            };

            // تهيئة قائمة الأشهر
            cmbMonthFilter.Items.AddRange(new string[] {
                "كل الأشهر",
                "يناير",
                "فبراير",
                "مارس",
                "إبريل",
                "مايو",
                "يونيو",
                "يوليو",
                "أغسطس",
                "سبتمبر",
                "أكتوبر",
                "نوفمبر",
                "ديسمبر"
            });
            cmbMonthFilter.SelectedIndex = 0;
            cmbVacationType.Items.AddRange(new string[] {
                "إجازة مساعدة",
                "إجازة اعتيادية",
                "إجازة مرضية",
                "إجازة طارئة",
                "إجازة بدون راتب",
                "إجازة أمومة",
                "أخرى"
            });

            plotTypeComboBox.SelectedIndex = 0;

            // تطبيق الثيم المحفوظ
            string theme = "default";
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT TOP 1 Theme FROM Settings";
                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            theme = reader["Theme"]?.ToString() ?? "default";
                        }
                    }
                }
            }
            catch
            {
                theme = "default";
            }

            ThemeManager.ApplyThemeToForm(this);

        }

        private void InitializePlot()
        {
            vacationsPlot.Plot.Style(figureBackground: Color.White, dataBackground: Color.White);
            vacationsPlot.Plot.XAxis.TickLabelStyle(fontSize: 12);
            vacationsPlot.Plot.YAxis.TickLabelStyle(fontSize: 12);
            UpdatePlot();
        }

        private void UpdatePlot()
        {
            vacationsPlot.Plot.Clear();

            var vacationsData = DatabaseHelper.GetAllVacations();
            if (vacationsData.Rows.Count == 0) return;

            switch (plotTypeComboBox.SelectedIndex)
            {
                case 0: // رسم بياني شريطي
                    CreateBarChart(vacationsData);
                    break;
                case 1: // رسم بياني دائري
                    CreatePieChart(vacationsData);
                    break;
                case 2: // رسم بياني خطي
                    CreateLineChart(vacationsData);
                    break;
            }

            vacationsPlot.Refresh();
        }

        private void CreateBarChart(DataTable vacationsData)
        {
            var types = new Dictionary<string, int>();
            foreach (DataRow row in vacationsData.Rows)
            {
                string type = row["VacationType"].ToString() ?? "غير محدد";
                if (!types.ContainsKey(type))
                    types[type] = 0;
                types[type]++;
            }

            double[] values = types.Values.Select(x => (double)x).ToArray();
            string[] labels = types.Keys.ToArray();

            var bar = vacationsPlot.Plot.AddBar(values);
            vacationsPlot.Plot.XTicks(Enumerable.Range(0, labels.Length).Select(x => (double)x).ToArray(), labels);
            vacationsPlot.Plot.Title("توزيع الإجازات حسب النوع", size: 16);
            vacationsPlot.Plot.XLabel("نوع الإجازة");
            vacationsPlot.Plot.YLabel("عدد الإجازات");
        }

        private void CreatePieChart(DataTable vacationsData)
        {
            var types = new Dictionary<string, int>();
            foreach (DataRow row in vacationsData.Rows)
            {
                string type = row["VacationType"].ToString() ?? "غير محدد";
                if (!types.ContainsKey(type))
                    types[type] = 0;
                types[type]++;
            }

            double[] values = types.Values.Select(x => (double)x).ToArray();
            string[] labels = types.Keys.ToArray();

            var pie = vacationsPlot.Plot.AddPie(values);
            pie.SliceLabels = labels;
            pie.ShowValues = true;
            pie.ShowPercentages = true;
            vacationsPlot.Plot.Title("النسب المئوية لأنواع الإجازات", size: 16);
        }

        private void CreateLineChart(DataTable vacationsData)
        {
            var monthlyCount = new Dictionary<DateTime, int>();
            var currentDate = DateTime.Now.AddMonths(-11);

            // تهيئة الأشهر الـ 12 الماضية
            for (int i = 0; i < 12; i++)
            {
                monthlyCount[new DateTime(currentDate.Year, currentDate.Month, 1)] = 0;
                currentDate = currentDate.AddMonths(1);
            }

            // حساب عدد الإجازات لكل شهر
            foreach (DataRow row in vacationsData.Rows)
            {
                if (DateTime.TryParse(row["StartDate"].ToString(), out DateTime startDate))
                {
                    var key = new DateTime(startDate.Year, startDate.Month, 1);
                    if (monthlyCount.ContainsKey(key))
                        monthlyCount[key]++;
                }
            }

            var sortedData = monthlyCount.OrderBy(x => x.Key).ToList();
            double[] xValues = sortedData.Select(x => x.Key.ToOADate()).ToArray();
            double[] yValues = sortedData.Select(x => (double)x.Value).ToArray();

            var line = vacationsPlot.Plot.AddScatter(xValues, yValues, lineWidth: 2, markerSize: 8);
            vacationsPlot.Plot.XAxis.DateTimeFormat(true);
            vacationsPlot.Plot.Title("توزيع الإجازات خلال الأشهر الماضية", size: 16);
            vacationsPlot.Plot.XLabel("الشهر");
            vacationsPlot.Plot.YLabel("عدد الإجازات");
        }

        private void plotTypeComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            UpdatePlot();
        }

        private void LoadVacations()
        {
            // استخدام DataTable لتخزين البيانات
            var table = DatabaseHelper.GetAllVacations();

            // تعيين عناوين الأعمدة بالعربية
            foreach (DataColumn column in table.Columns)
            {
                switch (column.ColumnName)
                {
                    case "VacationId":
                        column.ColumnName = "رقم الإجازة";
                        break;
                    case "EmployeeName":
                        column.ColumnName = "اسم الموظف";
                        break;
                    case "VacationType":
                        column.ColumnName = "نوع الإجازة";
                        break;
                    case "StartDate":
                        column.ColumnName = "تاريخ البداية";
                        break;
                    case "EndDate":
                        column.ColumnName = "تاريخ النهاية";
                        break;
                    case "DaysCount":
                        column.ColumnName = "عدد الأيام";
                        break;
                    case "Reason":
                        column.ColumnName = "السبب";
                        break;
                    case "Status":
                        column.ColumnName = "الحالة";
                        break;
                    case "RejectionReason":
                        column.ColumnName = "سبب الرفض";
                        break;
                    case "ApprovalDate":
                        column.ColumnName = "تاريخ الاعتماد";
                        break;
                    case "ApprovedBy":
                        column.ColumnName = "معتمد من";
                        break;
                }
            }

            // تحويل التواريخ إلى الصيغة المناسبة وتنظيف البيانات
            foreach (DataRow row in table.Rows)
            {
                // تحويل تاريخ البداية
                if (DateTime.TryParse(row["تاريخ البداية"].ToString(), out DateTime startDate))
                {
                    row["تاريخ البداية"] = startDate;
                }

                // تحويل تاريخ النهاية
                if (DateTime.TryParse(row["تاريخ النهاية"].ToString(), out DateTime endDate))
                {
                    row["تاريخ النهاية"] = endDate;
                }

                // تحويل تاريخ الاعتماد
                if (row["تاريخ الاعتماد"] != DBNull.Value && 
                    DateTime.TryParse(row["تاريخ الاعتماد"].ToString(), out DateTime approvalDate))
                {
                    row["تاريخ الاعتماد"] = approvalDate;
                }

                // تنظيف الحالة
                if (row["الحالة"] == DBNull.Value || string.IsNullOrEmpty(row["الحالة"].ToString()))
                {
                    row["الحالة"] = "في الانتظار";
                }

                // تنظيف سبب الرفض وإضافة رسالة واضحة للإجازات المرفوضة
                if (row["سبب الرفض"] == DBNull.Value || string.IsNullOrWhiteSpace(row["سبب الرفض"].ToString()))
                {
                    // إذا كانت الإجازة مرفوضة ولا يوجد سبب، أضف رسالة تنبيه
                    if (row["الحالة"].ToString() == "مرفوض")
                    {
                        row["سبب الرفض"] = "لم يتم تحديد سبب الرفض";
                    }
                    else
                    {
                        row["سبب الرفض"] = "";
                    }
                }

                // تنظيف معتمد من
                if (row["معتمد من"] == DBNull.Value)
                {
                    row["معتمد من"] = "";
                }
            }




            dataGridView1.DataSource = table;
            dataGridView1.Refresh();

            // تعيين رؤوس الأعمدة بالعربية مباشرة في DataGridView
            SetArabicColumnHeaders();

            // تطبيق ألوان مختلفة حسب حالة الإجازة
            ApplyStatusColors();

            // تحديث المخطط البياني
            UpdatePlot();
        }

        private void LoadEmployeeNames()
        {
            var employees = DatabaseHelper.GetAllEmployees();

            cmbEmployeeName.Items.Clear();
            foreach (DataRow row in employees.Rows)
            {
                cmbEmployeeName.Items.Add(row["الاسم"].ToString());
            }
            UpdateNoDocumentsLabel();
            cmbEmployeeName.AutoCompleteMode = AutoCompleteMode.SuggestAppend;
            cmbEmployeeName.AutoCompleteSource = AutoCompleteSource.ListItems;
        }

        /// <summary>
        /// دالة تعيين رؤوس الأعمدة بالعربية مباشرة في DataGridView
        /// </summary>
        private void SetArabicColumnHeaders()
        {
            try
            {
                if (dataGridView1?.Columns == null || dataGridView1.Columns.Count == 0)
                    return;

                foreach (DataGridViewColumn column in dataGridView1.Columns)
                {
                    if (column == null)
                        continue;

                    string columnName = column.DataPropertyName ?? column.Name;
                    if (string.IsNullOrEmpty(columnName))
                        continue;

                    switch (columnName)
                    {
                        case "VacationId":
                            column.HeaderText = "رقم الإجازة";
                            break;

                        case "EmployeeName":
                            column.HeaderText = "اسم الموظف";
                            break;

                        case "VacationType":
                            column.HeaderText = "نوع الإجازة";
                            break;

                        case "StartDate":
                            column.HeaderText = "تاريخ البداية";
                            break;

                        case "EndDate":
                            column.HeaderText = "تاريخ النهاية";
                            break;

                        case "DaysCount":
                            column.HeaderText = "عدد الأيام";
                            break;

                        case "Reason":
                            column.HeaderText = "السبب";
                            break;

                        case "Status":
                            column.HeaderText = "الحالة";
                            break;

                        case "RejectionReason":
                            column.HeaderText = "سبب الرفض";
                            column.DefaultCellStyle.BackColor = Color.LightYellow;
                            column.DefaultCellStyle.ForeColor = Color.DarkRed;
                            column.DefaultCellStyle.Font = new Font("Cairo", 12F, FontStyle.Bold);

                            // تأكد أن الخاصية Width قابلة للتعديل
                            if (column.Visible && column.Resizable != DataGridViewTriState.False)
                            {
                                column.Width = 200;
                            }
                            break;

                        case "ApprovalDate":
                            column.HeaderText = "تاريخ الاعتماد";
                            break;

                        case "ApprovedBy":
                            column.HeaderText = "معتمد من";
                            break;

                        default:
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تعيين رؤوس الأعمدة:\n" + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        
        private async void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                if (cmbEmployeeName.SelectedItem == null || string.IsNullOrWhiteSpace(cmbVacationType.Text))
                {
                    MessageBox.Show("الرجاء إدخال جميع البيانات المطلوبة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var vacation = new Vacation
                {
                    EmployeeName = cmbEmployeeName.Text,
                    VacationType = cmbVacationType.Text,
                    StartDate = dtStartDate.Value,
                    EndDate = dtEndDate.Value,
                    DaysCount = (int)(dtEndDate.Value.Date - dtStartDate.Value.Date).TotalDays,
                    Reason = txtReason.Text
                };

                await Task.Run(() => DatabaseHelper.AddVacation(vacation));

                // تسجيل عملية الإضافة
                var employeeName = cmbEmployeeName.Text;
                var description = $"إضافة إجازة جديدة للموظف: {employeeName}";
                var details = $"نوع الإجازة: {vacation.VacationType}, من: {vacation.StartDate:yyyy-MM-dd} إلى: {vacation.EndDate:yyyy-MM-dd}, عدد الأيام: {vacation.DaysCount}";
                
                await ActivityLogHelper.LogAddOperationAsync(
                    description,
                    "Vacations",
                    vacation.VacationId,
                    vacation,
                    "عادي"
                );

                MessageBox.Show("تمت إضافة الإجازة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                var vacations = await Task.Run(() => DatabaseHelper.GetAllVacations());
                dataGridView1.DataSource = vacations;

                ToastHelper.ShowAddToast();
                UpdateNoDocumentsLabel();
                LoadVacations();
                ClearFields();
            }
            catch (Exception ex)
            {
                // تسجيل فشل العملية
                var employeeName = cmbEmployeeName.Text;
                var description = $"فشل في إضافة إجازة للموظف: {employeeName}";
                
                await ActivityLogHelper.LogFailedOperationAsync(
                    description,
                    "Vacations",
                    ex.Message
                );
                
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
           
        }

       
        private async void btnUpdate_Click(object sender, EventArgs e)
        {
            if (!selectedVacationId.HasValue)
            {
                MessageBox.Show("الرجاء اختيار إجازة للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                // الحصول على البيانات القديمة قبل التحديث
                var oldVacation = DatabaseHelper.GetVacationById(selectedVacationId.Value);

                var vacation = new Vacation
                {
                    VacationId = selectedVacationId.Value,
                    EmployeeName = cmbEmployeeName.Text,
                    VacationType = cmbVacationType.Text,
                    StartDate = dtStartDate.Value,
                    EndDate = dtEndDate.Value,
                    DaysCount = (int)(dtEndDate.Value - dtStartDate.Value).TotalDays,
                    Reason = txtReason.Text
                };

                await Task.Run(() => DatabaseHelper.UpdateVacation(vacation));

                // تسجيل عملية التحديث
                var employeeName = cmbEmployeeName.Text;
                var description = $"تحديث إجازة للموظف: {employeeName}";
                
                await ActivityLogHelper.LogUpdateOperationAsync(
                    description,
                    "Vacations",
                    selectedVacationId.Value,
                    oldVacation,
                    vacation,
                    "عادي"
                );

                MessageBox.Show("تم تعديل الإجازة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                var vacations = await Task.Run(() => DatabaseHelper.GetAllVacations());
                dataGridView1.DataSource = vacations;

                ToastHelper.ShowEditToast();
                UpdateNoDocumentsLabel();
                LoadVacations();
                ClearFields();
            }
            catch (Exception ex)
            {
                // تسجيل فشل العملية
                var employeeName = cmbEmployeeName.Text;
                var description = $"فشل في تحديث إجازة للموظف: {employeeName}";
                
                await ActivityLogHelper.LogFailedOperationAsync(
                    description,
                    "Vacations",
                    ex.Message,
                    selectedVacationId,
                    "حرج"
                );
                
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
          
        }

       
        private async void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridView1.SelectedRows.Count == 0) return;

                string message = dataGridView1.SelectedRows.Count == 1
                    ? "هل أنت متأكد من حذف هذه الإجازة؟"
                    : $"هل أنت متأكد من حذف {dataGridView1.SelectedRows.Count} إجازة؟";

                if (MessageBox.Show(message, "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
                    return;

                List<int> ids = dataGridView1.SelectedRows
                    .Cast<DataGridViewRow>()
                    .Select(row => Convert.ToInt32(row.Cells["رقم الإجازة"].Value))
                    .ToList();

                // الحصول على بيانات الإجازات قبل الحذف لتسجيل العملية
                var vacationsToDelete = new List<Vacation>();
                foreach (var id in ids)
                {
                    var vacation = DatabaseHelper.GetVacationById(id);
                    if (vacation != null)
                        vacationsToDelete.Add(vacation);
                }

                await Task.Run(() =>
                {
                    foreach (var id in ids)
                        DatabaseHelper.DeleteVacation(id);
                });

                // تسجيل عمليات الحذف
                foreach (var vacation in vacationsToDelete)
                {
                    var description = $"حذف إجازة للموظف: {vacation.EmployeeName}";
                    
                    await ActivityLogHelper.LogDeleteOperationAsync(
                        description,
                        "Vacations",
                        vacation.VacationId,
                        vacation,
                        "مهم"
                    );
                }

                MessageBox.Show("تم حذف الإجازات بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                var vacations = await Task.Run(() => DatabaseHelper.GetAllVacations());
                dataGridView1.DataSource = vacations;

                ToastHelper.ShowDeleteToast();
                UpdateNoDocumentsLabel();
                LoadVacations();
                ClearFields();
                chkSelectAll.Checked = false;
            }
            catch (Exception ex)
            {
                // تسجيل فشل العملية
                var description = $"فشل في حذف {dataGridView1.SelectedRows.Count} إجازة";
                
                await ActivityLogHelper.LogFailedOperationAsync(
                    description,
                    "Vacations",
                    ex.Message,
                    null,
                    "حرج"
                );
                
                MessageBox.Show("حدث خطأ أثناء حذف الإجازات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
           
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedEmployee = cmbEmployeeName.Text;
                if (string.IsNullOrEmpty(selectedEmployee))
                {
                    MessageBox.Show("الرجاء اختيار اسم الموظف أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var vacations = DatabaseHelper.GetVacationsForEmployee(selectedEmployee);
                if (!vacations.Any())
                {
                    MessageBox.Show("لا توجد إجازات مسجلة لهذا الموظف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                //GenerateEmployeeVacationsReport(selectedEmployee, vacations);
                // حساب إجمالي الأيام حسب نوع الإجازة
                var summaryByType = vacations.GroupBy(v => v.VacationType ?? "غير محدد")
                    .ToDictionary(g => g.Key, g => g.Sum(v => v.DaysCount));
                int totalDays = vacations.Sum(v => v.DaysCount);

                // قراءة اسم المؤسسة من الإعدادات
                string companyName = "المؤسسة";
                try
                {
                    using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                    {
                        connection.Open();
                        string sql = "SELECT TOP 1 CompanyName FROM Settings";
                        using (var command = new SqlCommand(sql, connection))
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                companyName = reader["CompanyName"]?.ToString() ?? "المؤسسة";
                            }
                        }
                    }
                }
                catch
                {
                    companyName = "المؤسسة";
                }


                var employeeName = cmbEmployeeName.Text;

                // إنشاء HTML مباشرة في المتصفح
                string html = $@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>تقرير الإجازات - {employeeName}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');
        
        body {{ 
            font-family: 'Cairo', sans-serif;
            margin: 40px;
            direction: rtl;
            background-color: #f5f5f5;
            color: #333;
        }}
        
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        
        .org-name {{
            font-size: 24px;
            font-weight: bold;
            color: #45678a;
            margin-bottom: 10px;
        }}
        
        .report-title {{
            font-size: 20px;
            color: #666;
            margin-bottom: 20px;
        }}
        
        .employee-info {{
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }}
        
        .employee-info h2 {{
            color: #45678a;
            border-bottom: 2px solid #45678a;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }}
        
        .info-group {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }}
        
        .info-item {{
            margin-bottom: 10px;
        }}
        
        .info-label {{
            font-weight: bold;
            color: #666;
        }}
        
        .vacations-list {{
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        
        .vacations-list h2 {{
            color: #45678a;
            border-bottom: 2px solid #45678a;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }}
        
        table {{ 
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }}
        
        th, td {{ 
            padding: 12px;
            text-align: right;
            border: 1px solid #ddd;
        }}
        
        th {{ 
            background-color: #45678a;
            color: white;
            font-weight: bold;
        }}
        
        tr:nth-child(even) {{ 
            background-color: #f8f9fa;
        }}
        
        tr:hover {{
            background-color: #e9ecef;
        }}
        
        .summary {{
            margin-top: 30px;
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        
        .footer {{
            margin-top: 30px;
            text-align: center;
            color: #666;
            font-size: 14px;
        }}
        
        @media print {{
            body {{
                margin: 0;
                background: white;
            }}
            .header, .employee-info, .vacations-list, .summary {{
                box-shadow: none;
                border: 1px solid #ddd;
            }}
        }}
    </style>
</head>
<body>
    <div class=""header"">
        <div class=""org-name"">{companyName}</div>
        <div class=""report-title"">تقرير الإجازات</div>
    </div>

    <div class=""employee-info"">
        <h2>معلومات الموظف</h2>
        <div class=""info-group"">
            <div class=""info-item"">
                <span class=""info-label"">الاسم:</span>
                <span>{employeeName}</span>
            </div>
        </div>
    </div>

    <div class=""vacations-list"">
        <h2>سجل الإجازات</h2>
        <table>
            <thead>
                <tr>
                    <th>نوع الإجازة</th>
                    <th>تاريخ البداية</th>
                    <th>تاريخ النهاية</th>
                    <th>عدد الأيام</th>
                    <th>السبب</th>
                </tr>
            </thead>
            <tbody>";

                foreach (var vacation in vacations)
                {
                    html += $@"
                        <tr>
                            <td>{vacation.VacationType}</td>
                            <td>{vacation.StartDate:dd/MM/yyyy}</td>
                            <td>{vacation.EndDate:dd/MM/yyyy}</td>
                            <td>{vacation.DaysCount}</td>
                            <td>{vacation.Reason}</td>
                        </tr>";
                }

                html += @"
                    </tbody>
                </table>
            </div>

            <div class=""summary"">
                <h2>ملخص الإجازات</h2>
                <div class=""info-group"">";

                foreach (var summary in summaryByType)
                {
                    html += $@"
                        <div class=""info-item"">
                            <span class=""info-label"">{summary.Key}:</span>
                            <span>{summary.Value} يوم</span>
                        </div>";
                }

                html += $@"
                        <div class=""info-item"">
                            <span class=""info-label"">إجمالي الإجازات:</span>
                            <span>{totalDays} يوم</span>
                        </div>
                </div>
            </div>

            <div class=""footer"">
                <p>تم إنشاء هذا التقرير في {DateTime.Now:dd/MM/yyyy HH:mm}</p>
            </div>
        </body>
        </html>";

                // إنشاء محتوى HTML مؤقت وعرضه في المتصفح
                var tempFile = Path.GetTempFileName() + ".html";
                File.WriteAllText(tempFile, html);
                Process.Start(new ProcessStartInfo
                {
                    FileName = tempFile,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearFields();
        }

        // دالة فتح نموذج عرض الطلبات
        private void btnViewRequests_Click(object sender, EventArgs e)
        {
            try
            {
                var requestsForm = new VacationRequestsForm();
                requestsForm.ShowDialog();
                
                // إعادة تحميل البيانات بعد إغلاق نموذج الطلبات
                LoadVacations();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح نموذج الطلبات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // دالة اعتماد الإجازة
        private async void btnApprove_Click(object sender, EventArgs e)
        {
            try
            {
                if (!selectedVacationId.HasValue)
                {
                    MessageBox.Show("الرجاء اختيار إجازة للاعتماد", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // التحقق من حالة الإجازة الحالية
                var vacation = DatabaseHelper.GetVacationById(selectedVacationId.Value);
                if (vacation.Status == "معتمد")
                {
                    MessageBox.Show("هذه الإجازة معتمدة بالفعل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = MessageBox.Show($"هل أنت متأكد من اعتماد إجازة الموظف: {vacation.EmployeeName}؟", 
                    "تأكيد الاعتماد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // الحصول على اسم المستخدم الحالي (يمكنك تعديل هذا حسب نظام المصادقة لديك)
                    string currentUser = Environment.UserName; // أو من نظام تسجيل الدخول

                    await Task.Run(() => DatabaseHelper.ApproveVacation(selectedVacationId.Value, currentUser));

                    // تسجيل عملية الاعتماد
                    var description = $"اعتماد إجازة للموظف: {vacation.EmployeeName}";
                    await ActivityLogHelper.LogUpdateOperationAsync(
                        description,
                        "Vacations",
                        selectedVacationId.Value,
                        vacation,
                        vacation,
                        "مهم"
                    );

                    MessageBox.Show("تم اعتماد الإجازة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadVacations();
                    ClearFields();
                    ToastHelper.ShowEditToast();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء اعتماد الإجازة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // دالة رفض الإجازة
        private async void btnReject_Click(object sender, EventArgs e)
        {
            try
            {
                if (!selectedVacationId.HasValue)
                {
                    MessageBox.Show("الرجاء اختيار إجازة للرفض", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // التحقق من حالة الإجازة الحالية
                var vacation = DatabaseHelper.GetVacationById(selectedVacationId.Value);
                if (vacation.Status == "مرفوض")
                {
                    MessageBox.Show("هذه الإجازة مرفوضة بالفعل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // طلب سبب الرفض
                string rejectionReason = Microsoft.VisualBasic.Interaction.InputBox(
                    "الرجاء إدخال سبب رفض الإجازة:",
                    "سبب الرفض",
                    "");

                if (string.IsNullOrWhiteSpace(rejectionReason))
                {
                    MessageBox.Show("يجب إدخال سبب الرفض", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show($"هل أنت متأكد من رفض إجازة الموظف: {vacation.EmployeeName}؟", 
                    "تأكيد الرفض", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // الحصول على اسم المستخدم الحالي
                    string currentUser = Environment.UserName; // أو من نظام تسجيل الدخول

                    await Task.Run(() => DatabaseHelper.RejectVacation(selectedVacationId.Value, rejectionReason, currentUser));

                    // تسجيل عملية الرفض
                    var description = $"رفض إجازة للموظف: {vacation.EmployeeName} - السبب: {rejectionReason}";
                    await ActivityLogHelper.LogUpdateOperationAsync(
                        description,
                        "Vacations",
                        selectedVacationId.Value,
                        vacation,
                        vacation,
                        "مهم"
                    );

                    MessageBox.Show("تم رفض الإجازة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadVacations();
                    ClearFields();
                    ToastHelper.ShowEditToast();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء رفض الإجازة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearFields()
        {
            selectedVacationId = null;
            cmbEmployeeName.SelectedIndex = -1;
            cmbVacationType.SelectedIndex = -1;
            dtStartDate.Value = DateTime.Today;
            dtEndDate.Value = DateTime.Today;
            txtReason.Clear();
            
            // إعادة تعيين حالة أزرار الموافقة والرفض
            btnApprove.Enabled = true;
            btnReject.Enabled = true;
            btnApprove.BackColor = Color.FromArgb(40, 167, 69);
            btnReject.BackColor = Color.FromArgb(220, 53, 69);
        }

        private async void button1_Click(object sender, EventArgs e)
        {
            try
            {
                using (SaveFileDialog sfd = new SaveFileDialog())
                {
                    sfd.Filter = "Excel Workbook|*.xlsx";
                    sfd.FileName = "تقرير_الاجازات_" + DateTime.Now.ToString("yyyy-MM-dd") + ".xlsx";

                    if (sfd.ShowDialog() == DialogResult.OK)
                    {
                        using (XLWorkbook workbook = new XLWorkbook())
                        {
                            var worksheet = workbook.Worksheets.Add("الاجازات");

                            // Add headers
                            for (int i = 0; i < dataGridView1.Columns.Count; i++)
                            {
                                worksheet.Cell(1, i + 1).Value = dataGridView1.Columns[i].HeaderText;
                            }

                            // Add data
                            for (int i = 0; i < dataGridView1.Rows.Count; i++)
                            {
                                for (int j = 0; j < dataGridView1.Columns.Count; j++)
                                {
                                    worksheet.Cell(i + 2, j + 1).Value = dataGridView1.Rows[i].Cells[j].Value?.ToString() ?? "";
                                }
                            }

                            // Format as table
                            var range = worksheet.Range(1, 1, dataGridView1.Rows.Count + 1, dataGridView1.Columns.Count);
                            range.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            range.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                            worksheet.Row(1).Style.Fill.BackgroundColor = XLColor.LightGray;
                            worksheet.Row(1).Style.Font.Bold = true;

                            // Adjust column widths
                            worksheet.Columns().AdjustToContents();

                            workbook.SaveAs(sfd.FileName);
                        }

                        // تسجيل عملية التصدير
                        await ActivityLogHelper.LogExportAsync(
                            "تصدير الإجازات إلى Excel",
                            "Vacations",
                            dataGridView1.Rows.Count,
                            sfd.FileName,
                            "Excel"
                        );

                        MessageBox.Show("تم تصدير البيانات بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                // تسجيل فشل العملية
                await ActivityLogHelper.LogFailedOperationAsync(
                    "تصدير الإجازات إلى Excel",
                    "Vacations",
                    ex.Message,
                    null,
                    "حرج"
                );
                
                MessageBox.Show("حدث خطأ أثناء تصدير البيانات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnPrintHtml_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable table;
                string reportTitle = "تقرير الإجازات";

                // إذا كان هناك فلتر شهري محدد
                if (cmbMonthFilter.SelectedIndex > 0)
                {
                    table = DatabaseHelper.GetAllVacations().Clone();
                    foreach (DataRow row in DatabaseHelper.GetAllVacations().Rows)
                    {
                        bool showInMonth = false;

                        if (DateTime.TryParse(row["StartDate"].ToString(), out DateTime startDate) &&
                            DateTime.TryParse(row["EndDate"].ToString(), out DateTime endDate))
                        {
                            var currentDate = new DateTime(startDate.Year, cmbMonthFilter.SelectedIndex, 1);
                            showInMonth = startDate.Date <= currentDate.Date.AddMonths(1).AddDays(-1) &&
                                        endDate.Date >= currentDate.Date;
                        }

                        if (showInMonth)
                        {
                            table.ImportRow(row);
                        }
                    }
                    reportTitle = $"تقرير الإجازات - شهر {cmbMonthFilter.Text}";
                }
                else
                {
                    table = DatabaseHelper.GetAllVacations();
                }

                if (table == null || table.Rows.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات للطباعة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                GenerateVacationsTableReport(table, reportTitle);

                // تسجيل عملية الطباعة
                await ActivityLogHelper.LogReportGenerationAsync(
                    reportTitle,
                    "Vacations",
                    table.Rows.Count,
                    "HTML",
                    cmbMonthFilter.SelectedIndex > 0 ? $"فلتر الشهر: {cmbMonthFilter.Text}" : "جميع البيانات"
                );
            }
            catch (Exception ex)
            {
                // تسجيل فشل العملية
                await ActivityLogHelper.LogFailedOperationAsync(
                    "طباعة تقرير الإجازات",
                    "Vacations",
                    ex.Message,
                    null,
                    "حرج"
                );
                
                MessageBox.Show($"حدث خطأ أثناء إنشاء التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void GenerateEmployeeVacationsReport(string employeeName, IEnumerable<Vacation> vacations)
        {
            // حساب إجمالي الأيام حسب نوع الإجازة
            var summaryByType = vacations.GroupBy(v => v.VacationType ?? "غير محدد")
                .ToDictionary(g => g.Key, g => g.Sum(v => v.DaysCount));
            int totalDays = vacations.Sum(v => v.DaysCount);

            // قراءة اسم المؤسسة من الإعدادات
            string companyName = "المؤسسة";
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT TOP 1 CompanyName FROM Settings";
                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            companyName = reader["CompanyName"]?.ToString() ?? "المؤسسة";
                        }
                    }
                }
            }
            catch
            {
                companyName = "المؤسسة";
            }


            string html = GenerateHtmlReport(companyName, employeeName, vacations, summaryByType, totalDays);

            string htmlFolder = Path.Combine(Application.StartupPath, "html");
            if (!Directory.Exists(htmlFolder))
            {
                Directory.CreateDirectory(htmlFolder);
            }

            string fileName = Path.Combine(htmlFolder, $"تقرير_إجازات_{employeeName}_{DateTime.Now:yyyy-MM-dd_HH-mm}.html");
            File.WriteAllText(fileName, html, System.Text.Encoding.UTF8);

            Process.Start(new ProcessStartInfo
            {
                FileName = fileName,
                UseShellExecute = true
            });
        }
        private void GenerateVacationsTableReport(DataTable vacations, string reportTitle)
        {
            try
            {
                // قراءة اسم المؤسسة من الإعدادات
                string companyName = "المؤسسة";
                try
                {
                    using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                    {
                        connection.Open();
                        string sql = "SELECT TOP 1 CompanyName FROM Settings";
                        using (var command = new SqlCommand(sql, connection))
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                companyName = reader["CompanyName"]?.ToString() ?? "المؤسسة";
                            }
                        }
                    }
                }
                catch
                {
                    companyName = "المؤسسة";
                }


                // إنشاء HTML مباشرة في المتصفح
                string html = $@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>{reportTitle}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');
        
        body {{ 
            font-family: 'Cairo', sans-serif;
            margin: 40px;
            direction: rtl;
            background-color: #f5f5f5;
            color: #333;
        }}
        
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        
        .org-name {{
            font-size: 24px;
            font-weight: bold;
            color: #45678a;
            margin-bottom: 10px;
        }}
        
        .report-title {{
            font-size: 20px;
            color: #666;
            margin-bottom: 20px;
        }}
        
        .vacations-list {{
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }}
        
        table {{ 
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }}
        
        th, td {{ 
            padding: 12px;
            text-align: right;
            border: 1px solid #ddd;
        }}
        
        th {{ 
            background-color: #45678a;
            color: white;
            font-weight: bold;
        }}
        
        tr:nth-child(even) {{ 
            background-color: #f8f9fa;
        }}
        
        tr:hover {{
            background-color: #e9ecef;
        }}
        
        .summary {{
            margin-top: 30px;
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        
        .footer {{
            margin-top: 30px;
            text-align: center;
            color: #666;
            font-size: 14px;
        }}
        
        @media print {{
            body {{
                margin: 0;
                background: white;
            }}
            .header, .vacations-list, .summary {{
                box-shadow: none;
                border: 1px solid #ddd;
            }}
        }}
    </style>
</head>
<body>
    <div class=""header"">
        <div class=""org-name"">{companyName}</div>
        <div class=""report-title"">{reportTitle}</div>
    </div>

    <div class=""vacations-list"">
        <table>
            <thead>
                <tr>
                    <th>اسم الموظف</th>
                    <th>نوع الإجازة</th>
                    <th>تاريخ البداية</th>
                    <th>تاريخ النهاية</th>
                    <th>عدد الأيام</th>
                    <th>السبب</th>
                </tr>
            </thead>
            <tbody>";

                // إضافة البيانات
                foreach (DataRow row in vacations.Rows)
                {
                    string startDate = "غير محدد";
                    string endDate = "غير محدد";

                    if (DateTime.TryParse(row["StartDate"]?.ToString(), out DateTime start))
                        startDate = start.ToString("dd/MM/yyyy");
                    if (DateTime.TryParse(row["EndDate"]?.ToString(), out DateTime end))
                        endDate = end.ToString("dd/MM/yyyy");

                    html += $@"
                <tr>
                    <td>{row["EmployeeName"]}</td>
                    <td>{row["VacationType"]}</td>
                    <td>{startDate}</td>
                    <td>{endDate}</td>
                    <td>{row["DaysCount"]}</td>
                    <td>{row["Reason"]}</td>
                </tr>";
                }

                html += $@"
                </tbody>
            </table>
        </div>

        <div class=""summary"">
            <div class=""footer"">
                <p>تم إنشاء هذا التقرير في {DateTime.Now:dd/MM/yyyy HH:mm}</p>
            </div>
        </div>
    </body>
    </html>";

                // إنشاء محتوى HTML مؤقت وعرضه في المتصفح
                var tempFile = Path.GetTempFileName() + ".html";
                File.WriteAllText(tempFile, html);
                Process.Start(new ProcessStartInfo
                {
                    FileName = tempFile,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GenerateHtmlReport(string companyName, string employeeName, IEnumerable<Vacation> vacations, Dictionary<string, int> summaryByType, int totalDays)
        {
            return $@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>تقرير الإجازات - {employeeName}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');
        body {{ font-family: 'Cairo', sans-serif; margin: 40px; direction: rtl; background-color: #f5f5f5; color: #333; }}
        .header {{ text-align: center; margin-bottom: 30px; padding: 20px; background: #fff; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
        .org-name {{ font-size: 24px; font-weight: bold; color: #45678a; margin-bottom: 10px; }}
        .report-title {{ font-size: 20px; color: #666; margin-bottom: 20px; }}
        .employee-info {{ background: #fff; padding: 20px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); margin-bottom: 30px; }}
        h2 {{ color: #45678a; border-bottom: 2px solid #45678a; padding-bottom: 10px; margin-bottom: 20px; }}
        .info-group {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }}
        .info-item {{ margin-bottom: 10px; }}
        .info-label {{ font-weight: bold; color: #666; }}
        .vacations-list {{ background: #fff; padding: 20px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
        th, td {{ padding: 12px; text-align: right; border: 1px solid #ddd; }}
        th {{ background-color: #45678a; color: white; font-weight: bold; }}
        tr:nth-child(even) {{ background-color: #f8f9fa; }}
        tr:hover {{ background-color: #e9ecef; }}
        .summary {{ margin-top: 30px; background: #fff; padding: 20px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
        .footer {{ margin-top: 30px; text-align: center; color: #666; font-size: 14px; }}
        @media print {{ body {{ margin: 0; background: white; }} .header, .employee-info, .vacations-list, .summary {{ box-shadow: none; border: 1px solid #ddd; }} }}
    </style>
</head>
<body>
    <div class=""header"">
        <div class=""org-name"">{companyName}</div>
        <div class=""report-title"">تقرير الإجازات</div>
    </div>

    <div class=""employee-info"">
        <h2>معلومات الموظف</h2>
        <div class=""info-group"">
            <div class=""info-item"">
                <span class=""info-label"">الاسم:</span>
                <span>{employeeName}</span>
            </div>
        </div>
    </div>

    <div class=""vacations-list"">
        <h2>سجل الإجازات</h2>
        <table>
            <thead>
                <tr>
                    <th>نوع الإجازة</th>
                    <th>تاريخ البداية</th>
                    <th>تاريخ النهاية</th>
                    <th>عدد الأيام</th>
                    <th>السبب</th>
                </tr>
            </thead>
            <tbody>
                {string.Join("", vacations.Select(v => $@"
                    <tr>
                        <td>{v.VacationType}</td>
                        <td>{v.StartDate:dd/MM/yyyy}</td>
                        <td>{v.EndDate:dd/MM/yyyy}</td>
                        <td>{v.DaysCount}</td>
                        <td>{v.Reason}</td>
                    </tr>"))}
            </tbody>
        </table>
    </div>

    <div class=""summary"">
        <h2>ملخص الإجازات</h2>
        <div class=""info-group"">
            {string.Join("", summaryByType.Select(s => $@"
                <div class=""info-item"">
                    <span class=""info-label"">{s.Key}:</span>
                    <span>{s.Value} يوم</span>
                </div>"))}
            <div class=""info-item"">
                <span class=""info-label"">إجمالي الإجازات:</span>
                <span>{totalDays} يوم</span>
            </div>
        </div>
    </div>

    <div class=""footer"">
        <p>تم إنشاء هذا التقرير في {DateTime.Now:dd/MM/yyyy HH:mm}</p>
    </div>
</body>
</html>";
        }

        private void cmbMonthFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadVacations(cmbMonthFilter.SelectedIndex);
        }
        private void LoadVacations(int? monthFilter = null)
        {
            // استخدام DataTable لتخزين البيانات
            var table = DatabaseHelper.GetAllVacations();

            if (monthFilter.HasValue && monthFilter.Value > 0)
            {                // فلترة البيانات حسب الشهر
                var filteredTable = DatabaseHelper.GetAllVacations().Clone();
                foreach (DataRow row in DatabaseHelper.GetAllVacations().Rows)
                {
                    bool showInMonth = false;

                    if (DateTime.TryParse(row["StartDate"].ToString(), out DateTime startDate) &&
                        DateTime.TryParse(row["EndDate"].ToString(), out DateTime endDate))
                    {
                        // إظهار الإجازة إذا كان الشهر المحدد يقع بين تاريخ البداية والنهاية
                        var currentDate = new DateTime(startDate.Year, monthFilter.Value, 1);
                        showInMonth = startDate.Date <= currentDate.Date.AddMonths(1).AddDays(-1) &&
                                    endDate.Date >= currentDate.Date;
                    }

                    if (showInMonth)
                    {
                        filteredTable.ImportRow(row);
                    }
                }
                table = filteredTable;
            }

            // تعيين عناوين الأعمدة بالعربية
            foreach (DataColumn column in table.Columns)
            {
                switch (column.ColumnName)
                {
                    case "VacationId":
                        column.ColumnName = "رقم الإجازة";
                        break;
                    case "EmployeeName":
                        column.ColumnName = "اسم الموظف";
                        break;
                    case "VacationType":
                        column.ColumnName = "نوع الإجازة";
                        break;
                    case "StartDate":
                        column.ColumnName = "تاريخ البداية";
                        break;
                    case "EndDate":
                        column.ColumnName = "تاريخ النهاية";
                        break;
                    case "DaysCount":
                        column.ColumnName = "عدد الأيام";
                        break;
                    case "Reason":
                        column.ColumnName = "السبب";
                        break;
                    case "Status":
                        column.ColumnName = "الحالة";
                        break;
                    case "RejectionReason":
                        column.ColumnName = "سبب الرفض";
                        break;
                    case "ApprovalDate":
                        column.ColumnName = "تاريخ الاعتماد";
                        break;
                    case "ApprovedBy":
                        column.ColumnName = "معتمد من";
                        break;
                }
            }

            foreach (DataRow row in table.Rows)
            {
                if (DateTime.TryParse(row["تاريخ البداية"].ToString(), out DateTime startDate))
                {
                    row["تاريخ البداية"] = startDate; // خزن كـ DateTime
                }

                if (DateTime.TryParse(row["تاريخ النهاية"].ToString(), out DateTime endDate))
                {
                    row["تاريخ النهاية"] = endDate; // خزن كـ DateTime
                }
            }



            dataGridView1.DataSource = table;
            dataGridView1.Refresh();

            // تعيين رؤوس الأعمدة بالعربية مباشرة في DataGridView
            SetArabicColumnHeaders();

            // تطبيق ألوان مختلفة حسب حالة الإجازة
            ApplyStatusColors();

            UpdatePlot();
        }

        private void VacationForm_Load(object sender, EventArgs e)
        {
            ClearFields();
            UIHelper.ShowEmptyMessage(dataGridView1, lbl_NoDocuments, "لا توجد بيانات");
        }
        private void UpdateNoDocumentsLabel()
        {
            lbl_NoDocuments.Visible = dataGridView1.Rows.Count == 0;
        }

        private async void txtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                e.SuppressKeyPress = true;

                await PerformSearchAsync();
            }
        }
        // الدالة التي تنفذ البحث
        private async Task PerformSearchAsync()
        {
            string searchText = txtSearch.Text.Trim();

            if (string.IsNullOrWhiteSpace(searchText))
            {
                MessageBox.Show("الرجاء كتابة شيء للبحث عنه.", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                LoadVacations(); // تحميل جميع البيانات مباشرة
                return;
            }

            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh();

            try
            {
                var table = await Task.Run(() => DatabaseHelper.SearchVacations(searchText));

                // تسجيل عملية البحث
                int resultCount = table?.Rows.Count ?? 0;
                await ActivityLogHelper.LogSearchAsync(
                    "البحث في الإجازات",
                    "Vacations",
                    searchText,
                    resultCount,
                    $"تم البحث عن: {searchText}"
                );

                if (table == null || table.Rows.Count == 0)
                {
                    MessageBox.Show("لم يتم العثور على نتائج مطابقة.", "نتائج البحث", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var columnNames = new Dictionary<string, string>()
                {
                 { "VacationId", "رقم الإجازة" },
                 { "EmployeeName", "اسم الموظف" },
                 { "VacationType", "نوع الإجازة" },
                 { "StartDate", "تاريخ البداية" },
                 { "EndDate", "تاريخ النهاية" },
                 { "DaysCount", "عدد الأيام" },
                 { "Reason", "السبب" }
                };

                foreach (DataColumn column in table.Columns)
                {
                    if (columnNames.ContainsKey(column.ColumnName))
                        column.ColumnName = columnNames[column.ColumnName];
                }

                dataGridView1.Invoke((MethodInvoker)(() =>
                {
                    dataGridView1.DataSource = table;
                    dataGridView1.Refresh();
                }));
            }
            finally
            {
                loadingForm.Close();
            }
        }


        private async void btnSearchv_Click(object sender, EventArgs e)
        {
            await PerformSearchAsync();
        }

        private void dataGridView1_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (dataGridView1.CurrentRow != null)
            {
                try
                {
                    selectedVacationId = Convert.ToInt32(dataGridView1.CurrentRow.Cells["رقم الإجازة"].Value);
                    if (selectedVacationId.HasValue)
                    {
                        var vacation = DatabaseHelper.GetVacationById(selectedVacationId.Value);
                        cmbEmployeeName.Text = vacation.EmployeeName;
                        cmbVacationType.Text = vacation.VacationType;
                        dtStartDate.Value = vacation.StartDate;
                        dtEndDate.Value = vacation.EndDate;
                        txtReason.Text = vacation.Reason ?? "";
                        
                        // إظهار سبب الرفض إذا كانت الإجازة مرفوضة
                        if (vacation.Status == "مرفوض")
                        {
                            if (!string.IsNullOrWhiteSpace(vacation.RejectionReason))
                            {
                                MessageBox.Show($"هذه الإجازة مرفوضة\n\nسبب الرفض: {vacation.RejectionReason}", 
                                    "إجازة مرفوضة", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                            else
                            {
                                MessageBox.Show("هذه الإجازة مرفوضة ولكن لم يتم تحديد سبب الرفض", 
                                    "إجازة مرفوضة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            }
                        }
                        
                        // تحديث حالة أزرار الموافقة والرفض
                        UpdateApprovalButtonsState(vacation.Status);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحديد الصف: {ex.Message}");
                }
            }
        }

        // دالة تحديث حالة أزرار الموافقة والرفض
        private void UpdateApprovalButtonsState(string status)
        {
            switch (status)
            {
                case "معتمد":
                    btnApprove.Enabled = false;
                    btnReject.Enabled = true;
                    btnApprove.BackColor = Color.Gray;
                    btnReject.BackColor = Color.FromArgb(220, 53, 69);
                    break;
                case "مرفوض":
                    btnApprove.Enabled = true;
                    btnReject.Enabled = false;
                    btnApprove.BackColor = Color.FromArgb(40, 167, 69);
                    btnReject.BackColor = Color.Gray;
                    break;
                case "في الانتظار":
                default:
                    btnApprove.Enabled = true;
                    btnReject.Enabled = true;
                    btnApprove.BackColor = Color.FromArgb(40, 167, 69);
                    btnReject.BackColor = Color.FromArgb(220, 53, 69);
                    break;
            }
        }

        // دالة تطبيق ألوان مختلفة حسب حالة الإجازة
        private void ApplyStatusColors()
        {
            foreach (DataGridViewRow row in dataGridView1.Rows)
            {
                if (row.Cells["الحالة"].Value != null)
                {
                    string status = row.Cells["الحالة"].Value.ToString();
                    switch (status)
                    {
                        case "معتمد":
                            row.DefaultCellStyle.BackColor = Color.LightGreen;
                            row.DefaultCellStyle.ForeColor = Color.DarkGreen;
                            break;
                        case "مرفوض":
                            row.DefaultCellStyle.BackColor = Color.LightCoral;
                            row.DefaultCellStyle.ForeColor = Color.DarkRed;
                            break;
                        case "في الانتظار":
                            row.DefaultCellStyle.BackColor = Color.LightYellow;
                            row.DefaultCellStyle.ForeColor = Color.DarkOrange;
                            break;
                        default:
                            row.DefaultCellStyle.BackColor = Color.White;
                            row.DefaultCellStyle.ForeColor = Color.Black;
                            break;
                    }
                }
            }
        }
    }
}