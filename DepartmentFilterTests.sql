-- ===================================================================
-- اختبارات نظام فلترة الأقسام
-- ===================================================================

PRINT '===================================================================';
PRINT 'بدء اختبارات نظام فلترة الأقسام';
PRINT '===================================================================';

-- 1. اختبار البنية الأساسية
-- ===================================================================

PRINT 'اختبار 1: التحقق من وجود الأعمدة المطلوبة';

-- التحقق من جدول Users
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'DepartmentId')
    PRINT '✓ عمود DepartmentId موجود في جدول Users';
ELSE
    PRINT '✗ عمود DepartmentId غير موجود في جدول Users';

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'IsActive')
    PRINT '✓ عمود IsActive موجود في جدول Users';
ELSE
    PRINT '✗ عمود IsActive غير موجود في جدول Users';

-- التحقق من جدول Departments
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Departments' AND COLUMN_NAME = 'ManagerUserId')
    PRINT '✓ عمود ManagerUserId موجود في جدول Departments';
ELSE
    PRINT '✗ عمود ManagerUserId غير موجود في جدول Departments';

-- 2. اختبار البيانات الأساسية
-- ===================================================================

PRINT '';
PRINT 'اختبار 2: التحقق من وجود البيانات الأساسية';

DECLARE @DeptCount INT = (SELECT COUNT(*) FROM Departments WHERE IsActive = 1);
DECLARE @UserCount INT = (SELECT COUNT(*) FROM Users WHERE IsActive = 1);
DECLARE @ManagerCount INT = (SELECT COUNT(*) FROM Departments WHERE ManagerUserId IS NOT NULL AND IsActive = 1);

PRINT 'عدد الأقسام النشطة: ' + CAST(@DeptCount AS VARCHAR(10));
PRINT 'عدد المستخدمين النشطين: ' + CAST(@UserCount AS VARCHAR(10));
PRINT 'عدد الأقسام التي لها مدراء: ' + CAST(@ManagerCount AS VARCHAR(10));

IF @DeptCount > 0
    PRINT '✓ يوجد أقسام في النظام';
ELSE
    PRINT '✗ لا توجد أقسام في النظام';

IF @UserCount > 0
    PRINT '✓ يوجد مستخدمين في النظام';
ELSE
    PRINT '✗ لا توجد مستخدمين في النظام';

-- 3. اختبار ربط المدراء بالأقسام
-- ===================================================================

PRINT '';
PRINT 'اختبار 3: التحقق من ربط المدراء بالأقسام';

SELECT 
    d.DepartmentName as 'القسم',
    u.FullName as 'المدير',
    u.UserType as 'نوع المستخدم',
    CASE 
        WHEN u.UserType IN ('مدير القسم', 'مدير قسم') THEN '✓'
        ELSE '✗'
    END as 'نوع صحيح؟'
FROM Departments d
LEFT JOIN Users u ON d.ManagerUserId = u.UserId
WHERE d.IsActive = 1;

-- 4. اختبار ربط المستخدمين بالأقسام
-- ===================================================================

PRINT '';
PRINT 'اختبار 4: التحقق من ربط المستخدمين بالأقسام';

SELECT 
    d.DepartmentName as 'القسم',
    COUNT(u.UserId) as 'عدد المستخدمين'
FROM Departments d
LEFT JOIN Users u ON d.DepartmentId = u.DepartmentId AND u.IsActive = 1
WHERE d.IsActive = 1
GROUP BY d.DepartmentId, d.DepartmentName
ORDER BY d.DepartmentName;

-- 5. اختبار ربط الموظفين بالمستخدمين
-- ===================================================================

PRINT '';
PRINT 'اختبار 5: التحقق من ربط الموظفين بالمستخدمين';

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Employees')
BEGIN
    SELECT 
        d.DepartmentName as 'القسم',
        COUNT(e.EmployeeCode) as 'عدد الموظفين'
    FROM Departments d
    LEFT JOIN Users u ON d.DepartmentId = u.DepartmentId AND u.IsActive = 1
    LEFT JOIN Employees e ON u.UserId = e.EmployeeCode
    WHERE d.IsActive = 1
    GROUP BY d.DepartmentId, d.DepartmentName
    ORDER BY d.DepartmentName;
END
ELSE
BEGIN
    PRINT 'جدول Employees غير موجود - تخطي هذا الاختبار';
END

-- 6. اختبار دالة GetManagedDepartmentId
-- ===================================================================

PRINT '';
PRINT 'اختبار 6: اختبار دالة GetManagedDepartmentId';

-- اختبار لكل مدير قسم
DECLARE @TestUserId INT;
DECLARE @TestDeptId INT;
DECLARE user_cursor CURSOR FOR
    SELECT u.UserId, d.DepartmentId
    FROM Users u
    INNER JOIN Departments d ON u.UserId = d.ManagerUserId
    WHERE u.UserType IN ('مدير القسم', 'مدير قسم') AND u.IsActive = 1;

OPEN user_cursor;
FETCH NEXT FROM user_cursor INTO @TestUserId, @TestDeptId;

WHILE @@FETCH_STATUS = 0
BEGIN
    DECLARE @FoundDeptId INT;
    
    -- محاكاة دالة GetManagedDepartmentId
    SELECT @FoundDeptId = DepartmentId 
    FROM Departments 
    WHERE ManagerUserId = @TestUserId AND IsActive = 1;
    
    IF @FoundDeptId = @TestDeptId
        PRINT '✓ المستخدم ' + CAST(@TestUserId AS VARCHAR(10)) + ' مرتبط بالقسم ' + CAST(@TestDeptId AS VARCHAR(10));
    ELSE
        PRINT '✗ خطأ في ربط المستخدم ' + CAST(@TestUserId AS VARCHAR(10)) + ' بالقسم';
    
    FETCH NEXT FROM user_cursor INTO @TestUserId, @TestDeptId;
END

CLOSE user_cursor;
DEALLOCATE user_cursor;

-- 7. اختبار فلترة الموظفين حسب القسم
-- ===================================================================

PRINT '';
PRINT 'اختبار 7: اختبار فلترة الموظفين حسب القسم';

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Employees')
BEGIN
    -- لكل قسم، عرض الموظفين المرتبطين به
    DECLARE @DeptId INT;
    DECLARE @DeptName VARCHAR(100);
    DECLARE dept_cursor CURSOR FOR
        SELECT DepartmentId, DepartmentName
        FROM Departments
        WHERE IsActive = 1;

    OPEN dept_cursor;
    FETCH NEXT FROM dept_cursor INTO @DeptId, @DeptName;

    WHILE @@FETCH_STATUS = 0
    BEGIN
        DECLARE @EmpCount INT;
        
        SELECT @EmpCount = COUNT(*)
        FROM Employees e
        INNER JOIN Users u ON e.EmployeeCode = u.UserId
        WHERE u.DepartmentId = @DeptId AND u.IsActive = 1;
        
        PRINT 'القسم "' + @DeptName + '" يحتوي على ' + CAST(@EmpCount AS VARCHAR(10)) + ' موظف';
        
        FETCH NEXT FROM dept_cursor INTO @DeptId, @DeptName;
    END

    CLOSE dept_cursor;
    DEALLOCATE dept_cursor;
END

-- 8. اختبار أنواع المستخدمين
-- ===================================================================

PRINT '';
PRINT 'اختبار 8: التحقق من أنواع المستخدمين';

SELECT 
    UserType as 'نوع المستخدم',
    COUNT(*) as 'العدد'
FROM Users
WHERE IsActive = 1
GROUP BY UserType
ORDER BY UserType;

-- التحقق من وجود مدير عام
IF EXISTS (SELECT * FROM Users WHERE UserType = 'مدير' AND IsActive = 1)
    PRINT '✓ يوجد مدير عام في النظام';
ELSE
    PRINT '✗ لا يوجد مدير عام في النظام';

-- التحقق من وجود مدراء أقسام
IF EXISTS (SELECT * FROM Users WHERE UserType IN ('مدير القسم', 'مدير قسم') AND IsActive = 1)
    PRINT '✓ يوجد مدراء أقسام في النظام';
ELSE
    PRINT '✗ لا يوجد مدراء أقسام في النظام';

-- 9. اختبار التكامل بين الجداول
-- ===================================================================

PRINT '';
PRINT 'اختبار 9: اختبار التكامل بين الجداول';

-- التحقق من وجود مستخدمين بدون أقسام (غير مدراء عامين)
DECLARE @UsersWithoutDept INT = (
    SELECT COUNT(*) 
    FROM Users 
    WHERE DepartmentId IS NULL 
    AND UserType != 'مدير' 
    AND IsActive = 1
);

IF @UsersWithoutDept = 0
    PRINT '✓ جميع المستخدمين (غير المدراء العامين) مرتبطين بأقسام';
ELSE
    PRINT '⚠ يوجد ' + CAST(@UsersWithoutDept AS VARCHAR(10)) + ' مستخدم غير مرتبط بقسم';

-- التحقق من وجود أقسام بدون مدراء
DECLARE @DeptsWithoutManager INT = (
    SELECT COUNT(*) 
    FROM Departments 
    WHERE ManagerUserId IS NULL 
    AND IsActive = 1
);

IF @DeptsWithoutManager = 0
    PRINT '✓ جميع الأقسام لها مدراء';
ELSE
    PRINT '⚠ يوجد ' + CAST(@DeptsWithoutManager AS VARCHAR(10)) + ' قسم بدون مدير';

-- 10. اختبار الأداء
-- ===================================================================

PRINT '';
PRINT 'اختبار 10: اختبار الأداء';

-- اختبار سرعة الاستعلامات الأساسية
DECLARE @StartTime DATETIME = GETDATE();

-- استعلام فلترة الموظفين حسب القسم
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Employees')
BEGIN
    SELECT COUNT(*)
    FROM Employees e
    INNER JOIN Users u ON e.EmployeeCode = u.UserId
    WHERE u.DepartmentId = 1 AND u.IsActive = 1;
END

-- استعلام البحث عن القسم المُدار
SELECT DepartmentId 
FROM Departments 
WHERE ManagerUserId = 1 AND IsActive = 1;

DECLARE @EndTime DATETIME = GETDATE();
DECLARE @Duration INT = DATEDIFF(MILLISECOND, @StartTime, @EndTime);

PRINT 'وقت تنفيذ الاستعلامات الأساسية: ' + CAST(@Duration AS VARCHAR(10)) + ' ميلي ثانية';

IF @Duration < 1000
    PRINT '✓ الأداء جيد';
ELSE
    PRINT '⚠ الأداء قد يحتاج تحسين';

-- 11. ملخص النتائج
-- ===================================================================

PRINT '';
PRINT '===================================================================';
PRINT 'ملخص نتائج الاختبارات';
PRINT '===================================================================';

-- إحصائيات عامة
SELECT 
    'إجمالي الأقسام' as 'المؤشر',
    CAST(COUNT(*) AS VARCHAR(10)) as 'القيمة'
FROM Departments
WHERE IsActive = 1

UNION ALL

SELECT 
    'إجمالي المستخدمين' as 'المؤشر',
    CAST(COUNT(*) AS VARCHAR(10)) as 'القيمة'
FROM Users
WHERE IsActive = 1

UNION ALL

SELECT 
    'مدراء الأقسام' as 'المؤشر',
    CAST(COUNT(*) AS VARCHAR(10)) as 'القيمة'
FROM Users
WHERE UserType IN ('مدير القسم', 'مدير قسم') AND IsActive = 1

UNION ALL

SELECT 
    'المدراء العامين' as 'المؤشر',
    CAST(COUNT(*) AS VARCHAR(10)) as 'القيمة'
FROM Users
WHERE UserType = 'مدير' AND IsActive = 1;

-- توصيات
PRINT '';
PRINT 'التوصيات:';

IF @DeptCount = 0
    PRINT '- يجب إنشاء أقسام في النظام';

IF @ManagerCount < @DeptCount
    PRINT '- يجب تعيين مدراء لجميع الأقسام';

IF NOT EXISTS (SELECT * FROM Users WHERE UserType = 'مدير' AND IsActive = 1)
    PRINT '- يجب إنشاء مدير عام واحد على الأقل';

PRINT '';
PRINT '===================================================================';
PRINT 'انتهت اختبارات نظام فلترة الأقسام';
PRINT '===================================================================';
