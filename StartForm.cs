﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class StartForm : Form
    {
        public StartForm()
        {
            InitializeComponent();
            this.labelCopyright.Text = "جميع الحقوق محفوظة  © 2024-" + DateTime.Now.Year.ToString();
            this.StartPosition = FormStartPosition.CenterScreen;
        }

        private async void TimerStart_Tick(object sender, EventArgs e)
        {
            labelState.Text = "جاري التحقق من الاتصال...";
            timerStart.Enabled = false;

            progressBar1.Style = ProgressBarStyle.Marquee;
            progressBar1.MarqueeAnimationSpeed = 30;
            progressBar1.Visible = true;
            await Task.Delay(500); // انطباع بصري

            try
            {
                // تعيين نص الاتصال
                ConStringHelper.SetConString();

                // التحقق من وجود مستخدمين
                int userCount = await Task.Run(() =>
                {
                    using var conn = new SqlConnection(ConStringHelper.GetConnectionString());
                    conn.Open();

                    string query = "SELECT COUNT(*) FROM Users";
                    using var cmd = new SqlCommand(query, conn);
                    return Convert.ToInt32(cmd.ExecuteScalar());
                });
               
                if (userCount == 0)
                {
                    labelState.Text = "لا يوجد مستخدمون. سيتم فتح نموذج إضافة مستخدم...";
                    Application.DoEvents();
                    await Task.Delay(500);

                    var userForm = new UserForm
                    {
                        FromStartup = true,
                        IsFirstUserMode = true,
                        Text = "مدير",
                        StartPosition = FormStartPosition.CenterScreen
                    };
                    var result = userForm.ShowDialog();


                    if (result == DialogResult.OK && DatabaseHelper.HasUsers())
                    {
                        Application.Restart(); // ← إعادة التشغيل
                        return;
                    }
                    else
                    {
                        Application.Exit(); // ← الخروج بدون إضافة
                        return;
                    }
                }
                else
                {
                    labelState.Text = "تم العثور على مستخدمين. يتم فتح نموذج تسجيل الدخول...";
                    Application.DoEvents();
                    await Task.Delay(500);

                    using var loginForm = new LoginForm();
                    loginForm.ShowDialog();

                    this.Hide(); // ← إخفاء StartForm
                }
            }
            catch (Exception ex)
            {
                progressBar1.Visible = false;
                labelState.Text = "فشل الاتصال بقاعدة البيانات.";

                string message = "فشل الاتصال بقاعدة البيانات.\n\nهل تريد ضبط إعدادات الاتصال؟";

                var result = MessageBox.Show(
                    message,
                    "خطأ في الاتصال",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Error
                );

                if (result == DialogResult.Yes)
                {
                    using var settingsForm = new SettingsForm();
                    settingsForm.ShowDialog();

                    labelState.Text = "يرجى الانتظار... جاري إعادة التحقق.";
                    progressBar1.Visible = true;
                    await Task.Delay(500);
                    timerStart.Enabled = true;
                }
                else
                {
                    Application.Exit();
                }
            }
        }
    }
}


    


