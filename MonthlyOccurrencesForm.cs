using System;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using ClosedXML.Excel;
using DataTable = System.Data.DataTable;

namespace EmployeeManagementSystem
{
    public partial class MonthlyOccurrencesForm : Form
    {
        private DataTable currentReportData;
        private DataTable currentEmployeesData;
        private string currentUsername = string.Empty;
        private string currentUserFullName = string.Empty;
        private User? currentUser = null; // إضافة المستخدم الحالي

        public MonthlyOccurrencesForm()
        {
            InitializeComponent();
            SetCurrentUserInfo();
            InsertDefaultReportSettingsIfNotExists();

            // تسجيل فتح النموذج
            _ = Task.Run(async () => await ActivityLogHelper.LogFormAccessAsync("فتح تقرير الأحداث الشهرية"));
        }

        // Constructor جديد يستقبل المستخدم الحالي
        public MonthlyOccurrencesForm(User currentUser) : this()
        {
            this.currentUser = currentUser;

            // تم إزالة الرسالة المزعجة - الفلترة تعمل تلقائياً
           
        }

        public MonthlyOccurrencesForm(string username) : this()
        {
            currentUsername = username;
            GetCurrentUserInfo();
            
            // تسجيل إغلاق النموذج عند الإغلاق
            this.FormClosing += async (sender, e) => 
            {
                await ActivityLogHelper.LogFormAccessAsync("إغلاق تقرير الأحداث الشهرية");
            };
        }

        private void SetCurrentUserInfo()
        {
            // إذا لم يتم تمرير اسم المستخدم، استخدم اسم المستخدم الحالي في Windows
            if (string.IsNullOrEmpty(currentUsername))
            {
                currentUsername = Environment.UserName;
            }
            GetCurrentUserInfo();
        }

        private void GetCurrentUserInfo()
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string query = "SELECT FullName FROM Users WHERE Username = @username";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@username", currentUsername);
                        var result = command.ExecuteScalar();
                        if (result != null)
                        {
                            currentUserFullName = result.ToString();
                        }
                        else
                        {
                            // إذا لم يتم العثور على المستخدم، استخدم اسم المستخدم كاسم كامل
                            currentUserFullName = currentUsername;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، استخدم اسم المستخدم كاسم كامل
                currentUserFullName = currentUsername;
            }
        }

        private string GetCurrentLocation()
        {
            try
            {
                // يمكن تخصيص هذه الدالة للحصول على الموقع من:
                // - إعدادات النظام
                // - معلومات الشبكة
                // - أو قيمة ثابتة
                
                // محاولة الحصول على الموقع من إعدادات النظام أولاً
                string location = GetLocationFromSettings();
                if (!string.IsNullOrEmpty(location))
                {
                    return location;
                }
                
                // إذا لم يتم العثور على موقع في الإعدادات، استخدم اسم الجهاز ومعلومات إضافية
                string machineName = Environment.MachineName;
                string userDomain = Environment.UserDomainName;
                
                if (userDomain != machineName)
                {
                    return $"{machineName} ({userDomain})";
                }
                
                return machineName;
            }
            catch
            {
                return "غير محدد";
            }
        }

        private string GetLocationFromSettings()
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string query = "SELECT TOP 1 CompanyName FROM Settings";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        var result = command.ExecuteScalar();
                        return result?.ToString() ?? "";
                    }
                }
            }
            catch
            {
                return "";
            }
        }

        private void InsertDefaultReportSettingsIfNotExists()
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();

                    // فحص وجود بيانات في الجدول
                    string checkDataQuery = "SELECT COUNT(*) FROM ReportSettings";

                    using (SqlCommand checkCommand = new SqlCommand(checkDataQuery, connection))
                    {
                        int dataCount = Convert.ToInt32(checkCommand.ExecuteScalar());

                        if (dataCount == 0)
                        {
                            // إدراج البيانات الافتراضية
                            string insertDefaultQuery = @"
                                        INSERT INTO ReportSettings (ArabicHeaderText, EnglishHeaderText, ReportTitle, ManagerName, SecurityOfficerName, CommanderName, ModifiedBy, DirectorateName)
                                        VALUES (
                                            N'جـمهوية العـراق
        رئـاسـة الـوزراء 
        هـيأة الحـشــد الشــعبـي
        الـدائرة الادارية و الـمـاليـة الـعـامـة',
                                            'Republic Of Iraq
        Prime Minister
        Popular Mobilization Commission
        Administrative & Public Finance Department',
                                            N'جدول الحضور والغياب',
                                            N'مدير الادارة',
                                            N'ضابط الامن',
                                            N'امر التشكيل',
                                            N'النظام',
                                            N'اسم المديرية او التشكيل'
                                        )";

                            using (SqlCommand insertCommand = new SqlCommand(insertDefaultQuery, connection))
                            {
                                insertCommand.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، لا نظهر رسالة خطأ حتى لا نزعج المستخدم
                System.Diagnostics.Debug.WriteLine($"خطأ في إدراج البيانات الافتراضية: {ex.Message}");
            }
        }


        private ReportSettings GetReportSettings()
        {
            var defaultSettings = new ReportSettings
            {
                ArabicHeaderText = "جـمهوية العـراق\nرئـاسـة الـوزراء\nهـيأة الحـشــد الشــعبـي\nالـدائرة الادارية و الـمـاليـة الـعـامـة",
                EnglishHeaderText = "Republic Of Iraq\nPrime Minister\nPopular Mobilization Commission\nAdministrative & Public Finance Department",
                ReportTitle = "جدول الحضور والغياب",
                DirectorateName = "اسم المديرية او التشكيل",
                ManagerName = "مدير الادارة",
                SecurityOfficerName = "ضابط الامن",
                CommanderName = "امر التشكيل"
            };

            try
            {
                using (SqlConnection connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string query = @"
    SELECT TOP 1 ArabicHeaderText, EnglishHeaderText, CompanyLogo, ReportTitle, 
           ManagerName, SecurityOfficerName, CommanderName, DirectorateName
    FROM ReportSettings 
    ORDER BY Id DESC";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            defaultSettings.ArabicHeaderText = reader["ArabicHeaderText"]?.ToString() ?? defaultSettings.ArabicHeaderText;
                            defaultSettings.EnglishHeaderText = reader["EnglishHeaderText"]?.ToString() ?? defaultSettings.EnglishHeaderText;
                            defaultSettings.CompanyLogo = reader["CompanyLogo"]?.ToString() ?? "";
                            defaultSettings.ReportTitle = reader["ReportTitle"]?.ToString() ?? defaultSettings.ReportTitle;
                            defaultSettings.ManagerName = reader["ManagerName"]?.ToString() ?? defaultSettings.ManagerName;
                            defaultSettings.SecurityOfficerName = reader["SecurityOfficerName"]?.ToString() ?? defaultSettings.SecurityOfficerName;
                            defaultSettings.CommanderName = reader["CommanderName"]?.ToString() ?? defaultSettings.CommanderName;
                            defaultSettings.DirectorateName = reader["DirectorateName"]?.ToString() ?? "";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب إعدادات التقرير: {ex.Message}");
            }

            return defaultSettings;
        }

        private void AddReportHeader(IXLWorksheet worksheet, int totalColumns, int selectedMonth, int selectedYear, ReportSettings settings)
        {
            try
            {
               
                var arabicHeaderCell = worksheet.Cell(1, 1);
                arabicHeaderCell.Value = settings.ArabicHeaderText;
                var arabicRange = worksheet.Range(1, 1, 5, Math.Min(5, totalColumns - 1)); // غيّر إلى اليسار
                arabicRange.Merge();
                arabicRange.Style.Font.FontSize = 14;
                arabicRange.Style.Font.Bold = true;
                arabicRange.Style.Font.FontName = "Arial";
                arabicRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center; // لأن النص عربي
                arabicRange.Style.Alignment.Vertical = XLAlignmentVerticalValues.Top;
                arabicRange.Style.Alignment.WrapText = true;
                arabicRange.Style.Fill.BackgroundColor = XLColor.White;
                arabicRange.Style.Border.OutsideBorder = XLBorderStyleValues.None;

                // النص الإنجليزي في أعلى اليسار (بدون خطوط)
                var englishHeaderCell = worksheet.Cell(1, Math.Max(1, totalColumns - 5));
                englishHeaderCell.Value = settings.EnglishHeaderText;
                var englishRange = worksheet.Range(1, Math.Max(1, totalColumns - 5), 5, totalColumns);
                englishRange.Merge();
                englishRange.Style.Font.FontSize = 14;
                englishRange.Style.Font.Bold = true;
                englishRange.Style.Font.FontName = "Arial";
                englishRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center; // لأن النص إنجليزي
                englishRange.Style.Alignment.Vertical = XLAlignmentVerticalValues.Top;
                englishRange.Style.Alignment.WrapText = true;
                englishRange.Style.Fill.BackgroundColor = XLColor.White;
                englishRange.Style.Border.OutsideBorder = XLBorderStyleValues.None;

               

                // الشعار في المنتصف (إذا كان متوفراً)
                if (!string.IsNullOrEmpty(settings.CompanyLogo) && System.IO.File.Exists(settings.CompanyLogo))
                {
                    try
                    {
                        var picture = worksheet.AddPicture(settings.CompanyLogo);
                        picture.MoveTo(worksheet.Cell(1, (totalColumns / 2) - 1));
                        picture.Scale(0.4); // تحسين حجم الصورة
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"خطأ في إضافة الشعار: {ex.Message}");
                    }
                }

                // اسم المديرية / التشكيل أسفل النص الإنجليزي مباشرة (صف 6 و 7)
                var directorateCell = worksheet.Cell(6, Math.Max(1, totalColumns - 5));
                directorateCell.Value = settings.DirectorateName;
                var directorateRange = worksheet.Range(6, Math.Max(1, totalColumns - 5), 7, totalColumns);
                directorateRange.Merge();
                directorateRange.Style.Font.FontSize = 14;
                directorateRange.Style.Font.FontName = "Arial";
                directorateRange.Style.Font.Bold = true;
                directorateRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                directorateRange.Style.Alignment.Vertical = XLAlignmentVerticalValues.Top;
                directorateRange.Style.Alignment.WrapText = true;
                directorateRange.Style.Fill.BackgroundColor = XLColor.White;
                directorateRange.Style.Border.OutsideBorder = XLBorderStyleValues.None;

                // العنوان الرئيسي فوق أعمدة الأيام مباشرة (صف 8 ملاصق للأعمدة)
                string monthName = GetMonthName(selectedMonth);
                var titleCell = worksheet.Cell(8, 1);
                titleCell.Value = $"{settings.ReportTitle} لشـهر {monthName} لسـنة {selectedYear}";
                var titleRange = worksheet.Range(8, 1, 8, totalColumns);
                titleRange.Merge();
                titleRange.Style.Font.Bold = true;
                titleRange.Style.Font.FontSize = 14;
                titleRange.Style.Font.FontName = "Arial";
                titleRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                titleRange.Style.Fill.BackgroundColor = XLColor.White;
                titleRange.Style.Border.OutsideBorder = XLBorderStyleValues.None;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة هيدر التقرير: {ex.Message}");
            }
        }

        private string GetMonthName(int monthNumber)
        {
            string[] monthNames = {
                "", "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
            };
            
            return monthNumber >= 1 && monthNumber <= 12 ? monthNames[monthNumber] : monthNumber.ToString();
        }

        private void AddReportFooter(IXLWorksheet worksheet, int totalColumns, int startFooterRow, ReportSettings settings)
        {
            try
            {
                // تعيين إعدادات الصفحة للطباعة
                worksheet.PageSetup.PageOrientation = XLPageOrientation.Landscape;
                worksheet.PageSetup.PaperSize = XLPaperSize.A4Paper;
                worksheet.PageSetup.Margins.Top = 0.75;
                worksheet.PageSetup.Margins.Bottom = 1.0;
                worksheet.PageSetup.Margins.Left = 0.25;
                worksheet.PageSetup.Margins.Right = 0.25;
                worksheet.PageSetup.Header.Left.AddText("");
                worksheet.PageSetup.Header.Center.AddText("");
                worksheet.PageSetup.Header.Right.AddText("");
                worksheet.PageSetup.Footer.Left.AddText($"اسم وتوقيع وختم {settings.CommanderName}");
                worksheet.PageSetup.Footer.Center.AddText($"اسم وتوقيع {settings.SecurityOfficerName}");
                worksheet.PageSetup.Footer.Right.AddText($"اسم وتوقيع {settings.ManagerName}");
                //// إنشاء تذييل مخصص للتوقيعات
                //int footerStartRow = startFooterRow + 4;

                //// خط فاصل قبل التوقيعات
                //var separatorRange = worksheet.Range(footerStartRow - 1, 1, footerStartRow - 1, totalColumns);
                //separatorRange.Style.Border.TopBorder = XLBorderStyleValues.Medium;
                //separatorRange.Style.Border.TopBorderColor = XLColor.DarkBlue;

                //// حساب عرض كل قسم للتوقيعات
                //int sectionWidth = totalColumns / 3;
                //int leftStart = 1;
                //int centerStart = sectionWidth + 1;
                //int rightStart = (sectionWidth * 2) + 1;

                //// إضافة مسافة للتوقيعات
                //int signatureRows = 4;

                //// التوقيع الأيمن - مدير الإدارة
                //var managerCell = worksheet.Cell(footerStartRow, rightStart);
                //managerCell.Value = $"اسم وتوقيع {settings.ManagerName}";
                //var managerRange = worksheet.Range(footerStartRow, rightStart, footerStartRow + signatureRows - 1, totalColumns);
                //managerRange.Merge();
                //managerRange.Style.Font.Bold = true;
                //managerRange.Style.Font.FontSize = 12;
                //managerRange.Style.Font.FontName = "Arial";
                //managerRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                //managerRange.Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;
                //managerRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                //managerRange.Style.Fill.BackgroundColor = XLColor.LightGray;

                //// التوقيع الأوسط - ضابط الأمن
                //var securityCell = worksheet.Cell(footerStartRow, centerStart);
                //securityCell.Value = $"اسم وتوقيع {settings.SecurityOfficerName}";
                //var securityRange = worksheet.Range(footerStartRow, centerStart, footerStartRow + signatureRows - 1, rightStart - 1);
                //securityRange.Merge();
                //securityRange.Style.Font.Bold = true;
                //securityRange.Style.Font.FontSize = 12;
                //securityRange.Style.Font.FontName = "Arial";
                //securityRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                //securityRange.Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;
                //securityRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                //securityRange.Style.Fill.BackgroundColor = XLColor.LightGray;

                //// التوقيع الأيسر - آمر التشكيل
                //var commanderCell = worksheet.Cell(footerStartRow, leftStart);
                //commanderCell.Value = $"اسم وتوقيع وختم {settings.CommanderName}";
                //var commanderRange = worksheet.Range(footerStartRow, leftStart, footerStartRow + signatureRows - 1, centerStart - 1);
                //commanderRange.Merge();
                //commanderRange.Style.Font.Bold = true;
                //commanderRange.Style.Font.FontSize = 12;
                //commanderRange.Style.Font.FontName = "Arial";
                //commanderRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                //commanderRange.Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;
                //commanderRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                //commanderRange.Style.Fill.BackgroundColor = XLColor.LightGray;

                //// إضافة نص تعريفي للتوقيعات
                //var signatureLabel = worksheet.Cell(footerStartRow - 2, 1);
                //signatureLabel.Value = "التوقيعات والموافقات";
                //var signatureLabelRange = worksheet.Range(footerStartRow - 2, 1, footerStartRow - 2, totalColumns);
                //signatureLabelRange.Merge();
                //signatureLabelRange.Style.Font.Bold = true;
                //signatureLabelRange.Style.Font.FontSize = 12;
                //signatureLabelRange.Style.Font.FontName = "Arial";
                //signatureLabelRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                //signatureLabelRange.Style.Fill.BackgroundColor = XLColor.DarkBlue;
                //signatureLabelRange.Style.Font.FontColor = XLColor.White;

                // إضافة تذييل الصفحة المخصص للطباعة
                //var pageFooter = worksheet.PageSetup.Footer;
                //pageFooter.Left.AddText($"اسم وتوقيع وختم {settings.CommanderName}");
                //pageFooter.Center.AddText($"اسم وتوقيع {settings.SecurityOfficerName}");
                //pageFooter.Right.AddText($"اسم وتوقيع {settings.ManagerName}");


            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة تذييل التقرير: {ex.Message}");
            }
        }

        private void MonthlyOccurrencesForm_Load(object sender, EventArgs e)
        {
            InitializeForm();
            UIHelper.ShowEmptyMessage(dataGridViewOccurrences, lbl_NoDocuments, "لا توجد بيانات وقوعات");
            UpdateNoDataLabel();
            
            // إلغاء تفعيل أزرار الوجبات السريعة في البداية
            EnableQuickShiftButtons(false);
        }

        private void InitializeForm()
        {
            // إعداد ComboBox للشهور
            SetupMonthComboBox();

            // إعداد ComboBox للسنوات
            SetupYearComboBox();

            // إعداد DataGridView
            SetupDataGridView();

            // إعداد البحث
            SetupSearch();

            // إعداد المجموعات
            SetupGroupComboBox();

            // إعداد نوع الحضور
            SetupAttendanceTypeComboBox();

            // إضافة زر المساعدة إذا كان موجود
            SetupHelpButton();
        }

        private void SetupMonthComboBox()
        {
            cmbMonth.Items.Clear();

            // إضافة الشهور بالعربية
            string[] arabicMonths = {
                "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            };

            for (int i = 0; i < arabicMonths.Length; i++)
            {
                cmbMonth.Items.Add(new ComboBoxItem
                {
                    Text = arabicMonths[i],
                    Value = i + 1
                });
            }

            // تحديد الشهر الحالي
            DateTime currentMonth = DateTime.Now;
            cmbMonth.SelectedIndex = currentMonth.Month - 1;
            
            // إضافة حدث لتحديث التواريخ عند تغيير الشهر
            cmbMonth.SelectedIndexChanged += (s, e) => {
                if (cmbYear.SelectedItem != null)
                    UpdateDateRangeControls();
            };
        }

        private void SetupYearComboBox()
        {
            cmbYear.Items.Clear();

            // إضافة السنوات من 2020 إلى السنة الحالية + 2
            int currentYear = DateTime.Now.Year;
            for (int year = 2020; year <= currentYear + 2; year++)
            {
                cmbYear.Items.Add(year);
            }

            // تحديد السنة الحالية
            DateTime currentMonth = DateTime.Now;
            cmbYear.SelectedItem = currentMonth.Year;
            
            // إضافة حدث لتحديث التواريخ عند تغيير السنة
            cmbYear.SelectedIndexChanged += (s, e) => {
                if (cmbMonth.SelectedItem != null)
                    UpdateDateRangeControls();
            };
        }

        private void SetupDataGridView()
        {
            dataGridViewOccurrences.AutoGenerateColumns = false;
            dataGridViewOccurrences.AllowUserToAddRows = false;
            dataGridViewOccurrences.AllowUserToDeleteRows = false;
            dataGridViewOccurrences.ReadOnly = false; // السماح بتعديل خانات الاختيار
            dataGridViewOccurrences.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridViewOccurrences.MultiSelect = true; // تفعيل التحديد المتعدد
            dataGridViewOccurrences.BackgroundColor = Color.White;
            dataGridViewOccurrences.BorderStyle = BorderStyle.Fixed3D;
            dataGridViewOccurrences.CellBorderStyle = DataGridViewCellBorderStyle.Single;
            dataGridViewOccurrences.GridColor = Color.Gray;
            dataGridViewOccurrences.DefaultCellStyle.BackColor = Color.White;
            dataGridViewOccurrences.DefaultCellStyle.ForeColor = Color.Black;
            dataGridViewOccurrences.DefaultCellStyle.SelectionBackColor = Color.LightBlue;
            dataGridViewOccurrences.DefaultCellStyle.SelectionForeColor = Color.Black;
            dataGridViewOccurrences.ColumnHeadersDefaultCellStyle.BackColor = Color.Navy;
            dataGridViewOccurrences.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dataGridViewOccurrences.ColumnHeadersDefaultCellStyle.Font = new Font("Cairo", 10, FontStyle.Bold);
            dataGridViewOccurrences.DefaultCellStyle.Font = new Font("Cairo", 10);
            dataGridViewOccurrences.RightToLeft = RightToLeft.Yes;

            // إضافة أحداث مراقبة التغييرات
            dataGridViewOccurrences.CellContentClick += DataGridViewOccurrences_CellContentClick;
            dataGridViewOccurrences.CellValidating += DataGridViewOccurrences_CellValidating;
            dataGridViewOccurrences.CellValueChanged += DataGridViewOccurrences_CellValueChanged;


        }
       
        private async void btnGenerateExcel_Click(object sender, EventArgs e)
        {
            if (cmbGroup.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المجموعة أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (cmbMonth.SelectedItem == null || cmbYear.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الشهر والسنة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            // تسجيل عملية تصدير Excel
            await ActivityLogHelper.LogExportAsync("تصدير تقرير الأحداث الشهرية إلى Excel",
                "MonthlyOccurrences",
                dataGridViewOccurrences.Rows.Count,
                $"المجموعة: {cmbGroup.SelectedItem}, الشهر: {cmbMonth.SelectedItem}, السنة: {cmbYear.SelectedItem}");

            try
            {
                GenerateMonthlyReport();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء توليد التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void GenerateMonthlyReport()
        {
            int selectedMonth = ((ComboBoxItem)cmbMonth.SelectedItem).Value;
            int selectedYear = (int)cmbYear.SelectedItem;
            string selectedGroup = cmbGroup.SelectedItem.ToString();
            
            // تسجيل عملية إنشاء التقرير
            await ActivityLogHelper.LogReportGenerationAsync("إنشاء تقرير الأحداث الشهرية", 
                $"الشهر: {selectedMonth}, السنة: {selectedYear}, المجموعة: {selectedGroup}");

            // إنشاء DataTable جديد للعرض
            currentReportData = new DataTable();
            currentReportData.Columns.Add("EmployeeCode", typeof(string));
            currentReportData.Columns.Add("Name", typeof(string));
            currentReportData.Columns.Add("KeyCardNumber", typeof(string));

            // إضافة أعمدة الأيام
            int daysInMonth = DateTime.DaysInMonth(selectedYear, selectedMonth);
            for (int day = 1; day <= daysInMonth; day++)
            {
                currentReportData.Columns.Add($"day_{day:D2}", typeof(string));
            }

            // إضافة عمود الملاحظات
            currentReportData.Columns.Add("Notes", typeof(string));

            // جلب بيانات الموظفين من المجموعة المحددة
            DataTable employees = GetEmployeesByGroup(selectedGroup);

            if (employees != null && employees.Rows.Count > 0)
            {
                foreach (DataRow employee in employees.Rows)
                {
                    DataRow newRow = currentReportData.NewRow();
                    newRow["EmployeeCode"] = employee["EmployeeCode"];
                    newRow["Name"] = employee["Name"];
                    newRow["KeyCardNumber"] = employee["KeyCardNumber"] ?? "";

                    // محاولة جلب الوقوعات المحفوظة مسبقاً
                    DataRow existingData = GetEmployeeOccurrences(employee["EmployeeCode"].ToString(), selectedMonth, selectedYear);
                    
                    // ملء أعمدة الأيام
                    for (int day = 1; day <= daysInMonth; day++)
                    {
                        string dayColumn = $"day_{day:D2}";
                        if (existingData != null && existingData.Table.Columns.Contains(dayColumn))
                        {
                            newRow[dayColumn] = existingData[dayColumn] ?? "";
                        }
                        else
                        {
                            newRow[dayColumn] = "";
                        }
                    }

                    // إضافة الملاحظات
                    if (existingData != null && existingData.Table.Columns.Contains("note"))
                    {
                        newRow["Notes"] = existingData["note"] ?? "";
                    }
                    else
                    {
                        newRow["Notes"] = "";
                    }

                    currentReportData.Rows.Add(newRow);
                }

                DisplayDataInGrid(selectedMonth, selectedYear);
                btnExport.Enabled = true;
                UIHelper.ShowEmptyMessage(dataGridViewOccurrences, lbl_NoDocuments, "");
                
                // تحديث عناصر النطاق الزمني
                UpdateDateRangeControls();
                
                // تفعيل أزرار الوجبات السريعة
                EnableQuickShiftButtons(true);
                
                // تحديث عنوان النموذج ليشمل الشهر والسنة
                this.Text = $"تقرير الوقوعات الشهرية - {cmbMonth.Text} {cmbYear.SelectedItem}";
            }
            else
            {
                dataGridViewOccurrences.DataSource = null;
                dataGridViewOccurrences.Columns.Clear();
                btnExport.Enabled = false;
                this.Text = "تقرير الوقوعات الشهرية";
                UIHelper.ShowEmptyMessage(dataGridViewOccurrences, lbl_NoDocuments, "لا توجد بيانات موظفين للمجموعة المحددة");
                
                // إلغاء تفعيل أزرار الوجبات السريعة
                EnableQuickShiftButtons(false);
            }
        }

  
        private DataTable GetEmployeesByGroup(string groupName)
        {
            DataTable employees = new DataTable();

            try
            {
                using (SqlConnection connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string query;

                    if (groupName == "جميع الموظفين")
                    {
                        // تطبيق فلترة القسم للموظفين
                        if (EmployeeDepartmentHelper.IsDepartmentManager(currentUser))
                        {
                            int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                            if (managedDepartmentId.HasValue)
                            {
                                query = @"SELECT
                                e.EmployeeCode,
                                e.Name,
                                e.KeyCardNumber
                            FROM Employees e
                            INNER JOIN Users u ON e.EmployeeCode = u.UserId
                            WHERE u.DepartmentId = @DepartmentId AND u.IsActive = 1
                            ORDER BY e.EmployeeCode";
                            }
                            else
                            {
                                // مدير قسم بدون قسم - إرجاع جدول فارغ
                                return new DataTable();
                            }
                        }
                        else
                        {
                            query = @"SELECT
                            EmployeeCode,
                            Name,
                            KeyCardNumber
                        FROM Employees
                        ORDER BY EmployeeCode";
                        }
                    }
                    else
                    {
                        query = @"SELECT 
                        wgm.EmployeeCode,
                        wgm.EmployeeName as Name,
                        wgm.KeyCardNumber
                    FROM WorkGroupMembers wgm
                    INNER JOIN WorkGroups wg ON wgm.GroupId = wg.GroupId
                    WHERE wg.GroupName = @groupName AND wg.IsActive = 1
                    ORDER BY 
                        CASE wgm.MemberType 
                            WHEN 'عمل' THEN 1 
                            WHEN 'إجازة' THEN 2 
                            ELSE 3 
                        END,
                        wgm.MemberId";
                    }

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        if (groupName != "جميع الموظفين")
                        {
                            command.Parameters.AddWithValue("@groupName", groupName);
                        }

                        // إضافة معامل DepartmentId إذا كان مدير قسم
                        if (groupName == "جميع الموظفين" && EmployeeDepartmentHelper.IsDepartmentManager(currentUser))
                        {
                            int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                            if (managedDepartmentId.HasValue)
                            {
                                command.Parameters.AddWithValue("@DepartmentId", managedDepartmentId.Value);
                            }
                        }

                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(employees);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في جلب بيانات الموظفين: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }

            return employees;
        }

        private DataRow GetEmployeeOccurrences(string employeeCode, int month, int year)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string query = @"SELECT * FROM add_geab 
                                   WHERE id_sub_employees = @employeeCode 
                                   AND month_add = @month 
                                   AND year_add = @year";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@employeeCode", employeeCode);
                        command.Parameters.AddWithValue("@month", month);
                        command.Parameters.AddWithValue("@year", year);

                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            DataTable data = new DataTable();
                            adapter.Fill(data);
                            
                            if (data.Rows.Count > 0)
                            {
                                return data.Rows[0];
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // في حالة خطأ، إرجاع null
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب وقوعات الموظف {employeeCode}: {ex.Message}");
            }

            return null;
        }
        private void DisplayDataInGrid(int month, int year)
        {
            dataGridViewOccurrences.Columns.Clear();

            // إضافة عمود تحديد
            DataGridViewCheckBoxColumn checkColumn = new DataGridViewCheckBoxColumn
            {
                Name = "Selected",
                HeaderText = "تحديد",
                Width = 60,
                TrueValue = true,
                FalseValue = false,
                IndeterminateValue = false,
                ThreeState = false,
                ReadOnly = false
            };
            dataGridViewOccurrences.Columns.Add(checkColumn);

            // إضافة العمودين الأساسيين
            dataGridViewOccurrences.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "EmployeeCode",
                HeaderText = "الرقم الوظيفي",
                DataPropertyName = "EmployeeCode",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            dataGridViewOccurrences.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Name",
                HeaderText = "اسم الموظف",
                DataPropertyName = "Name",
                Width = 200,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            dataGridViewOccurrences.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "KeyCardNumber",
                HeaderText = "رقم الكي كارد",
                DataPropertyName = "KeyCardNumber",
                Width = 100,
                ReadOnly = true,
                DefaultCellStyle = new DataGridViewCellStyle { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // عدد أيام الشهر
            int daysInMonth = DateTime.DaysInMonth(year, month);

            // أعمدة الأيام - قابلة للتعديل
            for (int day = 1; day <= daysInMonth; day++)
            {
                dataGridViewOccurrences.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = $"Day{day:D2}",
                    HeaderText = day.ToString(),
                    DataPropertyName = $"day_{day:D2}",
                    Width = 30,
                    ReadOnly = false, // السماح بالتعديل
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Alignment = DataGridViewContentAlignment.MiddleCenter,
                        Font = new Font("Arial", 8, FontStyle.Bold)
                    }
                });
            }

            // إضافة عمود الملاحظات
            dataGridViewOccurrences.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Notes",
                HeaderText = "الملاحظات",
                DataPropertyName = "Notes",
                Width = 200,
                ReadOnly = false, // السماح بالتعديل
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleLeft,
                    Font = new Font("Arial", 9),
                    BackColor = Color.LightYellow
                }
            });

            // ربط البيانات
            dataGridViewOccurrences.DataSource = currentReportData;

            // تنسيق الخلايا
            ApplyCellFormatting(daysInMonth);

            // إضافة تعليمات للمستخدم
            AddTooltipsToDataGrid();
        }

        private void ApplyCellFormatting(int daysInMonth)
        {
            foreach (DataGridViewRow row in dataGridViewOccurrences.Rows)
            {
                for (int day = 1; day <= daysInMonth; day++)
                {
                    string columnName = $"Day{day:D2}";
                    if (dataGridViewOccurrences.Columns.Contains(columnName))
                    {
                        var cell = row.Cells[columnName];
                        string value = cell.Value?.ToString() ?? "";

                        // تطبيق الألوان حسب نوع الوقوع
                        switch (value.ToUpper())
                        {
                            case "V": // حضور إنجليزي
                                cell.Style.BackColor = Color.Chocolate;
                                cell.Style.ForeColor = Color.Black;
                                break;

                            case "غ": // غياب
                                cell.Style.BackColor = Color.Yellow;
                                cell.Style.ForeColor = Color.Black;
                                break;

                            case "ت": // هروب
                                cell.Style.BackColor = Color.Red;
                                cell.Style.ForeColor = Color.Black;
                                break;

                            case "ج": // إجازة
                                cell.Style.BackColor = Color.DodgerBlue;
                                cell.Style.ForeColor = Color.Black;
                                break;
                            case "م": // مرضية
                                cell.Style.BackColor = Color.LightGreen;
                                cell.Style.ForeColor = Color.Black;
                                break;
                            case "د": // دورة
                                cell.Style.BackColor = Color.RoyalBlue;
                                cell.Style.ForeColor = Color.Black;
                                break;
                            case "و": // واجب
                                cell.Style.BackColor = Color.ForestGreen;
                                cell.Style.ForeColor = Color.Black;
                                break;
                            case "":
                            case " ":
                                cell.Style.BackColor = Color.WhiteSmoke;
                                cell.Style.ForeColor = Color.Gray;
                                break;
                            default:
                                cell.Style.BackColor = Color.LightYellow;
                                cell.Style.ForeColor = Color.Black;
                                break;
                        }
                    }
                }
            }
        }

        private void btnExport_Click(object sender, EventArgs e)
        {
            if (dataGridViewOccurrences.Rows.Count == 0)
            {
                MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // عد البيانات المحفوظة
            int rowsWithData = 0;
            foreach (DataGridViewRow row in dataGridViewOccurrences.Rows)
            {
                if (!row.IsNewRow)
                {
                    bool hasData = false;
                    for (int i = 4; i < dataGridViewOccurrences.Columns.Count; i++)
                    {
                        if (dataGridViewOccurrences.Columns[i].Name.StartsWith("Day"))
                        {
                            string value = row.Cells[i].Value?.ToString() ?? "";
                            if (!string.IsNullOrEmpty(value))
                            {
                                hasData = true;
                                break;
                            }
                        }
                    }
                    if (hasData) rowsWithData++;
                }
            }

            if (rowsWithData == 0)
            {
                if (MessageBox.Show("لا توجد وقوعات محفوظة. هل تريد تصدير القائمة الفارغة؟", 
                    "تأكيد التصدير", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
                {
                    return;
                }
            }

            try
            {
                ExportToExcel();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء التصدير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnReportSettings_Click(object sender, EventArgs e)
        {
            try
            {
                var settingsForm = new ReportSettingsForm();
                if (settingsForm.ShowDialog() == DialogResult.OK)
                {
                    MessageBox.Show("تم تحديث إعدادات التقرير بنجاح!\nسيتم تطبيق الإعدادات الجديدة في التقارير القادمة.", 
                        "تحديث الإعدادات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح إعدادات التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void ExportToExcel()
        {
            SaveFileDialog saveFileDialog = new SaveFileDialog()
            {
                Filter = "Excel Files|*.xlsx",
                Title = "حفظ تقرير الوقوعات الشهرية",
                FileName = $"تقرير_الوقوعات_{cmbMonth.Text}_{cmbYear.Text}.xlsx"
            };

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    using (var workbook = new XLWorkbook())
                    {
                        var worksheet = workbook.Worksheets.Add("تقرير الوقوعات الشهرية");
                        int lastColumn = 36; // AJ = 36
                        var clearRange = worksheet.Range(1, 1, 7, lastColumn);
                        clearRange.Style.Fill.BackgroundColor = XLColor.White;
                        clearRange.Style.Border.OutsideBorder = XLBorderStyleValues.None;
                        clearRange.Style.Border.InsideBorder = XLBorderStyleValues.None;

                        // حساب عدد الأعمدة
                        int selectedMonth = ((ComboBoxItem)cmbMonth.SelectedItem).Value;
                        int selectedYear = (int)cmbYear.SelectedItem;
                        int daysInMonth = DateTime.DaysInMonth(selectedYear, selectedMonth);
                        int totalColumns = 3 + daysInMonth + 3; // 3 أعمدة أساسية + أيام الشهر + 3 إحصائيات (حضور، غياب، ملاحظات)

                        // الحصول على إعدادات التقرير
                        var reportSettings = GetReportSettings();

                        // إضافة الهيدر المخصص
                        AddReportHeader(worksheet, totalColumns, selectedMonth, selectedYear, reportSettings);

                        // تطبيق RTL على الورقة
                        worksheet.RightToLeft = true;
                        
                        // إضافة رؤوس الأعمدة (بدء من الصف 9 بعد الهيدر المحسن)
                        int headerRowIndex = 9;
                        int colIndex = 1;
                        worksheet.Cell(headerRowIndex, colIndex++).Value = "ت";
                        worksheet.Cell(headerRowIndex, colIndex++).Value = "اسم الموظف";
                        worksheet.Cell(headerRowIndex, colIndex++).Value = "رقم الكي كارد";

                        // أعمدة الأيام
                        for (int day = 1; day <= daysInMonth; day++)
                        {
                            worksheet.Cell(headerRowIndex, colIndex++).Value = day.ToString();
                        }

                        // أعمدة الإحصائيات
                        worksheet.Cell(headerRowIndex, colIndex++).Value = "أيام الحضور";
                        worksheet.Cell(headerRowIndex, colIndex++).Value = "أيام الغياب";
                        worksheet.Cell(headerRowIndex, colIndex++).Value = "الملاحظات";

                        // تنسيق رؤوس الأعمدة
                        var headerRange = worksheet.Range(headerRowIndex, 1, headerRowIndex, totalColumns);
                        headerRange.Style.Font.Bold = true;
                        //headerRange.Style.Fill.BackgroundColor = XLColor.Navy;
                        headerRange.Style.Fill.BackgroundColor = XLColor.FromTheme(XLThemeColor.Accent1, 0.4);
                        headerRange.Style.Font.FontColor = XLColor.Black;
                        headerRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                        headerRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thick;
                        headerRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

                        // إضافة البيانات من DataGridView بدلاً من currentReportData لضمان تصدير البيانات المحدثة
                        int rowIndex = headerRowIndex + 1; // بدء البيانات من الصف التالي لرؤوس الأعمدة
                        foreach (DataGridViewRow gridRow in dataGridViewOccurrences.Rows)
                        {
                            if (gridRow.IsNewRow) continue;

                            colIndex = 1;

                            // البيانات الأساسية
                            worksheet.Cell(rowIndex, colIndex++).Value = rowIndex - headerRowIndex; // تسلسل بدلاً من الرقم الوظيفي
                            worksheet.Cell(rowIndex, colIndex++).Value = gridRow.Cells["Name"].Value?.ToString() ?? "";
                            worksheet.Cell(rowIndex, colIndex++).Value = gridRow.Cells["KeyCardNumber"].Value?.ToString() ?? "";

                            // بيانات الأيام مع الإحصائيات
                            int presentDays = 0, absentDays = 0, escapeDays = 0, vacationDays = 0;

                            for (int day = 1; day <= daysInMonth; day++)
                            {
                                string dayColumnName = $"Day{day:D2}";
                                string dayValue = "";
                                
                                if (dataGridViewOccurrences.Columns.Contains(dayColumnName))
                                {
                                    dayValue = gridRow.Cells[dayColumnName].Value?.ToString() ?? "";
                                }

                                var dayCell = worksheet.Cell(rowIndex, colIndex++);
                                dayCell.Value = dayValue;

                                // تطبيق التنسيق والإحصائيات
                                switch (dayValue.ToUpper())
                                {
                                    case "V":
                                        dayCell.Style.Fill.BackgroundColor = XLColor.Chocolate;
                                        dayCell.Style.Font.FontColor = XLColor.Black;
                                        presentDays++;
                                        break;
                                    case "غ":
                                        dayCell.Style.Fill.BackgroundColor = XLColor.Yellow;
                                        dayCell.Style.Font.FontColor = XLColor.Black;
                                        absentDays++;
                                        break;
                                    case "ت":
                                        dayCell.Style.Fill.BackgroundColor = XLColor.Red;
                                        dayCell.Style.Font.FontColor = XLColor.Black;
                                        escapeDays++;
                                        break;
                                    case "ج":
                                        dayCell.Style.Fill.BackgroundColor = XLColor.DodgerBlue;
                                        dayCell.Style.Font.FontColor = XLColor.Black;
                                        vacationDays++;
                                        break;
                                    case "م":
                                        dayCell.Style.Fill.BackgroundColor = XLColor.LightGreen;
                                        dayCell.Style.Font.FontColor = XLColor.Black;
                                        vacationDays++;
                                        break;
                                    case "د":
                                        dayCell.Style.Fill.BackgroundColor = XLColor.RoyalBlue;
                                        dayCell.Style.Font.FontColor = XLColor.Black;
                                        vacationDays++;
                                        break;
                                    case "و":
                                        dayCell.Style.Fill.BackgroundColor = XLColor.ForestGreen;
                                        dayCell.Style.Font.FontColor = XLColor.Black;
                                        vacationDays++;
                                        break;
                                    default:
                                        dayCell.Style.Fill.BackgroundColor = XLColor.WhiteSmoke;
                                        break;
                                }

                                dayCell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                                dayCell.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            }

                            // إضافة الإحصائيات
                            worksheet.Cell(rowIndex, colIndex++).Value = presentDays;
                            worksheet.Cell(rowIndex, colIndex++).Value = absentDays;

                            // إضافة الملاحظات
                            var notesCell = worksheet.Cell(rowIndex, colIndex++);
                            string notesValue = "";
                            if (dataGridViewOccurrences.Columns.Contains("Notes"))
                            {
                                notesValue = gridRow.Cells["Notes"].Value?.ToString() ?? "";
                            }
                            notesCell.Value = notesValue;
                            notesCell.Style.Fill.BackgroundColor = XLColor.LightYellow;
                            notesCell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Left;

                            rowIndex++;
                        }

                        // تطبيق التنسيق العام (بدء من أعمدة البيانات فقط)
                        if (rowIndex > headerRowIndex)
                        {
                            var dataRange = worksheet.Range(headerRowIndex, 1, rowIndex - 1, totalColumns);
                            dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                            dataRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                        }

                        // ضبط عرض الأعمدة
                        worksheet.Column(1).Width = 5; // التسلسل (ت)
                        worksheet.Column(2).Width = 25; // اسم الموظف
                        worksheet.Column(3).Width = 15; // رقم الكي كارد

                        // أعمدة الأيام - عرض 2.29 وارتفاع 15
                        for (int i = 4; i <= 3 + daysInMonth; i++)
                        {
                            worksheet.Column(i).Width = 2.29;
                        }

                        // ضبط ارتفاع الصفوف لأعمدة الأيام
                        for (int i = headerRowIndex; i < rowIndex; i++)
                        {
                            worksheet.Row(i).Height = 15;
                        }

                        // أعمدة الإحصائيات
                        worksheet.Column(4 + daysInMonth).Width = 12; // أيام الحضور
                        worksheet.Column(5 + daysInMonth).Width = 12; // أيام الغياب
                        worksheet.Column(6 + daysInMonth).Width = 30; // الملاحظات

                        // إضافة التذييل
                        AddReportFooter(worksheet, totalColumns, rowIndex + 3, reportSettings);

                        // حفظ الملف
                        workbook.SaveAs(saveFileDialog.FileName);
                    }

                    // تسجيل نجاح التصدير
                    await ActivityLogHelper.LogExportAsync(
                        "تقرير الوقوعات الشهرية إلى Excel",
                        "MonthlyOccurrences",
                        dataGridViewOccurrences.Rows.Count,
                        saveFileDialog.FileName,
                        "Excel (.xlsx)"
                    );

                    MessageBox.Show("تم تصدير التقرير بنجاح!", "نجح التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // فتح الملف
                    if (MessageBox.Show("هل تريد فتح الملف المصدر؟", "فتح الملف", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo()
                        {
                            FileName = saveFileDialog.FileName,
                            UseShellExecute = true
                        });
                    }
                }
                catch (Exception ex)
                {
                    // تسجيل فشل التصدير
                    await ActivityLogHelper.LogFailedOperationAsync(
                        "فشل تصدير تقرير الوقوعات الشهرية إلى Excel",
                        "MonthlyOccurrences",
                        ex.Message
                    );

                    MessageBox.Show($"خطأ في تصدير Excel: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void UpdateNoDataLabel()
        {
            UIHelper.ShowEmptyMessage(dataGridViewOccurrences, lbl_NoDocuments, "لا توجد بيانات وقوعات");
        }

        private void SetupSearch()
        {
            // إلغاء البحث التلقائي لتحسين الأداء
            // txtSearch.TextChanged += TxtSearch_TextChanged;

            // إضافة Tooltip للمساعدة
            ToolTip toolTip = new ToolTip();
            toolTip.SetToolTip(txtSearch, "اكتب اسم الموظف أو الرقم الوظيفي ثم اضغط زر البحث");
            toolTip.SetToolTip(btnSearch, "انقر للبحث في بيانات الموظفين");
            toolTip.SetToolTip(btnClearSearch, "انقر لإلغاء البحث وإعادة عرض جميع البيانات");
            
            // إضافة إمكانية البحث بالضغط على Enter
            txtSearch.KeyDown += TxtSearch_KeyDown;
        }

        private void TxtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.SuppressKeyPress = true; // منع صوت الـ beep
                PerformSearch();
            }
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            // تم تعطيل البحث التلقائي لتحسين الأداء
            // FilterData();
        }

        // دالة البحث المحسنة مع معالجة أفضل للأخطاء
        private async void PerformSearch()
        {
            if (currentReportData == null)
            {
                MessageBox.Show("لا توجد بيانات للبحث فيها. قم بإنشاء تقرير أولاً.", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string searchTerm = txtSearch.Text.Trim();
            
            // إذا كان البحث فارغ، عرض جميع البيانات
            if (string.IsNullOrEmpty(searchTerm))
            {
                dataGridViewOccurrences.DataSource = currentReportData;
                ApplyFormattingToDataGridView();
                this.Text = "تقرير الوقوعات الشهرية";
                return;
            }

            try
            {
                // إظهار مؤشر التحميل
                this.Cursor = Cursors.WaitCursor;
                
                // إظهار رسالة تحميل مؤقتة
                var originalText = this.Text;
                this.Text = "جاري البحث...";
                
                int resultCount = 0;
                
                // تنفيذ البحث في مهمة منفصلة لتجنب تجمد الواجهة
                await Task.Run(() =>
                {
                    // تطبيق الفلتر على البيانات
                    DataView dataView = new DataView(currentReportData);
                    string filterExpression = $"Name LIKE '%{searchTerm.Replace("'", "''")}%' OR " +
                                            $"EmployeeCode LIKE '%{searchTerm.Replace("'", "''")}%'";

                    // تطبيق الفلتر
                    dataView.RowFilter = filterExpression;
                    resultCount = dataView.Count;
                    
                    // تحديث الواجهة في الـ UI Thread
                    this.Invoke((MethodInvoker)delegate
                    {
                        dataGridViewOccurrences.DataSource = dataView;
                        ApplyFormattingToDataGridView();
                        
                        // إظهار عدد النتائج
                        if (dataView.Count > 0)
                        {
                            this.Text = $"تقرير الوقوعات الشهرية - نتائج البحث ({dataView.Count} نتيجة)";
                            
                            // إظهار رسالة نجاح البحث
                            ToastHelper.ShowToast($"تم العثور على {dataView.Count} نتيجة",
                                ToastHelper.ToastType.Success);
                        }
                        else
                        {
                            this.Text = "تقرير الوقوعات الشهرية - لا توجد نتائج";
                            
                            // إظهار رسالة عدم وجود نتائج
                            ToastHelper.ShowToast("لم يتم العثور على أي نتائج",
                                ToastHelper.ToastType.Warning);
                        }
                    });
                });
                
                // تسجيل عملية البحث مع عدد النتائج
                await ActivityLogHelper.LogSearchAsync("بحث في تقرير الأحداث الشهرية", "MonthlyOccurrences", searchTerm, resultCount, $"معايير البحث: {searchTerm}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                
                // إعادة تعيين العنوان في حالة الخطأ
                this.Text = "تقرير الوقوعات الشهرية";
                
                // إظهار رسالة خطأ
                ToastHelper.ShowToast("حدث خطأ في البحث", ToastHelper.ToastType.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        // دالة البحث القديمة (للتوافق مع الأكواد الموجودة)
        private void FilterData()
        {
            PerformSearch();
        }

        // Handler لزر البحث
        // ملاحظة: تأكد من ربط هذا الـ event في الـ Designer أو في الـ constructor
        private void btnSearch_Click(object sender, EventArgs e)
        {
            PerformSearch();
        }

        // Handler لزر إلغاء البحث (إذا كان موجوداً)
        // ملاحظة: تأكد من ربط هذا الـ event في الـ Designer أو في الـ constructor
        private void btnClearSearch_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            if (currentReportData != null)
            {
                dataGridViewOccurrences.DataSource = currentReportData;
                ApplyFormattingToDataGridView();
                this.Text = "تقرير الوقوعات الشهرية";
                
                // إظهار رسالة إلغاء البحث
                ToastHelper.ShowToast("تم إلغاء البحث وعرض جميع البيانات", 
                    ToastHelper.ToastType.Info);
            }
        }

        private void ApplyFormattingToDataGridView()
        {
            if (dataGridViewOccurrences.Rows.Count == 0) return;

            // تحديد عدد أعمدة الأيام
            int dayColumns = 0;
            for (int i = 1; i <= 31; i++)
            {
                if (dataGridViewOccurrences.Columns.Contains($"Day{i:D2}"))
                    dayColumns = i;
            }

            // تطبيق التنسيق
            if (dayColumns > 0)
            {
                ApplyCellFormatting(dayColumns);
            }
        }

        private async void btnSaveOccurrences_Click(object sender, EventArgs e)
        {
            if (cmbMonth.SelectedItem == null || cmbYear.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الشهر والسنة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (cmbAttendanceType.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع الحضور", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (dataGridViewOccurrences.DataSource == null)
            {
                MessageBox.Show("لا توجد بيانات موظفين للحفظ", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                await SaveOccurrencesToDatabase();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ الوقوعات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task SaveOccurrencesToDatabase()
        {
            int selectedMonth = ((ComboBoxItem)cmbMonth.SelectedItem).Value;
            int selectedYear = (int)cmbYear.SelectedItem;
            string attendanceType = cmbAttendanceType.SelectedItem.ToString().Substring(0, 1); // ن، غ، ت، V
            int daysInMonth = DateTime.DaysInMonth(selectedYear, selectedMonth);
            string selectedGroup = cmbGroup.SelectedItem?.ToString() ?? "";

            // جمع الصفوف المحددة
            List<DataGridViewRow> selectedRows = new List<DataGridViewRow>();
            foreach (DataGridViewRow row in dataGridViewOccurrences.Rows)
            {
                if (row.Cells["Selected"].Value != null && Convert.ToBoolean(row.Cells["Selected"].Value))
                {
                    selectedRows.Add(row);
                }
            }

            if (selectedRows.Count == 0)
            {
                MessageBox.Show("يرجى تحديد موظف واحد على الأقل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            using (SqlConnection connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                using (SqlTransaction transaction = connection.BeginTransaction())
                {
                    try
                    {
                        int addedRecords = 0;
                        int updatedRecords = 0;
                        
                        foreach (DataGridViewRow row in selectedRows)
                        {
                            string employeeCode = row.Cells["EmployeeCode"].Value?.ToString();
                            string employeeName = row.Cells["Name"].Value?.ToString();
                            string keyCardNumber = row.Cells["KeyCardNumber"].Value?.ToString();

                            // التحقق من وجود سجل مسبق
                            string checkQuery = @"SELECT COUNT(*) FROM add_geab
                                          WHERE id_sub_employees = @employeeCode
                                          AND month_add = @month AND year_add = @year";

                            using (SqlCommand checkCommand = new SqlCommand(checkQuery, connection, transaction))
                            {
                                checkCommand.Parameters.AddWithValue("@employeeCode", employeeCode);
                                checkCommand.Parameters.AddWithValue("@month", selectedMonth);
                                checkCommand.Parameters.AddWithValue("@year", selectedYear);

                                int existingRecords = (int)checkCommand.ExecuteScalar();

                                if (existingRecords == 0)
                                {
                                    // إدخال جديد
                                    string insertQuery = @"INSERT INTO add_geab
                                    (id_sub_employees, fullname, month_add, year_add, KeyCardNumber, group_, data_add, user_add, location";

                                    for (int day = 1; day <= daysInMonth; day++)
                                        insertQuery += $", day_{day:D2}";

                                    insertQuery += ", note) VALUES (@employeeCode, @employeeName, @month, @year, @keyCardNumber, @groupName, @dataAdd, @userAdd, @location";

                                    for (int day = 1; day <= daysInMonth; day++)
                                        insertQuery += $", @day{day:D2}";

                                    insertQuery += ", @notes)";

                                    using (SqlCommand insertCommand = new SqlCommand(insertQuery, connection, transaction))
                                    {
                                        insertCommand.Parameters.AddWithValue("@employeeCode", employeeCode);
                                        insertCommand.Parameters.AddWithValue("@employeeName", employeeName);
                                        insertCommand.Parameters.AddWithValue("@month", selectedMonth);
                                        insertCommand.Parameters.AddWithValue("@year", selectedYear);
                                        insertCommand.Parameters.AddWithValue("@keyCardNumber", keyCardNumber ?? "");
                                        insertCommand.Parameters.AddWithValue("@groupName", selectedGroup);
                                        insertCommand.Parameters.AddWithValue("@dataAdd", DateTime.Now);
                                        insertCommand.Parameters.AddWithValue("@userAdd", currentUserFullName);
                                        insertCommand.Parameters.AddWithValue("@location", GetCurrentLocation());

                                        // جمع بيانات الأيام للتسجيل
                                        var dayData = new Dictionary<string, string>();
                                        for (int day = 1; day <= daysInMonth; day++)
                                        {
                                            string dayValue = "";
                                            if (row.Cells[$"Day{day:D2}"].Value != null)
                                            {
                                                dayValue = row.Cells[$"Day{day:D2}"].Value.ToString().Trim();
                                            }

                                            dayData[$"day_{day:D2}"] = dayValue;
                                            // حفظ القيمة كما هي - سواء كانت فارغة أو محددة
                                            insertCommand.Parameters.AddWithValue($"@day{day:D2}", dayValue);
                                        }

                                        // إضافة الملاحظات
                                        string notesValue = "";
                                        if (row.Cells["Notes"].Value != null)
                                        {
                                            notesValue = row.Cells["Notes"].Value.ToString().Trim();
                                        }
                                        insertCommand.Parameters.AddWithValue("@notes", notesValue);

                                        insertCommand.ExecuteNonQuery();
                                        addedRecords++;

                                        // تسجيل عملية الإضافة
                                        var newData = new
                                        {
                                            EmployeeCode = employeeCode,
                                            EmployeeName = employeeName,
                                            Month = selectedMonth,
                                            Year = selectedYear,
                                            Group = selectedGroup,
                                            KeyCardNumber = keyCardNumber,
                                            Days = dayData,
                                            Notes = notesValue
                                        };

                                        await ActivityLogHelper.LogAddOperationAsync(
                                            "إضافة وقوعات شهرية جديدة",
                                            "MonthlyOccurrences",
                                            null,
                                            System.Text.Json.JsonSerializer.Serialize(newData, new System.Text.Json.JsonSerializerOptions { WriteIndented = true, Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping }),
                                            $"الموظف: {employeeName}, الشهر: {selectedMonth}/{selectedYear}, المجموعة: {selectedGroup}"
                                        );
                                    }
                                }
                                else
                                {
                                    // جلب البيانات القديمة للمقارنة
                                    string selectOldQuery = @"SELECT * FROM add_geab
                                                            WHERE id_sub_employees = @employeeCode
                                                            AND month_add = @month AND year_add = @year";
                                    
                                    Dictionary<string, string> oldDayData = new Dictionary<string, string>();
                                    string oldNotes = "";
                                    string oldGroup = "";
                                    
                                    using (SqlCommand selectCommand = new SqlCommand(selectOldQuery, connection, transaction))
                                    {
                                        selectCommand.Parameters.AddWithValue("@employeeCode", employeeCode);
                                        selectCommand.Parameters.AddWithValue("@month", selectedMonth);
                                        selectCommand.Parameters.AddWithValue("@year", selectedYear);
                                        
                                        using (SqlDataReader reader = selectCommand.ExecuteReader())
                                        {
                                            if (reader.Read())
                                            {
                                                for (int day = 1; day <= daysInMonth; day++)
                                                {
                                                    string dayColumn = $"day_{day:D2}";
                                                    oldDayData[dayColumn] = reader[dayColumn]?.ToString() ?? "";
                                                }
                                                oldNotes = reader["note"]?.ToString() ?? "";
                                                oldGroup = reader["group_"]?.ToString() ?? "";
                                            }
                                        }
                                    }

                                    // تحديث السجل الحالي
                                    string updateQuery = @"UPDATE add_geab SET
                                    fullname = @employeeName,
                                    KeyCardNumber = @keyCardNumber,
                                    group_ = @groupName,
                                    data_add = @dataAdd,
                                    user_add = @userAdd,
                                    location = @location";

                                    for (int day = 1; day <= daysInMonth; day++)
                                        updateQuery += $", day_{day:D2} = @day{day:D2}";

                                    updateQuery += ", note = @notes";

                                    updateQuery += @" WHERE id_sub_employees = @employeeCode
                                              AND month_add = @month AND year_add = @year";

                                    using (SqlCommand updateCommand = new SqlCommand(updateQuery, connection, transaction))
                                    {
                                        updateCommand.Parameters.AddWithValue("@employeeCode", employeeCode);
                                        updateCommand.Parameters.AddWithValue("@employeeName", employeeName);
                                        updateCommand.Parameters.AddWithValue("@keyCardNumber", keyCardNumber ?? "");
                                        updateCommand.Parameters.AddWithValue("@groupName", selectedGroup);
                                        updateCommand.Parameters.AddWithValue("@month", selectedMonth);
                                        updateCommand.Parameters.AddWithValue("@year", selectedYear);
                                        updateCommand.Parameters.AddWithValue("@dataAdd", DateTime.Now);
                                        updateCommand.Parameters.AddWithValue("@userAdd", currentUserFullName);
                                        updateCommand.Parameters.AddWithValue("@location", GetCurrentLocation());

                                        // جمع البيانات الجديدة للمقارنة
                                        var newDayData = new Dictionary<string, string>();
                                        for (int day = 1; day <= daysInMonth; day++)
                                        {
                                            string dayValue = "";
                                            if (row.Cells[$"Day{day:D2}"].Value != null)
                                            {
                                                dayValue = row.Cells[$"Day{day:D2}"].Value.ToString().Trim();
                                            }

                                            newDayData[$"day_{day:D2}"] = dayValue;
                                            // حفظ القيمة كما هي - سواء كانت فارغة أو محددة
                                            updateCommand.Parameters.AddWithValue($"@day{day:D2}", dayValue);
                                        }

                                        // إضافة الملاحظات
                                        string notesValue = "";
                                        if (row.Cells["Notes"].Value != null)
                                        {
                                            notesValue = row.Cells["Notes"].Value.ToString().Trim();
                                        }
                                        updateCommand.Parameters.AddWithValue("@notes", notesValue);

                                        updateCommand.ExecuteNonQuery();
                                        updatedRecords++;

                                        // إنشاء كائن التغييرات للتسجيل
                                        var changes = new List<object>();
                                        
                                        // مقارنة بيانات الأيام
                                        foreach (var dayEntry in newDayData)
                                        {
                                            string oldValue = oldDayData.ContainsKey(dayEntry.Key) ? oldDayData[dayEntry.Key] : "";
                                            if (oldValue != dayEntry.Value)
                                            {
                                                changes.Add(new
                                                {
                                                    Field = dayEntry.Key,
                                                    OldValue = oldValue,
                                                    NewValue = dayEntry.Value
                                                });
                                            }
                                        }
                                        
                                        // مقارنة الملاحظات
                                        if (oldNotes != notesValue)
                                        {
                                            changes.Add(new
                                            {
                                                Field = "Notes",
                                                OldValue = oldNotes,
                                                NewValue = notesValue
                                            });
                                        }
                                        
                                        // مقارنة المجموعة
                                        if (oldGroup != selectedGroup)
                                        {
                                            changes.Add(new
                                            {
                                                Field = "Group",
                                                OldValue = oldGroup,
                                                NewValue = selectedGroup
                                            });
                                        }

                                        // تسجيل عملية التحديث إذا كان هناك تغييرات
                                        if (changes.Count > 0)
                                        {
                                            // استخدام LogCustomActivityAsync بدلاً من LogUpdateOperationAsync لأن لدينا بيانات مخصصة
                                            await ActivityLogHelper.LogCustomActivityAsync(
                                                $"تحديث وقوعات شهرية للموظف: {employeeName}",
                                                "MonthlyOccurrences",
                                                null,
                                                null,
                                                System.Text.Json.JsonSerializer.Serialize(changes, new System.Text.Json.JsonSerializerOptions { WriteIndented = true, Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping }),
                                                "تحديث",
                                                "عادي",
                                                $"الموظف: {employeeName}, الشهر: {selectedMonth}/{selectedYear}, عدد التغييرات: {changes.Count}"
                                            );
                                        }
                                    }
                                }
                            }
                        }

                        transaction.Commit();
                        
                        // تسجيل ملخص العملية
                        string operationSummary = $"حفظ الوقوعات الشهرية - إضافة: {addedRecords}, تحديث: {updatedRecords}";
                        await ActivityLogHelper.LogAddOperationAsync(
                            operationSummary,
                            "MonthlyOccurrences",
                            null,
                            $"الشهر: {selectedMonth}/{selectedYear}, المجموعة: {selectedGroup}, إجمالي الموظفين: {selectedRows.Count}",
                            $"تم حفظ وقوعات {selectedRows.Count} موظف بنجاح"
                        );
                        
                        MessageBox.Show($"تم حفظ الوقوعات بنجاح لـ {selectedRows.Count} موظف", "نجح الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        GenerateMonthlyReport(); // إعادة تحميل البيانات بعد الحفظ
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        
                        // تسجيل فشل العملية
                        await ActivityLogHelper.LogFailedOperationAsync(
                            "فشل حفظ الوقوعات الشهرية",
                            "MonthlyOccurrences",
                            ex.Message,
                            null
                        );
                        
                        MessageBox.Show($"فشل الحفظ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private bool isUpdatingCheckboxes = false;

        private void chkSelectAll_CheckedChanged(object sender, EventArgs e)
        {
            if (dataGridViewOccurrences.DataSource != null && !isUpdatingCheckboxes)
            {
                isUpdatingCheckboxes = true;
                foreach (DataGridViewRow row in dataGridViewOccurrences.Rows)
                {
                    if (!row.IsNewRow)
                    {
                        row.Cells["Selected"].Value = chkSelectAll.Checked;
                    }
                }
                isUpdatingCheckboxes = false;
            }
        }

        private void DataGridViewOccurrences_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            if (isUpdatingCheckboxes) return;

            // التحقق من أن النقر في عمود التحديد
            if (e.ColumnIndex >= 0 && dataGridViewOccurrences.Columns[e.ColumnIndex].Name == "Selected")
            {
                // الانتظار لحتى يتم تحديث القيمة
                dataGridViewOccurrences.BeginInvoke(new Action(() =>
                {
                    UpdateSelectAllCheckbox();
                }));
            }
        }

        private void UpdateSelectAllCheckbox()
        {
            if (isUpdatingCheckboxes) return;

            isUpdatingCheckboxes = true;

            // حساب عدد الصفوف المحددة
            int selectedCount = 0;
            int totalRows = 0;

            foreach (DataGridViewRow row in dataGridViewOccurrences.Rows)
            {
                if (!row.IsNewRow)
                {
                    totalRows++;
                    if (row.Cells["Selected"].Value != null && Convert.ToBoolean(row.Cells["Selected"].Value))
                    {
                        selectedCount++;
                    }
                }
            }

            // تحديث حالة checkbox الرئيسي
            if (selectedCount == 0)
            {
                chkSelectAll.CheckState = CheckState.Unchecked;
            }
            else if (selectedCount == totalRows)
            {
                chkSelectAll.CheckState = CheckState.Checked;
            }
            else
            {
                chkSelectAll.CheckState = CheckState.Indeterminate;
            }

            isUpdatingCheckboxes = false;
        }

        private void DataGridViewOccurrences_CellValidating(object sender, DataGridViewCellValidatingEventArgs e)
        {
            // التحقق من أن الخلية في عمود يوم
            if (e.ColumnIndex >= 0 && dataGridViewOccurrences.Columns[e.ColumnIndex].Name.StartsWith("Day"))
            {
                string value = e.FormattedValue?.ToString().Trim().ToUpper() ?? "";
                
                // القيم المسموحة
                string[] allowedValues = { "", "V", "غ", "ت", "ج","م", "د","و" };
                
                if (!string.IsNullOrEmpty(value) && !allowedValues.Contains(value))
                {
                    MessageBox.Show("القيم المسموحة فقط:\n" +
                                  "V  = حضور\n" +
                                  "غ = غياب\n" +
                                  "ت = هروب\n" +
                                  "ج = إجازة\n" +
                                  "م = مرضية\n" +
                                   "د = دورة\n" +
                                   "و = واجب\n" +
                                  "أو اتركها فارغة", 
                                  "قيمة غير صحيحة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    e.Cancel = true;
                }
            }
        }

        private void DataGridViewOccurrences_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            // تحديث التنسيق عند تغيير قيمة يوم
            if (e.ColumnIndex >= 0 && e.RowIndex >= 0 && 
                dataGridViewOccurrences.Columns[e.ColumnIndex].Name.StartsWith("Day"))
            {
                UpdateCellFormatting(e.RowIndex, e.ColumnIndex);
            }
        }

        private void UpdateCellFormatting(int rowIndex, int columnIndex)
        {
            if (rowIndex < 0 || columnIndex < 0 || 
                rowIndex >= dataGridViewOccurrences.Rows.Count ||
                columnIndex >= dataGridViewOccurrences.Columns.Count)
                return;

            var cell = dataGridViewOccurrences.Rows[rowIndex].Cells[columnIndex];
            string value = cell.Value?.ToString().ToUpper() ?? "";

            // تطبيق الألوان حسب نوع الوقوع
            switch (value)
            {
              
                case "V": // حضور عربي
                    cell.Style.BackColor = Color.Chocolate;
                    cell.Style.ForeColor = Color.Black;
                    break;

                case "غ": // غياب
                    cell.Style.BackColor = Color.Yellow;
                    cell.Style.ForeColor = Color.Black;
                    break;

                case "ت": // هروب
                    cell.Style.BackColor = Color.Red;
                    cell.Style.ForeColor = Color.Black;
                    break;

                case "ج": // إجازة
                    cell.Style.BackColor = Color.DodgerBlue;
                    cell.Style.ForeColor = Color.Black;
                    break;
                case "م": // مرضية
                    cell.Style.BackColor = Color.LightGreen;
                    cell.Style.ForeColor = Color.Black;
                    break;
                case "د": // دورة
                    cell.Style.BackColor = Color.RoyalBlue;
                    cell.Style.ForeColor = Color.Black;
                    break;
                case "و": // واجب
                    cell.Style.BackColor = Color.ForestGreen;
                    cell.Style.ForeColor = Color.Black;
                    break;
                default:
                    // قيمة غير معروفة
                    cell.Style.BackColor = Color.White;
                    cell.Style.ForeColor = Color.Black;
                    break;
                case "":
                case " ":
                    cell.Style.BackColor = Color.WhiteSmoke;
                    cell.Style.ForeColor = Color.Gray;
                    break;
                
                    
            }
        }

        private void AddTooltipsToDataGrid()
        {
            // إضافة tooltip للمساعدة
            ToolTip gridToolTip = new ToolTip();
            gridToolTip.SetToolTip(dataGridViewOccurrences, 
                "🔘 انقر على خانة التحديد لتحديد الموظف\n" +
                "✏️ انقر على أي يوم لتعديل الحالة يدوياً\n" +
                "📝 القيم المسموحة: V (حضور), غ (غياب), ت (هروب), ج (إجازة),و (واجب),د (دورة), م (مرضية)\n" +
                "⚡ استخدم الأزرار السريعة للوجبات الشائعة\n" +
                "📅 أو حدد نطاق مخصص للفترات");
        }

        // دالة مساعدة لعرض تعليمات الاستخدام
        private void ShowUsageInstructions()
        {
            MessageBox.Show(
                "📋 طريقة استخدام نظام الوجبات:\n\n" +
                "1️⃣ توليد التقرير:\n" +
                "   • اختر المجموعة والشهر والسنة\n" +
                "   • انقر 'توليد التقرير'\n\n" +
                "2️⃣ تحديد الموظفين:\n" +
                "   • حدد الموظفين المطلوبين\n" +
                "   • أو استخدم 'تحديد الكل'\n\n" +
                "3️⃣ تطبيق الحالات:\n" +
                "   أ) للشهر كاملاً: زر 'تطبيق الحالة'\n" +
                "   ب) للوجبات السريعة: الأزرار الملونة (تمسح باقي الأيام)\n" +
                "   ج) لنطاق مخصص: حدد التواريخ + زر 'تطبيق على النطاق'\n" +
                "   د) تعديل فردي: انقر على اليوم مباشرة\n" +
                "   هـ) مسح الكل: زر 'مسح الكل' لبدء جديد\n\n" +
                "4️⃣ الحفظ والتصدير:\n" +
                "   • احفظ البيانات لتأكيد التغييرات\n" +
                "   • صدّر إلى Excel للتقارير\n\n" +
                "💡 مثال للوجبات:\n" +
                "   الوجبة الأولى: 1-15 (حضور) - باقي الأيام فارغة\n" +
                "   الوجبة الثانية: 16-30 (إجازة) - باقي الأيام فارغة\n\n" +
                "🎯 ميزة جديدة: الأيام خارج الوجبة تُمسح تلقائياً لتوضيح نطاق العمل!",
                "دليل الاستخدام السريع", 
                MessageBoxButtons.OK, 
                MessageBoxIcon.Information);
        }

        private void SetupHelpButton()
        {
            // البحث عن زر المساعدة
            var btnHelp = this.Controls.Find("btnHelp", true).FirstOrDefault() as Button;
            
            if (btnHelp != null)
            {
                //btnHelp.Text = "مساعدة";
                //btnHelp.BackColor = Color.LightSkyBlue;
                //btnHelp.Font = new Font("Cairo",10, FontStyle.Bold);
                btnHelp.Click += (s, e) => ShowUsageInstructions();

                ToolTip helpTip = new ToolTip();
                helpTip.SetToolTip(btnHelp, "انقر لعرض دليل الاستخدام وأمثلة على الوجبات");
            }
        }

        
        private void btnSelectAll_Click(object sender, EventArgs e)
        {
            if (dataGridViewOccurrences.DataSource != null)
            {
                int selectedCount = 0;

                foreach (DataGridViewRow row in dataGridViewOccurrences.SelectedRows)
                {
                    if (!row.IsNewRow)
                    {
                        row.Cells["Selected"].Value = true;

                        // تنسيق مرئي اختياري
                        row.DefaultCellStyle.BackColor = Color.LightYellow;
                        row.DefaultCellStyle.SelectionBackColor = Color.Gold;

                        selectedCount++;
                    }
                }

                chkSelectAll.Checked = (selectedCount == dataGridViewOccurrences.Rows.Count - 1); // تقريبًا كلهم
                this.Text = $"تم تحديد {selectedCount} موظف";
            }
        }

        private void btnClearSelection_Click(object sender, EventArgs e)
        {
            if (dataGridViewOccurrences.DataSource != null)
            {
                foreach (DataGridViewRow row in dataGridViewOccurrences.Rows)
                {
                    if (!row.IsNewRow)
                    {
                        row.Cells["Selected"].Value = false;
                        
                        // إعادة اللون الطبيعي
                        row.DefaultCellStyle.BackColor = Color.White;
                        row.DefaultCellStyle.SelectionBackColor = Color.LightBlue;
                    }
                }
                chkSelectAll.Checked = false;
                
                // إعادة تعيين عنوان النموذج
                this.Text = "الوقوعات الشهرية";
            }
        }

        /// <summary>
        /// تحديد الصفوف المظللة حالياً - ميزة جديدة لتسهيل العمل
        /// </summary>
        private void btnSelectHighlighted_Click(object sender, EventArgs e)
        {
            if (dataGridViewOccurrences.DataSource != null && dataGridViewOccurrences.SelectedRows.Count > 0)
            {
                int selectedCount = 0;
                foreach (DataGridViewRow row in dataGridViewOccurrences.SelectedRows)
                {
                    if (!row.IsNewRow && dataGridViewOccurrences.Columns.Contains("Selected"))
                    {
                        row.Cells["Selected"].Value = true;
                        selectedCount++;
                        
                        // تغيير لون الصف ليدل على التحديد
                        row.DefaultCellStyle.BackColor = Color.LightYellow;
                        row.DefaultCellStyle.SelectionBackColor = Color.Gold;
                    }
                }
                
                if (selectedCount > 0)
                {
                    MessageBox.Show($"تم تحديد {selectedCount} موظف من الصفوف المظللة", "تم التحديد", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    // تحديث عنوان النموذج
                    this.Text = $"الوقوعات الشهرية - محدد: {GetTotalSelectedCount()} موظف";
                }
                else
                {
                    MessageBox.Show("لا توجد صفوف صالحة للتحديد", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            else
            {
                MessageBox.Show("يرجى تظليل الصفوف أولاً ثم الضغط على هذا الزر", "تعليمات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
        
        /// <summary>
        /// حساب إجمالي الصفوف المحددة
        /// </summary>
        private int GetTotalSelectedCount()
        {
            int count = 0;
            if (dataGridViewOccurrences.DataSource != null)
            {
                foreach (DataGridViewRow row in dataGridViewOccurrences.Rows)
                {
                    if (!row.IsNewRow && row.Cells["Selected"].Value != null && 
                        Convert.ToBoolean(row.Cells["Selected"].Value))
                    {
                        count++;
                    }
                }
            }
            return count;
        }

        private void SetupGroupComboBox()
        {
            try
            {
                cmbGroup.Items.Clear();
                cmbGroup.Items.Add("جميع الموظفين");

                // جلب المجموعات من قاعدة البيانات
                using (SqlConnection connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string query = "SELECT GroupName FROM WorkGroups WHERE IsActive = 1 ORDER BY GroupName";
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                string groupName = reader["GroupName"].ToString();
                                if (!string.IsNullOrEmpty(groupName))
                                {
                                    cmbGroup.Items.Add(groupName);
                                }
                            }
                        }
                    }
                }

                cmbGroup.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المجموعات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetupAttendanceTypeComboBox()
        {
            cmbAttendanceType.Items.Clear();
            cmbAttendanceType.Items.Add("V - حضور");
            cmbAttendanceType.Items.Add("غ - غياب");
            cmbAttendanceType.Items.Add("ت - هروب");
            cmbAttendanceType.Items.Add("ج - إجازة");
            cmbAttendanceType.Items.Add("م - مرضية");
            cmbAttendanceType.Items.Add("د - دورة");
            cmbAttendanceType.Items.Add("و - واجب");
            cmbAttendanceType.SelectedIndex = 0;

            // إضافة tooltip للمساعدة
            ToolTip attendanceToolTip = new ToolTip();
            attendanceToolTip.SetToolTip(cmbAttendanceType, 
                "اختر الحالة المراد تطبيقها على الموظفين المحددين\n" +
                "يمكنك أيضاً تعديل الأيام الفردية مباشرة في الجدول");

            // إعداد عناصر تحديد النطاق
            SetupDateRangeControls();
        }

        private void SetupDateRangeControls()
        {
            // إذا كانت العناصر موجودة، قم بإعدادها
            if (this.Controls.Find("dtpFromDate", true).Length > 0 && 
                this.Controls.Find("dtpToDate", true).Length > 0)
            {
                var dtpFromDate = this.Controls.Find("dtpFromDate", true)[0] as DateTimePicker;
                var dtpToDate = this.Controls.Find("dtpToDate", true)[0] as DateTimePicker;

                if (dtpFromDate != null && dtpToDate != null)
                {
                    // تعيين التاريخ الافتراضي للشهر الحالي
                    int selectedMonth = cmbMonth.SelectedItem != null ? ((ComboBoxItem)cmbMonth.SelectedItem).Value : DateTime.Now.Month;
                    int selectedYear = cmbYear.SelectedItem != null ? (int)cmbYear.SelectedItem : DateTime.Now.Year;

                    DateTime monthStart = new DateTime(selectedYear, selectedMonth, 1);
                    DateTime monthEnd = new DateTime(selectedYear, selectedMonth, DateTime.DaysInMonth(selectedYear, selectedMonth));

                    dtpFromDate.MinDate = monthStart;
                    dtpFromDate.MaxDate = monthEnd;
                    dtpFromDate.Value = monthStart;

                    dtpToDate.MinDate = monthStart;
                    dtpToDate.MaxDate = monthEnd;
                    dtpToDate.Value = monthEnd;

                    // إضافة أحداث التحديث
                    dtpFromDate.ValueChanged += DtpFromDate_ValueChanged;
                    dtpToDate.ValueChanged += DtpToDate_ValueChanged;

                    // إضافة tooltips
                    ToolTip rangeToolTip = new ToolTip();
                    rangeToolTip.SetToolTip(dtpFromDate, "اختر تاريخ البداية للفترة (الوجبة)");
                    rangeToolTip.SetToolTip(dtpToDate, "اختر تاريخ النهاية للفترة (الوجبة)");
                }
            }

            // إعداد ComboBox للوجبات السريعة
            if (this.Controls.Find("cmbQuickRanges", true).Length > 0)
            {
                var cmbQuickRanges = this.Controls.Find("cmbQuickRanges", true)[0] as ComboBox;
                if (cmbQuickRanges != null)
                {
                    SetupQuickRangesComboBox(cmbQuickRanges);
                }
            }
        }

        private void SetupQuickRangesComboBox(ComboBox cmbQuickRanges)
        {
            cmbQuickRanges.Items.Clear();
            cmbQuickRanges.Items.Add("النصف الأول من الشهر (1-15)");
            cmbQuickRanges.Items.Add("النصف الثاني من الشهر (16-نهاية الشهر)");
            cmbQuickRanges.Items.Add("الأسبوع الأول (1-7)");
            cmbQuickRanges.Items.Add("الأسبوع الثاني (8-14)");
            cmbQuickRanges.Items.Add("الأسبوع الثالث (15-21)");
            cmbQuickRanges.Items.Add("الأسبوع الرابع (22-28)");
            cmbQuickRanges.Items.Add("الأيام الأخيرة من الشهر (29-نهاية الشهر)");
            cmbQuickRanges.Items.Add("كامل الشهر");

            cmbQuickRanges.SelectedIndexChanged += CmbQuickRanges_SelectedIndexChanged;

            ToolTip quickToolTip = new ToolTip();
            quickToolTip.SetToolTip(cmbQuickRanges, "اختر نطاق سريع للوجبات الشائعة");
        }

        private void DtpFromDate_ValueChanged(object sender, EventArgs e)
        {
            var dtpFromDate = sender as DateTimePicker;
            var dtpToDate = this.Controls.Find("dtpToDate", true)[0] as DateTimePicker;

            if (dtpFromDate != null && dtpToDate != null)
            {
                // التأكد من أن تاريخ النهاية ليس قبل تاريخ البداية
                if (dtpToDate.Value < dtpFromDate.Value)
                {
                    dtpToDate.Value = dtpFromDate.Value;
                }
            }
        }

        private void DtpToDate_ValueChanged(object sender, EventArgs e)
        {
            var dtpToDate = sender as DateTimePicker;
            var dtpFromDate = this.Controls.Find("dtpFromDate", true)[0] as DateTimePicker;

            if (dtpFromDate != null && dtpToDate != null)
            {
                // التأكد من أن تاريخ البداية ليس بعد تاريخ النهاية
                if (dtpFromDate.Value > dtpToDate.Value)
                {
                    dtpFromDate.Value = dtpToDate.Value;
                }
            }
        }

        private void CmbQuickRanges_SelectedIndexChanged(object sender, EventArgs e)
        {
            var cmbQuickRanges = sender as ComboBox;
            var dtpFromDate = this.Controls.Find("dtpFromDate", true).FirstOrDefault() as DateTimePicker;
            var dtpToDate = this.Controls.Find("dtpToDate", true).FirstOrDefault() as DateTimePicker;

            if (cmbQuickRanges == null || dtpFromDate == null || dtpToDate == null || 
                cmbMonth.SelectedItem == null || cmbYear.SelectedItem == null)
                return;

            int selectedMonth = ((ComboBoxItem)cmbMonth.SelectedItem).Value;
            int selectedYear = (int)cmbYear.SelectedItem;
            int daysInMonth = DateTime.DaysInMonth(selectedYear, selectedMonth);

            DateTime monthStart = new DateTime(selectedYear, selectedMonth, 1);

            switch (cmbQuickRanges.SelectedIndex)
            {
                case 0: // النصف الأول (1-15)
                    dtpFromDate.Value = monthStart;
                    dtpToDate.Value = new DateTime(selectedYear, selectedMonth, Math.Min(15, daysInMonth));
                    break;
                case 1: // النصف الثاني (16-نهاية الشهر)
                    dtpFromDate.Value = new DateTime(selectedYear, selectedMonth, Math.Min(16, daysInMonth));
                    dtpToDate.Value = new DateTime(selectedYear, selectedMonth, daysInMonth);
                    break;
                case 2: // الأسبوع الأول (1-7)
                    dtpFromDate.Value = monthStart;
                    dtpToDate.Value = new DateTime(selectedYear, selectedMonth, Math.Min(7, daysInMonth));
                    break;
                case 3: // الأسبوع الثاني (8-14)
                    dtpFromDate.Value = new DateTime(selectedYear, selectedMonth, Math.Min(8, daysInMonth));
                    dtpToDate.Value = new DateTime(selectedYear, selectedMonth, Math.Min(14, daysInMonth));
                    break;
                case 4: // الأسبوع الثالث (15-21)
                    dtpFromDate.Value = new DateTime(selectedYear, selectedMonth, Math.Min(15, daysInMonth));
                    dtpToDate.Value = new DateTime(selectedYear, selectedMonth, Math.Min(21, daysInMonth));
                    break;
                case 5: // الأسبوع الرابع (22-28)
                    dtpFromDate.Value = new DateTime(selectedYear, selectedMonth, Math.Min(22, daysInMonth));
                    dtpToDate.Value = new DateTime(selectedYear, selectedMonth, Math.Min(28, daysInMonth));
                    break;
                case 6: // الأيام الأخيرة (29-نهاية الشهر)
                    dtpFromDate.Value = new DateTime(selectedYear, selectedMonth, Math.Min(29, daysInMonth));
                    dtpToDate.Value = new DateTime(selectedYear, selectedMonth, daysInMonth);
                    break;
                case 7: // كامل الشهر
                    dtpFromDate.Value = monthStart;
                    dtpToDate.Value = new DateTime(selectedYear, selectedMonth, daysInMonth);
                    break;
            }
        }

        private void cmbGroup_SelectedIndexChanged(object sender, EventArgs e)
        {
            // لا تفعل شيئاً هنا لأننا نريد أن يتم توليد البيانات فقط عند الضغط على زر التوليد
        }

        // دالة لتحديث نطاقات التاريخ عند تغيير الشهر أو السنة
        private void UpdateDateRangeControls()
        {
            if (cmbMonth.SelectedItem == null || cmbYear.SelectedItem == null)
                return;

            if (dtpFromDate != null && dtpToDate != null)
            {
                int selectedMonth = ((ComboBoxItem)cmbMonth.SelectedItem).Value;
                int selectedYear = (int)cmbYear.SelectedItem;

                DateTime monthStart = new DateTime(selectedYear, selectedMonth, 1);
                DateTime monthEnd = new DateTime(selectedYear, selectedMonth, DateTime.DaysInMonth(selectedYear, selectedMonth));

                // إعادة تعيين القيم بترتيب صحيح لتجنب تعارض MinDate/MaxDate
                try
                {
                    // إعادة تعيين النطاقات بطريقة آمنة
                    dtpFromDate.MinDate = new DateTime(1900, 1, 1);
                    dtpFromDate.MaxDate = new DateTime(2100, 12, 31);
                    dtpToDate.MinDate = new DateTime(1900, 1, 1);
                    dtpToDate.MaxDate = new DateTime(2100, 12, 31);
                    
                    // تحديد القيم أولاً
                    dtpFromDate.Value = monthStart;
                    dtpToDate.Value = monthEnd;
                    
                    // ثم تحديد النطاقات الصحيحة
                    dtpFromDate.MinDate = monthStart;
                    dtpFromDate.MaxDate = monthEnd;
                    dtpToDate.MinDate = monthStart;
                    dtpToDate.MaxDate = monthEnd;
                }
                catch (Exception ex)
                {
                    // في حالة حدوث خطأ، استخدم القيم الافتراضية
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحديث التواريخ: {ex.Message}");
                    
                    dtpFromDate.MinDate = new DateTime(1900, 1, 1);
                    dtpFromDate.MaxDate = new DateTime(2100, 12, 31);
                    dtpFromDate.Value = monthStart;
                    
                    dtpToDate.MinDate = new DateTime(1900, 1, 1);
                    dtpToDate.MaxDate = new DateTime(2100, 12, 31);
                    dtpToDate.Value = monthEnd;
                }
            }
        }

        // دالة مساعدة لتفعيل أزرار الوجبات السريعة
        private void EnableQuickShiftButtons(bool enable)
        {
            // تفعيل/إلغاء تفعيل الأزرار السريعة
            btnFirstHalf.Enabled = enable;
            btnSecondHalf.Enabled = enable;
            btnWeek1.Enabled = enable;
            btnWeek2.Enabled = enable;
            btnClearAll.Enabled = enable;

            if (enable)
            {
                // إضافة tooltips
                ToolTip quickTip = new ToolTip();
                quickTip.SetToolTip(btnFirstHalf, "تطبيق الحالة على النصف الأول من الشهر (1-15) ومسح باقي الأيام");
                quickTip.SetToolTip(btnSecondHalf, "تطبيق الحالة على النصف الثاني من الشهر (16-نهاية) ومسح باقي الأيام");
                quickTip.SetToolTip(btnWeek1, "تطبيق الحالة على الأسبوع الأول (1-7) ومسح باقي الأيام");
                quickTip.SetToolTip(btnWeek2, "تطبيق الحالة على الأسبوع الثاني (8-14) ومسح باقي الأيام");
                quickTip.SetToolTip(btnClearAll, "مسح جميع الأيام للموظفين المحددين - لبدء جديد");
            }
        }

        private void ClearAllDays()
        {
            if (cmbMonth.SelectedItem == null || cmbYear.SelectedItem == null)
            {
                MessageBox.Show("يرجى انشاء وقوعات أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // تحديد الصفوف المحددة
            List<DataGridViewRow> selectedRows = new List<DataGridViewRow>();
            foreach (DataGridViewRow row in dataGridViewOccurrences.Rows)
            {
                if (!row.IsNewRow && row.Cells["Selected"].Value != null && Convert.ToBoolean(row.Cells["Selected"].Value))
                {
                    selectedRows.Add(row);
                }
            }

            if (selectedRows.Count == 0)
            {
                MessageBox.Show("يرجى تحديد موظف واحد على الأقل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            DialogResult result = MessageBox.Show(
                $"هل أنت متأكد من مسح جميع الأيام للموظفين المحددين ({selectedRows.Count} موظف)؟\n\n" +
                "سيتم مسح جميع البيانات في الشهر الحالي",
                "تأكيد المسح", 
                MessageBoxButtons.YesNo, 
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                int selectedMonth = ((ComboBoxItem)cmbMonth.SelectedItem).Value;
                int selectedYear = (int)cmbYear.SelectedItem;
                int daysInMonth = DateTime.DaysInMonth(selectedYear, selectedMonth);

                int clearedCells = 0;
                foreach (DataGridViewRow row in selectedRows)
                {
                    for (int day = 1; day <= daysInMonth; day++)
                    {
                        string dayColumnName = $"Day{day:D2}";
                        if (dataGridViewOccurrences.Columns.Contains(dayColumnName))
                        {
                            var cell = row.Cells[dayColumnName];
                            cell.Value = "";
                            
                            // تحديث التنسيق
                            int columnIndex = dataGridViewOccurrences.Columns[dayColumnName].Index;
                            UpdateCellFormatting(row.Index, columnIndex);
                            clearedCells++;
                        }
                    }
                }

                MessageBox.Show($"✅ تم مسح {clearedCells} خلية بنجاح\n" +
                              $"👥 للموظفين المحددين: {selectedRows.Count} موظف\n" +
                              $"📅 الشهر: {cmbMonth.Text} {cmbYear.SelectedItem}\n\n" +
                              "💡 يمكنك الآن تطبيق وجبات جديدة على نطاقات محددة\n" +
                              "ملاحظة: يجب حفظ البيانات لتأكيد التغييرات", 
                    "تم المسح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        // دوال الأحداث للأزرار السريعة
        private void btnFirstHalf_Click(object sender, EventArgs e)
        {
            ApplyQuickShift(1, 15);
        }

        private void btnSecondHalf_Click(object sender, EventArgs e)
        {
            int selectedMonth = ((ComboBoxItem)cmbMonth.SelectedItem).Value;
            int selectedYear = (int)cmbYear.SelectedItem;
            int daysInMonth = DateTime.DaysInMonth(selectedYear, selectedMonth);
            ApplyQuickShift(16, daysInMonth);
        }

        private void btnWeek1_Click(object sender, EventArgs e)
        {
            ApplyQuickShift(1, 7);
        }

        private void btnWeek2_Click(object sender, EventArgs e)
        {
            ApplyQuickShift(8, 14);
        }

        private void btnClearAll_Click(object sender, EventArgs e)
        {
            ClearAllDays();
        }

        private void ApplyQuickShift(int fromDay, int toDay)
        {
            if (cmbAttendanceType.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع الحضور أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (cmbMonth.SelectedItem == null || cmbYear.SelectedItem == null)
            {
                MessageBox.Show("يرجى انشاء وقوعات أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int selectedMonth = ((ComboBoxItem)cmbMonth.SelectedItem).Value;
            int selectedYear = (int)cmbYear.SelectedItem;
            int daysInMonth = DateTime.DaysInMonth(selectedYear, selectedMonth);

            // إذا كان toDay هو -1، استخدم آخر يوم في الشهر
            if (toDay == -1)
                toDay = daysInMonth;

            // التأكد من أن الأيام ضمن نطاق الشهر
            fromDay = Math.Max(1, Math.Min(fromDay, daysInMonth));
            toDay = Math.Max(fromDay, Math.Min(toDay, daysInMonth));

            string attendanceType = cmbAttendanceType.SelectedItem.ToString().Substring(0, 1);

            // تحديد الصفوف المحددة
            List<DataGridViewRow> selectedRows = new List<DataGridViewRow>();
            foreach (DataGridViewRow row in dataGridViewOccurrences.Rows)
            {
                if (!row.IsNewRow && row.Cells["Selected"].Value != null && Convert.ToBoolean(row.Cells["Selected"].Value))
                {
                    selectedRows.Add(row);
                }
            }

            if (selectedRows.Count == 0)
            {
                MessageBox.Show("يرجى تحديد موظف واحد على الأقل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // تطبيق الحالة على النطاق المحدد فقط (بدون مسح باقي الأيام)
            int updatedCells = 0;
            foreach (DataGridViewRow row in selectedRows)
            {
                for (int day = fromDay; day <= toDay; day++)
                {
                    string dayColumnName = $"Day{day:D2}";
                    if (dataGridViewOccurrences.Columns.Contains(dayColumnName))
                    {
                        var cell = row.Cells[dayColumnName];
                        cell.Value = attendanceType;
                        
                        // تحديث التنسيق فوراً
                        int columnIndex = dataGridViewOccurrences.Columns[dayColumnName].Index;
                        UpdateCellFormatting(row.Index, columnIndex);
                        updatedCells++;
                    }
                }
            }

            string dateRange = $"{fromDay} إلى {toDay} من {cmbMonth.Text}";
            
            MessageBox.Show($"✅ تم تطبيق حالة '{cmbAttendanceType.SelectedItem}' على {selectedRows.Count} موظف\n" +
                          $"📅 للوجبة: {dateRange}\n" +
                          $"✏️ عدد الخلايا المحدثة: {updatedCells}\n\n" +
                          "💡 تم ملء الأيام المحددة فقط، باقي الأيام تُركت كما هي\n" +
                          "� يمكنك الآن تطبيق وجبات أخرى دون القلق من فقدان البيانات السابقة\n" +
                          "ملاحظة: يجب حفظ البيانات لتأكيد التغييرات", 
                "تم تطبيق الوجبة السريعة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnApplyAttendance_Click(object sender, EventArgs e)
        {
            if (cmbAttendanceType.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع الحضور أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string attendanceType = cmbAttendanceType.SelectedItem.ToString().Substring(0, 1);

            // تحديد الصفوف المحددة
            List<DataGridViewRow> selectedRows = new List<DataGridViewRow>();
            foreach (DataGridViewRow row in dataGridViewOccurrences.Rows)
            {
                if (!row.IsNewRow && row.Cells["Selected"].Value != null && Convert.ToBoolean(row.Cells["Selected"].Value))
                {
                    selectedRows.Add(row);
                }
            }

            if (selectedRows.Count == 0)
            {
                MessageBox.Show("يرجى تحديد موظف واحد على الأقل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // تطبيق الحالة على جميع أيام الموظفين المحددين
            foreach (DataGridViewRow row in selectedRows)
            {
                for (int i = 4; i < dataGridViewOccurrences.Columns.Count; i++)
                {
                    if (dataGridViewOccurrences.Columns[i].Name.StartsWith("Day"))
                    {
                        row.Cells[i].Value = attendanceType;
                        // تحديث التنسيق فوراً
                        UpdateCellFormatting(row.Index, i);
                    }
                }
            }

            MessageBox.Show($"تم تطبيق حالة '{cmbAttendanceType.SelectedItem}' على {selectedRows.Count} موظف لجميع أيام الشهر\n\n" +
                          "يمكنك الآن تعديل أيام فردية بالنقر عليها مباشرة\n" +
                          "ملاحظة: يجب حفظ البيانات لتأكيد التغييرات", 
                "تم التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        // دالة لمسح جميع الأيام عدا النطاق المحدد
        private void ClearDaysExceptRange(List<DataGridViewRow> selectedRows, int fromDay, int toDay, int daysInMonth)
        {
            foreach (DataGridViewRow row in selectedRows)
            {
                for (int day = 1; day <= daysInMonth; day++)
                {
                    // إذا كان اليوم خارج النطاق المحدد، امسحه
                    if (day < fromDay || day > toDay)
                    {
                        string dayColumnName = $"Day{day:D2}";
                        if (dataGridViewOccurrences.Columns.Contains(dayColumnName))
                        {
                            var cell = row.Cells[dayColumnName];
                            cell.Value = ""; // مسح القيمة
                            
                            // تحديث التنسيق للخلية الفارغة
                            int columnIndex = dataGridViewOccurrences.Columns[dayColumnName].Index;
                            UpdateCellFormatting(row.Index, columnIndex);
                        }
                    }
                }
            }
        }

        // دالة جديدة لتطبيق الحالة على نطاق محدد من الأيام
        private void btnApplyToDateRange_Click(object sender, EventArgs e)
        {
            if (cmbAttendanceType.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع الحضور أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (cmbMonth.SelectedItem == null || cmbYear.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الشهر والسنة أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (dtpFromDate == null || dtpToDate == null)
            {
                MessageBox.Show("لم يتم العثور على عناصر تحديد التاريخ", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            string attendanceType = cmbAttendanceType.SelectedItem.ToString().Substring(0, 1);

            // تحديد الصفوف المحددة
            List<DataGridViewRow> selectedRows = new List<DataGridViewRow>();
            foreach (DataGridViewRow row in dataGridViewOccurrences.Rows)
            {
                if (!row.IsNewRow && row.Cells["Selected"].Value != null && Convert.ToBoolean(row.Cells["Selected"].Value))
                {
                    selectedRows.Add(row);
                }
            }

            if (selectedRows.Count == 0)
            {
                MessageBox.Show("يرجى تحديد موظف واحد على الأقل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // حساب الأيام في النطاق المحدد
            int selectedMonth = ((ComboBoxItem)cmbMonth.SelectedItem).Value;
            int selectedYear = (int)cmbYear.SelectedItem;
            
            // التأكد من أن التواريخ في نفس الشهر المحدد
            DateTime fromDate = dtpFromDate.Value;
            DateTime toDate = dtpToDate.Value;
            
            // إذا كانت التواريخ في شهر مختلف، استخدم الأيام فقط
            int fromDay = fromDate.Day;
            int toDay = toDate.Day;
            
            // التأكد من صحة النطاق
            if (fromDay > toDay)
            {
                MessageBox.Show("تاريخ البداية يجب أن يكون قبل تاريخ النهاية", "خطأ في النطاق", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            int daysInMonth = DateTime.DaysInMonth(selectedYear, selectedMonth);

            // تطبيق الحالة على النطاق المحدد فقط (بدون مسح باقي الأيام)
            int updatedCells = 0;
            foreach (DataGridViewRow row in selectedRows)
            {
                for (int day = fromDay; day <= toDay; day++)
                {
                    string dayColumnName = $"Day{day:D2}";
                    if (dataGridViewOccurrences.Columns.Contains(dayColumnName))
                    {
                        var cell = row.Cells[dayColumnName];
                        cell.Value = attendanceType;
                        
                        // تحديث التنسيق فوراً
                        int columnIndex = dataGridViewOccurrences.Columns[dayColumnName].Index;
                        UpdateCellFormatting(row.Index, columnIndex);
                        updatedCells++;
                    }
                }
            }

            string dateRange = $"{fromDay}/{selectedMonth} إلى {toDay}/{selectedMonth}";
            
            MessageBox.Show($"✅ تم تطبيق حالة '{cmbAttendanceType.SelectedItem}' على {selectedRows.Count} موظف\n" +
                          $"📅 للنطاق المخصص: {dateRange}\n" +
                          $"✏️ عدد الخلايا المحدثة: {updatedCells}\n\n" +
                          "💡 تم ملء الأيام المحددة فقط، باقي الأيام تُركت كما هي\n" +
                          "� يمكنك الآن تطبيق نطاقات أخرى دون القلق من فقدان البيانات السابقة\n" +
                          "ملاحظة: يجب حفظ البيانات لتأكيد التغييرات", 
                "تم التطبيق على النطاق المخصص", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    // فئة مساعدة للعناصر المختارة في ComboBox
    public class ComboBoxItem
    {
        public string Text { get; set; }
        public int Value { get; set; }

        public override string ToString()
        {
            return Text;
        }
    }
}