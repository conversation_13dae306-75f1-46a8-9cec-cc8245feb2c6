# نظام إدارة الدورات التدريبية المطور

## نظرة عامة
تم تطوير نظام شامل لإدارة الدورات التدريبية في تطبيق الموارد البشرية، يتضمن إدارة الحضور والتقييم والإشعارات مع إمكانيات متقدمة للتتبع والتقارير.

## المميزات الجديدة

### 1. إدارة الدورات المتقدمة
- **الحقول الجديدة المضافة:**
  - حالة الدورة (مجدولة، جارية، مكتملة، ملغية، مؤجلة)
  - موقع الدورة
  - العدد الأقصى والحالي للمشاركين
  - وصف الدورة والمتطلبات المسبقة
  - حالة إصدار الشهادة
  - تكلفة الدورة
  - مستوى الأولوية
  - حالة إرسال الإشعارات
  - معلومات الإنشاء والتحديث
  - حالة النشاط

### 2. نظام حضور الدورات
- تسجيل حضور المشاركين يومياً
- إضافة ملاحظات للحضور
- تتبع من قام بتسجيل الحضور ومتى
- تقارير شاملة للحضور

### 3. نظام تقييم الدورات
- تقييم الدورات من 1 إلى 10
- إضافة تعليقات مفصلة
- تتبع تاريخ التقييم
- حساب متوسط التقييمات

### 4. نظام الإشعارات المتقدم
- أنواع إشعارات متعددة (بداية، انتهاء، تذكير، إلغاء، تأجيل)
- مستويات أولوية مختلفة
- إرسال جماعي للإشعارات
- تتبع حالة قراءة الإشعارات
- إشعارات تلقائية للدورات القادمة

### 5. تفاصيل الدورات المتقدمة
- واجهة شاملة لإدارة جميع تفاصيل الدورة
- إرسال إشعارات مباشرة للمشاركين
- تحديث معلومات الإنشاء والتعديل تلقائياً

## الملفات المضافة/المحدثة

### النماذج الجديدة
1. **CourseAttendanceForm.cs/.Designer.cs** - إدارة حضور الدورات
2. **CourseEvaluationForm.cs/.Designer.cs** - تقييم الدورات
3. **CourseNotificationsForm.cs/.Designer.cs** - إدارة الإشعارات
4. **CourseDetailsForm.cs/.Designer.cs** - التفاصيل المتقدمة

### الملفات المساعدة
1. **CourseManagementHelper.cs** - دوال مساعدة متقدمة
2. **CreateCoursesTables.sql** - سكريبت قاعدة البيانات

### الملفات المحدثة
1. **Course.cs** - نموذج البيانات المحدث
2. **DatabaseHelper.cs** - دوال قاعدة البيانات الجديدة
3. **MainForm.cs/.Designer.cs** - القائمة المنسدلة للدورات

## قاعدة البيانات

### الجداول الجديدة
1. **CourseAttendance** - حضور الدورات
2. **CourseEvaluations** - تقييم الدورات  
3. **CourseNotifications** - إشعارات الدورات

### الحقول المضافة لجدول Courses
- Status, Location, MaxParticipants, CurrentParticipants
- Description, Prerequisites, CertificateIssued, Cost
- Priority, NotificationSent, CreatedBy, CreatedDate
- UpdatedBy, UpdatedDate, IsActive

## التثبيت والإعداد

### 1. تحديث قاعدة البيانات
```sql
-- تشغيل سكريبت CreateCoursesTables.sql
-- سيقوم بإضافة الجداول والحقول الجديدة تلقائياً
```

### 2. إعادة بناء المشروع
```bash
# إعادة بناء المشروع لتضمين الملفات الجديدة
Build -> Rebuild Solution
```

## كيفية الاستخدام

### 1. الوصول للنظام
- من القائمة الرئيسية: **إدارة الدورات**
- اختر النموذج المطلوب من القائمة المنسدلة

### 2. إدارة الدورات الأساسية
- إضافة/تعديل/حذف الدورات
- البحث والتصفية
- طباعة التقارير

### 3. تسجيل الحضور
- اختيار الدورة والموظف
- تحديد تاريخ الحضور
- تسجيل الحضور/الغياب مع الملاحظات

### 4. تقييم الدورات
- اختيار الدورة والموظف
- إعطاء تقييم من 1-10
- إضافة تعليقات مفصلة

### 5. إدارة الإشعارات
- إنشاء إشعارات فردية أو جماعية
- تحديد نوع ومستوى الأولوية
- متابعة حالة قراءة الإشعارات

### 6. التفاصيل المتقدمة
- تحديث معلومات الدورة الشاملة
- إرسال إشعارات للمشاركين
- تتبع تاريخ الإنشاء والتحديث

## المميزات التقنية

### 1. الأداء
- فهارس محسنة لقاعدة البيانات
- استعلامات محسنة للبحث والتصفية
- تحميل البيانات بشكل غير متزامن

### 2. الأمان
- التحقق من صحة البيانات
- حماية من SQL Injection
- تتبع المستخدم للعمليات

### 3. سهولة الاستخدام
- واجهات عربية بالكامل
- تصميم متجاوب
- رسائل خطأ واضحة
- نصائح أدوات مفيدة

### 4. التقارير
- تصدير إلى Excel
- طباعة التقارير
- إحصائيات شاملة
- تقارير مخصصة

## الدوال المساعدة المتقدمة

### CourseManagementHelper
- **SendUpcomingCourseNotificationsAsync()** - إرسال إشعارات تلقائية
- **GetCourseStatistics()** - حساب الإحصائيات
- **GetExpiredCourses()** - الدورات المنتهية
- **UpdateExpiredCoursesStatusAsync()** - تحديث الحالة تلقائياً
- **GetComprehensiveCourseReport()** - تقرير شامل

## الصيانة والتطوير المستقبلي

### التحسينات المقترحة
1. إضافة نظام تقييم المدربين
2. ربط مع التقويم الخارجي
3. إشعارات عبر البريد الإلكتروني
4. تقارير تحليلية متقدمة
5. نظام حجز القاعات

### الصيانة الدورية
- تشغيل UpdateExpiredCoursesStatusAsync() يومياً
- تشغيل SendUpcomingCourseNotificationsAsync() يومياً
- نسخ احتياطي منتظم لقاعدة البيانات
- مراجعة الأداء شهرياً

## الدعم والمساعدة
للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---
**تاريخ التطوير:** يناير 2025  
**الإصدار:** 2.0  
**المطور:** فريق تطوير نظام الموارد البشرية