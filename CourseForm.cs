using System;
using System.Data;
using System.Windows.Forms;
using System.Drawing;
using ClosedXML.Excel;
using EmployeeManagementSystem.LoadingGui;
using System.Data.SqlClient;


namespace EmployeeManagementSystem
{
    public partial class CourseForm : Form
    {
        private int? selectedCourseId = null;

        // تعريف الحدث بشكل صحيح مع معالجة nullability
        private async void btnPrintReport_Click(object? sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(cmbEmployeeName.Text))
                {
                    MessageBox.Show("الرجاء اختيار اسم الموظف أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                string reportPath = DatabaseHelper.GenerateCourseReport(cmbEmployeeName.Text);
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = reportPath,
                    UseShellExecute = true
                });

                // تسجيل عملية إنشاء التقرير الفردي
                var employeeName = cmbEmployeeName.Text;
                var description = $"إنشاء تقرير دورات للموظف: {employeeName}";
                var reportTitle = $"تقرير دورات الموظف {employeeName}";
                
                await ActivityLogHelper.LogReportGenerationAsync(
                    description,
                    "Courses",
                    1, // عدد الموظفين في التقرير
                    reportTitle,
                    "عادي"
                );
            }
            catch (Exception ex)
            {
                // تسجيل فشل إنشاء التقرير
                var employeeName = cmbEmployeeName.Text;
                var description = $"فشل في إنشاء تقرير دورات للموظف: {employeeName}";
                
                await ActivityLogHelper.LogFailedOperationAsync(
                    description,
                    "Courses",
                    ex.Message,
                    null,
                    "حرج"
                );
                
                MessageBox.Show($"حدث خطأ أثناء إنشاء التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public CourseForm()
        {
            InitializeComponent();

            // إزالة جميع الأعمدة الموجودة
            dataGridView1.DataSource = null;
            dataGridView1.Columns.Clear();
            dataGridView1.AutoGenerateColumns = false;
            
            // تسجيل فتح النموذج
            _ = System.Threading.Tasks.Task.Run(async () => await ActivityLogHelper.LogFormAccessAsync("فتح نموذج الدورات"));
            
            // تسجيل إغلاق النموذج عند الإغلاق
            this.FormClosing += async (sender, e) => 
            {
                await ActivityLogHelper.LogFormAccessAsync("إغلاق نموذج الدورات");
            };

            // إضافة الأعمدة يدوياً مع التنسيق المناسب
            dataGridView1.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    DataPropertyName = "المعرف",
                    HeaderText = "المعرف",
                    Name = "CourseId",
                    ReadOnly = true,
                    AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells
                },
                 new DataGridViewTextBoxColumn
                {
                    DataPropertyName = "الصنف",
                    HeaderText = "الصنف",
                    Name = "Category",
                    AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells
                },
                new DataGridViewTextBoxColumn
                {
                    DataPropertyName = "اسم الموظف",
                    HeaderText = "اسم الموظف",
                    Name = "EmployeeName",
                    AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
                },
                new DataGridViewTextBoxColumn
                {
                    DataPropertyName = "رقم الدورة",
                    HeaderText = "رقم الدورة",
                    Name = "CourseNumber",
                    AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells
                },
                new DataGridViewTextBoxColumn
                {
                    DataPropertyName = "نوع الدورة",
                    HeaderText = "نوع الدورة",
                    Name = "CourseType",
                    AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
                },
                new DataGridViewTextBoxColumn
                {
                    DataPropertyName = "تاريخ البداية",
                    HeaderText = "تاريخ البداية",
                    Name = "StartDate",
                    AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells
                },
                new DataGridViewTextBoxColumn
                {
                    DataPropertyName = "تاريخ النهاية",
                    HeaderText = "تاريخ النهاية",
                    Name = "EndDate",
                    AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells
                },
                new DataGridViewTextBoxColumn
                {
                    DataPropertyName = "عدد الأيام",
                    HeaderText = "عدد الأيام",
                    Name = "DaysCount",
                    AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells
                },
                new DataGridViewTextBoxColumn
                {
                    DataPropertyName = "درجة التخرج",
                    HeaderText = "درجة التخرج",
                    Name = "GraduationGrade",
                    AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells
                }
            });

            // تعيين خصائص العرض
            dataGridView1.RightToLeft = RightToLeft.Yes;
            dataGridView1.EnableHeadersVisualStyles = false;
            dataGridView1.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(45, 66, 91);
            dataGridView1.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dataGridView1.ColumnHeadersDefaultCellStyle.Font = new Font("Cairo", 12F, FontStyle.Bold);
            dataGridView1.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridView1.ColumnHeadersHeight = 50;

            dataGridView1.DefaultCellStyle.Font = new Font("Cairo", 12F);
            dataGridView1.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridView1.DefaultCellStyle.SelectionBackColor = Color.FromArgb(87, 115, 153);

            dataGridView1.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(237, 243, 247);
            dataGridView1.BackgroundColor = Color.White;
            dataGridView1.BorderStyle = BorderStyle.None;
            dataGridView1.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dataGridView1.GridColor = Color.FromArgb(223, 230, 233);
            dataGridView1.RowTemplate.Height = 35;
            dataGridView1.SelectionMode = DataGridViewSelectionMode.FullRowSelect;

            // تحميل البيانات
            LoadEmployeeNames();
            LoadCourses();
            ClearForm();

            // تطبيق الثيم
            string theme = "default";
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT TOP 1 Theme FROM Settings";
                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            theme = reader["Theme"]?.ToString() ?? "default";
                        }
                    }
                }
            }
            catch
            {
                theme = "default";
            }

            ThemeManager.ApplyThemeToForm(this);


            // تهيئة قائمة الأشهر
            cmbMonthFilter.Items.AddRange(new string[] {
                "كل الأشهر",
                "يناير",
                "فبراير",
                "مارس",
                "إبريل",
                "مايو",
                "يونيو",
                "يوليو",
                "أغسطس",
                "سبتمبر",
                "أكتوبر",
                "نوفمبر",
                "ديسمبر"
            });
            cmbMonthFilter.SelectedIndex = 0;
        }

        private void LoadEmployeeNames()
        {
            try
            {

                var employees = DatabaseHelper.GetAllEmployees();
                cmbEmployeeName.Items.Clear();
                foreach (DataRow row in employees.Rows)
                {
                    cmbEmployeeName.Items.Add(row["الاسم"].ToString());
                }
                UpdateNoDocumentsLabel();
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تحميل أسماء الموظفين: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadCourses(int? monthFilter = null)
        {
            try
            {
                var table = DatabaseHelper.GetAllCourses();

                if (monthFilter.HasValue && monthFilter.Value > 0)
                {                    // فلترة البيانات حسب الشهر
                    var filteredTable = table.Clone();
                    foreach (DataRow row in table.Rows)
                    {
                        bool showInMonth = false;

                        if (DateTime.TryParse(row["تاريخ البداية"].ToString(), out DateTime startDate) &&
                            DateTime.TryParse(row["تاريخ النهاية"].ToString(), out DateTime endDate))
                        {
                            // إظهار الدورة إذا كان الشهر المحدد يقع بين تاريخ البداية والنهاية
                            var currentDate = new DateTime(startDate.Year, monthFilter.Value, 1);
                            showInMonth = startDate.Date <= currentDate.Date.AddMonths(1).AddDays(-1) &&
                                        endDate.Date >= currentDate.Date;
                        }

                        if (showInMonth)
                        {
                            filteredTable.ImportRow(row);
                        }
                    }
                    table = filteredTable;
                }

                dataGridView1.DataSource = table;
                dataGridView1.Refresh();
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تحميل الدورات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearForm()
        {
            selectedCourseId = null;
            cmbEmployeeName.SelectedIndex = -1;
            txtCourseType.Clear();
            txtCourseNumber.Clear();
            dtStartDate.Value = DateTime.Today;
            dtEndDate.Value = DateTime.Today;
            txtSearch.Clear();
            txtCategory.Clear();
            txtGraduationGrade.Clear();
            btnAdd.Enabled = true;
            btnUpdate.Enabled = false;
            btnDelete.Enabled = false;
            dataGridView1.ClearSelection();
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(cmbEmployeeName.Text))
            {
                MessageBox.Show("الرجاء اختيار اسم الموظف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtCourseType.Text))
            {
                MessageBox.Show("الرجاء إدخال نوع الدورة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtCourseNumber.Text))
            {
                MessageBox.Show("الرجاء إدخال رقم الدورة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (dtEndDate.Value < dtStartDate.Value)
            {
                MessageBox.Show("تاريخ النهاية يجب أن يكون بعد تاريخ البداية", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private Course GetFormData()
        {
            return new Course
            {
                CourseId = selectedCourseId ?? 0,
                EmployeeName = cmbEmployeeName.Text,
                CourseType = txtCourseType.Text,
                CourseNumber = txtCourseNumber.Text,
                Category = txtCategory.Text,
                StartDate = dtStartDate.Value,
                EndDate = dtEndDate.Value,
                DaysCount = (dtEndDate.Value - dtStartDate.Value).Days + 1,
                GraduationGrade = txtGraduationGrade.Text
            };
        }

        //private void btnAdd_Click(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        if (!ValidateForm()) return; var course = GetFormData();
        //        DatabaseHelper.AddCourse(course);

        //        MessageBox.Show("تمت إضافة الدورة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        //        LoadCourses();
        //        ToastHelper.ShowAddToast();
        //        UpdateNoDocumentsLabel();
        //        ClearForm();
        //    }
        //    catch (Exception ex)
        //    {
        //        MessageBox.Show("حدث خطأ أثناء إضافة الدورة: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        //    }
        //}
        private async void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;

                var course = GetFormData();

                await Task.Run(() => DatabaseHelper.AddCourse(course));

                // تسجيل عملية الإضافة
                var employeeName = cmbEmployeeName.Text;
                var description = $"إضافة دورة جديدة للموظف: {employeeName}";
                var details = $"نوع الدورة: {course.CourseType}, رقم الدورة: {course.CourseNumber}, من: {course.StartDate:yyyy-MM-dd} إلى: {course.EndDate:yyyy-MM-dd}";
                
                await ActivityLogHelper.LogAddOperationAsync(
                    description,
                    "Courses",
                    course.CourseId,
                    course,
                    "عادي"
                );

                MessageBox.Show("تمت إضافة الدورة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                var data = await Task.Run(() => DatabaseHelper.GetAllCourses());

                dataGridView1.DataSource = data;

                ToastHelper.ShowAddToast();
                UpdateNoDocumentsLabel();
                ClearForm();
            }
            catch (Exception ex)
            {
                // تسجيل فشل العملية
                var employeeName = cmbEmployeeName.Text;
                var description = $"فشل في إضافة دورة للموظف: {employeeName}";
                
                await ActivityLogHelper.LogFailedOperationAsync(
                    description,
                    "Courses",
                    ex.Message,
                    null,
                    "حرج"
                );
                
                MessageBox.Show("حدث خطأ أثناء إضافة الدورة: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
           
        }
        private async void btnUpdate_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm() || !selectedCourseId.HasValue) return;

                // إنشاء كائن للبيانات القديمة من النموذج الحالي
                var oldCourse = new Course
                {
                    CourseId = selectedCourseId.Value,
                    EmployeeName = cmbEmployeeName.Text,
                    CourseType = txtCourseType.Text,
                    CourseNumber = txtCourseNumber.Text,
                    Category = txtCategory.Text,
                    StartDate = dtStartDate.Value,
                    EndDate = dtEndDate.Value,
                    DaysCount = (dtEndDate.Value - dtStartDate.Value).Days + 1,
                    GraduationGrade = txtGraduationGrade.Text
                };

                var course = GetFormData();

                await Task.Run(() => DatabaseHelper.UpdateCourse(course));

                // تسجيل عملية التحديث
                var employeeName = cmbEmployeeName.Text;
                var description = $"تحديث دورة للموظف: {employeeName}";
                
                await ActivityLogHelper.LogUpdateOperationAsync(
                    description,
                    "Courses",
                    selectedCourseId.Value,
                    oldCourse,
                    course,
                    "عادي"
                );

                MessageBox.Show("تم تحديث الدورة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                var data = await Task.Run(() => DatabaseHelper.GetAllCourses());

                dataGridView1.DataSource = data;

                ToastHelper.ShowEditToast();
                UpdateNoDocumentsLabel();
                ClearForm();
            }
            catch (Exception ex)
            {
                // تسجيل فشل العملية
                var employeeName = cmbEmployeeName.Text;
                var description = $"فشل في تحديث دورة للموظف: {employeeName}";
                
                await ActivityLogHelper.LogFailedOperationAsync(
                    description,
                    "Courses",
                    ex.Message,
                    selectedCourseId,
                    "حرج"
                );
                
                MessageBox.Show("حدث خطأ أثناء تحديث الدورة: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
           
        }

        //private void btnUpdate_Click(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        if (!ValidateForm() || !selectedCourseId.HasValue) return;

        //        var course = GetFormData();
        //        DatabaseHelper.UpdateCourse(course);
        //        MessageBox.Show("تم تحديث الدورة بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        //        LoadCourses();
        //        ToastHelper.ShowEditToast();
        //        UpdateNoDocumentsLabel();
        //        ClearForm();
        //    }
        //    catch (Exception ex)
        //    {
        //        MessageBox.Show("حدث خطأ أثناء تحديث الدورة: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        //    }
        //}
        //private void btnDelete_Click(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        if (dataGridView1.SelectedRows.Count == 0) return;

        //        string message = dataGridView1.SelectedRows.Count == 1
        //            ? "هل أنت متأكد من حذف هذه الدورة؟"
        //            : $"هل أنت متأكد من حذف {dataGridView1.SelectedRows.Count} دورة؟";

        //        if (MessageBox.Show(message, "تأكيد الحذف",
        //            MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
        //        {
        //            foreach (DataGridViewRow row in dataGridView1.SelectedRows)
        //            {
        //                int courseId = Convert.ToInt32(row.Cells["CourseId"].Value);
        //                DatabaseHelper.DeleteCourse(courseId);
        //            }

        //            MessageBox.Show("تم حذف الدورات بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        //            LoadCourses();
        //            ToastHelper.ShowDeleteToast();
        //            UpdateNoDocumentsLabel();
        //            ClearForm();
        //            chkSelectAll.Checked = false;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        MessageBox.Show("حدث خطأ أثناء حذف الدورات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        //    }
        //}
        private async void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridView1.SelectedRows.Count == 0) return;

                string message = dataGridView1.SelectedRows.Count == 1
                    ? "هل أنت متأكد من حذف هذه الدورة؟"
                    : $"هل أنت متأكد من حذف {dataGridView1.SelectedRows.Count} دورة؟";

                if (MessageBox.Show(message, "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
                    return;

                List<int> courseIds = dataGridView1.SelectedRows
                    .Cast<DataGridViewRow>()
                    .Select(r => Convert.ToInt32(r.Cells["CourseId"].Value))
                    .ToList();

                // الحصول على بيانات الدورات قبل الحذف لتسجيل العملية
                var coursesToDelete = new List<Course>();
                foreach (DataGridViewRow row in dataGridView1.SelectedRows)
                {
                    var course = new Course
                    {
                        CourseId = Convert.ToInt32(row.Cells["CourseId"].Value),
                        EmployeeName = row.Cells["EmployeeName"].Value?.ToString() ?? "",
                        CourseType = row.Cells["CourseType"].Value?.ToString() ?? "",
                        CourseNumber = row.Cells["CourseNumber"].Value?.ToString() ?? "",
                        Category = row.Cells["Category"].Value?.ToString() ?? "",
                        GraduationGrade = row.Cells["GraduationGrade"].Value?.ToString() ?? ""
                    };
                    
                    if (DateTime.TryParse(row.Cells["StartDate"].Value?.ToString(), out DateTime startDate))
                        course.StartDate = startDate;
                    if (DateTime.TryParse(row.Cells["EndDate"].Value?.ToString(), out DateTime endDate))
                        course.EndDate = endDate;
                    if (int.TryParse(row.Cells["DaysCount"].Value?.ToString(), out int daysCount))
                        course.DaysCount = daysCount;
                    
                    coursesToDelete.Add(course);
                }

                await Task.Run(() =>
                {
                    foreach (int id in courseIds)
                        DatabaseHelper.DeleteCourse(id);
                });

                // تسجيل عمليات الحذف
                foreach (var course in coursesToDelete)
                {
                    var description = $"حذف دورة للموظف: {course.EmployeeName}";
                    
                    await ActivityLogHelper.LogDeleteOperationAsync(
                        description,
                        "Courses",
                        course.CourseId,
                        course,
                        "مهم"
                    );
                }

                MessageBox.Show("تم حذف الدورات بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                var data = await Task.Run(() => DatabaseHelper.GetAllCourses());

                dataGridView1.DataSource = data;

                ToastHelper.ShowDeleteToast();
                UpdateNoDocumentsLabel();
                ClearForm();
                chkSelectAll.Checked = false;
            }
            catch (Exception ex)
            {
                // تسجيل فشل العملية
                var description = $"فشل في حذف {dataGridView1.SelectedRows.Count} دورة";
                
                await ActivityLogHelper.LogFailedOperationAsync(
                    description,
                    "Courses",
                    ex.Message,
                    null,
                    "حرج"
                );
                
                MessageBox.Show("حدث خطأ أثناء حذف الدورات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
           
        }

        private async void btnExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                using (SaveFileDialog sfd = new SaveFileDialog())
                {
                    sfd.Filter = "Excel Workbook|*.xlsx";
                    sfd.FileName = "تقرير_الدورات_" + DateTime.Now.ToString("yyyy-MM-dd") + ".xlsx";

                    if (sfd.ShowDialog() == DialogResult.OK)
                    {
                        using (XLWorkbook workbook = new XLWorkbook())
                        {
                            var worksheet = workbook.Worksheets.Add("الدورات");

                            // Add headers
                            for (int i = 0; i < dataGridView1.Columns.Count; i++)
                            {
                                worksheet.Cell(1, i + 1).Value = dataGridView1.Columns[i].HeaderText;
                            }

                            // Add data
                            for (int i = 0; i < dataGridView1.Rows.Count; i++)
                            {
                                for (int j = 0; j < dataGridView1.Columns.Count; j++)
                                {
                                    worksheet.Cell(i + 2, j + 1).Value = dataGridView1.Rows[i].Cells[j].Value?.ToString() ?? "";
                                }
                            }

                            // Format as table
                            var range = worksheet.Range(1, 1, dataGridView1.Rows.Count + 1, dataGridView1.Columns.Count);
                            range.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            range.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                            worksheet.Row(1).Style.Fill.BackgroundColor = XLColor.LightGray;
                            worksheet.Row(1).Style.Font.Bold = true;

                            // Adjust column widths
                            worksheet.Columns().AdjustToContents();

                            workbook.SaveAs(sfd.FileName);
                        }

                        // تسجيل عملية التصدير
                        var description = "تصدير بيانات الدورات إلى Excel";
                        var recordCount = dataGridView1.Rows.Count;
                        var fileName = Path.GetFileName(sfd.FileName);
                        var details = $"عدد السجلات المصدرة: {recordCount}, اسم الملف: {fileName}";
                        
                        await ActivityLogHelper.LogExportAsync(
                            description,
                            "Courses",
                            recordCount,
                            fileName,
                            "عادي"
                        );

                        MessageBox.Show("تم تصدير البيانات بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                // تسجيل فشل التصدير
                var description = "فشل في تصدير بيانات الدورات إلى Excel";
                
                await ActivityLogHelper.LogFailedOperationAsync(
                    description,
                    "Courses",
                    ex.Message,
                    null,
                    "حرج"
                );
                
                MessageBox.Show("حدث خطأ أثناء تصدير البيانات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }
       
        private async void btnPrintAll_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable table = DatabaseHelper.GetAllCourses();
                string reportTitle = "تقرير الدورات";

                // إذا كان هناك فلتر شهري محدد
                if (cmbMonthFilter.SelectedIndex > 0)
                {
                    var filteredTable = table.Clone();
                    foreach (DataRow row in table.Rows)
                    {
                        bool showInMonth = false;

                        if (DateTime.TryParse(row["تاريخ البداية"].ToString(), out DateTime startDate) &&
                            DateTime.TryParse(row["تاريخ النهاية"].ToString(), out DateTime endDate))
                        {
                            var currentDate = new DateTime(startDate.Year, cmbMonthFilter.SelectedIndex, 1);
                            showInMonth = startDate.Date <= currentDate.Date.AddMonths(1).AddDays(-1) &&
                                        endDate.Date >= currentDate.Date;
                        }

                        if (showInMonth)
                        {
                            filteredTable.ImportRow(row);
                        }
                    }
                    table = filteredTable;
                    reportTitle = $"تقرير الدورات - شهر {cmbMonthFilter.Text}";
                }

                GenerateCoursesTableReport(table, reportTitle);

                // تسجيل عملية إنشاء التقرير
                var description = $"إنشاء تقرير الدورات: {reportTitle}";
                var recordCount = table.Rows.Count;
                var details = $"عدد السجلات في التقرير: {recordCount}";
                
                await ActivityLogHelper.LogReportGenerationAsync(
                    description,
                    "Courses",
                    recordCount,
                    reportTitle,
                    "عادي"
                );
            }
            catch (Exception ex)
            {
                // تسجيل فشل إنشاء التقرير
                var description = "فشل في إنشاء تقرير الدورات";
                
                await ActivityLogHelper.LogFailedOperationAsync(
                    description,
                    "Courses",
                    ex.Message,
                    null,
                    "حرج"
                );
                
                MessageBox.Show("حدث خطأ أثناء إنشاء التقرير: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void chkSelectAll_CheckedChanged(object sender, EventArgs e)
        {
            // تحديد أو إلغاء تحديد جميع الصفوف في DataGridView
            foreach (DataGridViewRow row in dataGridView1.Rows)
            {
                row.Selected = chkSelectAll.Checked;
            }
        }

        private void cmbMonthFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadCourses(cmbMonthFilter.SelectedIndex);
        }
        private string GetPrintStyles()
        {
            return @"
    @page {
        size: A4;
        margin: 1cm;
    }
    @media print {
        body {
            margin: 0;
            background: white;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
        
        .header, .courses-list, .summary {
            box-shadow: none;
            border: 1px solid #ddd;
            break-inside: avoid;
            page-break-inside: avoid;
        }

        table { 
            font-size: 12px;
            width: 100%;
        }
        
        th, td {
            padding: 6px;
        }

        .stats-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .stat-item {
            flex: 1;
            min-width: 200px;
        }

        .stat-value {
            font-size: 18px;
        }

        .category-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .category-item {
            flex: 1;
            min-width: 180px;
        }

        .footer {
            position: fixed;
            bottom: 0;
            width: calc(100% - 2cm);
            text-align: center;
            padding: 10px;
            background: white;
        }

        .header {
            margin-bottom: 15px;
            padding: 10px;
        }

        .org-name {
            font-size: 20px;
            margin-bottom: 5px;
        }

        .report-title {
            font-size: 16px;
            margin-bottom: 10px;
        }

        h2, h3 {
            margin: 10px 0;
            padding-bottom: 5px;
        }
    }
";
        }

        private void GenerateCoursesTableReport(DataTable courses, string reportTitle)
        {
            try
            {
                string companyName = "اسم المؤسسة";
                try
                {
                    using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                    {
                        connection.Open();
                        string sql = "SELECT TOP 1 CompanyName FROM Settings";
                        using (var command = new SqlCommand(sql, connection))
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                companyName = reader["CompanyName"]?.ToString() ?? "اسم المؤسسة";
                            }
                        }
                    }
                }
                catch
                {
                    companyName = "اسم المؤسسة";
                }


                // إنشاء HTML مباشرة في المتصفح
                string html = $@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>{reportTitle}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');
        
        body {{ 
            font-family: 'Cairo', sans-serif;
            margin: 40px;
            direction: rtl;
            background-color: #f5f5f5;
            color: #333;
            font-size: 14px;
        }}
        
        .header {{
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        
        .org-name {{
            font-size: 22px;
            font-weight: bold;
            color: #45678a;
            margin-bottom: 8px;
        }}
        
        .report-title {{
            font-size: 18px;
            color: #666;
            margin-bottom: 15px;
        }}
        
        .courses-list {{
            background: #fff;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }}
        
        table {{ 
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }}
        
        th, td {{ 
            padding: 8px;
            text-align: right;
            border: 1px solid #ddd;
            font-size: 12px;
        }}
        
        th {{ 
            background-color: #45678a;
            color: white;
            font-weight: bold;
        }}
        
        tr:nth-child(even) {{ 
            background-color: #f8f9fa;
        }}
        
        .summary {{
            background: #fff;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-top: 20px;
        }}
        
        .footer {{
            margin-top: 20px;
            text-align: center;
            color: #666;
            font-size: 12px;
            padding: 10px;
        }}
        
        {GetPrintStyles()}
    </style>
</head>
<body>
    <div class=""header"">
        <div class=""org-name"">{companyName}</div>
        <div class=""report-title"">{reportTitle}</div>
    </div>

    <div class=""courses-list"">
        <table>
            <thead>
                <tr>
                    <th>الصنف</th>
                    <th>اسم الموظف</th>
                    <th>رقم الدورة</th>
                    <th>نوع الدورة</th>
                    <th>تاريخ البداية</th>
                    <th>تاريخ النهاية</th>
                    <th>عدد الأيام</th>
                    <th>درجة التخرج</th>
                </tr>
            </thead>
            <tbody>";

                foreach (DataRow row in courses.Rows)
                {
                    string startDate = "غير محدد";
                    string endDate = "غير محدد";

                    if (DateTime.TryParse(row["تاريخ البداية"]?.ToString(), out DateTime start))
                        startDate = start.ToString("dd/MM/yyyy");
                    if (DateTime.TryParse(row["تاريخ النهاية"]?.ToString(), out DateTime end))
                        endDate = end.ToString("dd/MM/yyyy");

                    html += $@"
                <tr>
                    <td>{row["الصنف"]}</td>
                    <td>{row["اسم الموظف"]}</td>
                    <td>{row["رقم الدورة"]}</td>
                    <td>{row["نوع الدورة"]}</td>
                    <td>{startDate}</td>
                    <td>{endDate}</td>
                    <td>{row["عدد الأيام"]}</td>
                    <td>{row["درجة التخرج"]}</td>
                </tr>";
                }

                html += $@"
            </tbody>
        </table>
    </div>    <div class=""summary"">
        <h2>إحصائيات الدورات</h2>
        <div class=""stats-grid"">";

                // حساب الإحصائيات
                int totalCourses = courses.Rows.Count;
                double avgDays = 0;
                double maxGrade = 0;
                double minGrade = 100;
                var coursesByCategory = new Dictionary<string, int>();

                foreach (DataRow row in courses.Rows)
                {
                    // حساب متوسط الأيام
                    if (int.TryParse(row["عدد الأيام"]?.ToString(), out int days))
                    {
                        avgDays += days;
                    }

                    // حساب الدرجات
                    if (double.TryParse(row["درجة التخرج"]?.ToString()?.Replace("%", ""), out double grade))
                    {
                        maxGrade = Math.Max(maxGrade, grade);
                        minGrade = Math.Min(minGrade, grade);
                    }

                    // تصنيف حسب الصنف
                    string category = row["الصنف"]?.ToString() ?? "غير محدد";
                    if (!coursesByCategory.ContainsKey(category))
                        coursesByCategory[category] = 0;
                    coursesByCategory[category]++;
                }

                if (totalCourses > 0)
                    avgDays /= totalCourses;

                html += $@"
            <div class=""stat-item"">
                <div class=""stat-label"">إجمالي عدد الدورات</div>
                <div class=""stat-value"">{totalCourses}</div>
            </div>
            <div class=""stat-item"">
                <div class=""stat-label"">متوسط مدة الدورات</div>
                <div class=""stat-value"">{avgDays:F1} يوم</div>
            </div>";

                if (maxGrade > 0)
                {
                    html += $@"
            <div class=""stat-item"">
                <div class=""stat-label"">أعلى درجة تخرج</div>
                <div class=""stat-value"">{maxGrade}%</div>
            </div>
            <div class=""stat-item"">
                <div class=""stat-label"">أقل درجة تخرج</div>
                <div class=""stat-value"">{minGrade}%</div>
            </div>";
                }

                html += @"</div>

        <h3>توزيع الدورات حسب الصنف</h3>
        <div class=""category-grid"">";

                foreach (var category in coursesByCategory)
                {
                    double percentage = (category.Value * 100.0) / totalCourses;
                    html += $@"
            <div class=""category-item"">
                <div class=""category-name"">{category.Key}</div>
                <div class=""category-count"">{category.Value} ({percentage:F1}%)</div>
            </div>";
                }

                html += $@"
        </div>

        <div class=""footer"">
            <p>تم إنشاء هذا التقرير في {DateTime.Now:dd/MM/yyyy HH:mm}</p>
        </div>
    </div>
    <style>
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        
        .stat-item {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }}
        
        .stat-label {{
            color: #666;
            margin-bottom: 5px;
        }}
        
        .stat-value {{
            font-size: 24px;
            color: #45678a;
            font-weight: bold;
        }}
        
        .category-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }}
        
        .category-item {{
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
        }}
        
        .category-name {{
            color: #45678a;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        
        .category-count {{
            color: #666;
        }}
        
        h2, h3 {{
            color: #45678a;
            margin: 20px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #45678a;
        }}
    </style>
</body>
</html>";

                // إنشاء محتوى HTML مؤقت وعرضه في المتصفح
                var tempFile = Path.GetTempFileName() + ".html";
                File.WriteAllText(tempFile, html, System.Text.Encoding.UTF8);
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempFile,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CourseForm_Load(object sender, EventArgs e)
        {
            ClearForm();
            UIHelper.ShowEmptyMessage(dataGridView1, lbl_NoDocuments, "لا توجد بيانات");
        }
        private void UpdateNoDocumentsLabel()
        {
            lbl_NoDocuments.Visible = dataGridView1.Rows.Count == 0;
        }
        private async void btnSearch_Click(object sender, EventArgs e)
        {
            string searchText = txtSearch.Text.Trim();

            if (string.IsNullOrWhiteSpace(searchText))
            {
                // استدعِ جميع الدورات من البداية
                await Task.Run(() =>
                {
                    Invoke((MethodInvoker)(() =>
                    {
                        LoadCourses(); // إعادة تحميل كل البيانات
                    }));
                });
                MessageBox.Show("الرجاء كتابة شيء للبحث عنه.", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return; // لا حاجة لعرض رسالة تنبيه هنا
            }

            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh();

            try
            {
                var result = await Task.Run(() => DatabaseHelper.SearchCourses(searchText));

                // تسجيل عملية البحث
                var description = $"البحث في الدورات بالكلمة المفتاحية: {searchText}";
                var resultCount = result?.Rows.Count ?? 0;
                var details = $"عدد النتائج: {resultCount}";
                
                await ActivityLogHelper.LogSearchAsync(
                    description,
                    "Courses",
                    searchText,
                    resultCount,
                    "عادي"
                );

                if (result == null || result.Rows.Count == 0)
                {
                    MessageBox.Show("لم يتم العثور على نتائج مطابقة.", "نتائج البحث", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                dataGridView1.Invoke((MethodInvoker)(() =>
                {
                    dataGridView1.DataSource = result;
                }));
            }
            catch (Exception ex)
            {
                // تسجيل فشل البحث
                var description = $"فشل في البحث في الدورات بالكلمة المفتاحية: {searchText}";
                
                await ActivityLogHelper.LogFailedOperationAsync(
                    description,
                    "Courses",
                    ex.Message,
                    null,
                    "حرج"
                );
                
                MessageBox.Show("حدث خطأ أثناء البحث: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                loadingForm.Close();
            }
        }

        private void txtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                e.SuppressKeyPress = true;
                btnSearch.PerformClick(); // ينفذ كود الزر مباشرة
            }
        }

        private void dataGridView1_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0) // تأكد أن الصف حقيقي
            {
                try
                {
                    var row = dataGridView1.Rows[e.RowIndex];

                    selectedCourseId = Convert.ToInt32(row.Cells["CourseId"].Value);
                    cmbEmployeeName.Text = row.Cells["EmployeeName"].Value?.ToString() ?? "";
                    txtCourseType.Text = row.Cells["CourseType"].Value?.ToString() ?? "";
                    txtCourseNumber.Text = row.Cells["CourseNumber"].Value?.ToString() ?? "";
                    txtCategory.Text = row.Cells["Category"].Value?.ToString() ?? "";
                    txtGraduationGrade.Text = row.Cells["GraduationGrade"].Value?.ToString() ?? "";

                    if (DateTime.TryParse(row.Cells["StartDate"].Value?.ToString(), out DateTime startDate))
                        dtStartDate.Value = startDate;

                    if (DateTime.TryParse(row.Cells["EndDate"].Value?.ToString(), out DateTime endDate))
                        dtEndDate.Value = endDate;

                    btnAdd.Enabled = false;
                    btnUpdate.Enabled = true;
                    btnDelete.Enabled = true;
                }
                catch (Exception ex)
                {
                    MessageBox.Show("حدث خطأ أثناء تحميل بيانات الدورة: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}