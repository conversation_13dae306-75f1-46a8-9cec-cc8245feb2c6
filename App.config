<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<configSections>
		<sectionGroup name="userSettings"
					  type="System.Configuration.UserSettingsGroup, System.Configuration.ConfigurationManager, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
			<section name="EmployeeManagementSystem.Properties.Settings"
					 type="System.Configuration.ClientSettingsSection, System.Configuration.ConfigurationManager, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
					 allowExeDefinition="MachineToLocalUser"
					 requirePermission="false" />
		</sectionGroup>
	</configSections>

	<userSettings>
		<EmployeeManagementSystem.Properties.Settings>
			<setting name="IsActivated" serializeAs="String">
				<value>False</value>
			</setting>
			<setting name="ActivationKey" serializeAs="String">
				<value></value>
			</setting>
			<setting name="CompanyName" serializeAs="String">
				<value>اسم المؤسسة</value>
			</setting>
			<setting name="CompanyDes" serializeAs="String">
				<value>وصف المؤسسة</value>
			</setting>
			<setting name="ToastDuration" serializeAs="String">
				<value>2000</value>
			</setting>
			<setting name="Server" serializeAs="String">
				<value>.\SQLEXPRESS</value>
			</setting>
			<setting name="AUserName" serializeAs="String">
				<value></value>
			</setting>
			<setting name="APassword" serializeAs="String">
				<value></value>
			</setting>
			<setting name="ServerType" serializeAs="String">
				<value>Local</value>
			</setting>
			<setting name="Timeout" serializeAs="String">
				<value>120</value>
			</setting>
			<setting name="DataBase" serializeAs="String">
				<value>HRMSDB</value>
			</setting>
			<setting name="Theme" serializeAs="String">
				<value></value>
			</setting>
			<setting name="CompanyLogo" serializeAs="String">
				<value></value>
			</setting>
		</EmployeeManagementSystem.Properties.Settings>
	</userSettings>

</configuration>
