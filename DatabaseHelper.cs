﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Drawing.Imaging;
using System.IO;

namespace EmployeeManagementSystem
{
    public class DatabaseHelper : IDisposable
    {
        private SqlConnection _connection;
        private bool disposed = false;

        public SqlConnection Connection
        {
            get
            {
                if (_connection == null)
                {
                    _connection = new SqlConnection(ConStringHelper.GetConnectionString());
                    _connection.Open();
                }
                return _connection;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing && _connection != null)
                {
                    _connection.Dispose();
                    _connection = null;
                }

                disposed = true;
            }
        }

        ~DatabaseHelper()
        {
            Dispose(false);
        }



        public static byte[] CompressImage(byte[] imageBytes, long quality = 50L)
        {
            using (MemoryStream inputStream = new MemoryStream(imageBytes))
            using (MemoryStream outputStream = new MemoryStream())
            {
                using (Image img = Image.FromStream(inputStream))
                {
                    ImageCodecInfo jpgEncoder = GetEncoder(ImageFormat.Jpeg);
                    if (jpgEncoder == null)
                        throw new Exception("JPEG encoder not found");

                    EncoderParameters encoderParams = new EncoderParameters(1);
                    encoderParams.Param[0] = new EncoderParameter(Encoder.Quality, quality);
                    img.Save(outputStream, jpgEncoder, encoderParams);
                }

                return outputStream.ToArray();
            }
        }

        private static ImageCodecInfo GetEncoder(ImageFormat format)
        {
            ImageCodecInfo[] codecs = ImageCodecInfo.GetImageEncoders();
            foreach (ImageCodecInfo codec in codecs)
            {
                if (codec.FormatID == format.Guid)
                {
                    return codec;
                }
            }
            return null;
        }


        public static void AddDocument(int employeeCode, string description, string fileName, byte[] fileData)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"INSERT INTO Documents (EmployeeCode, Description, FileName, FileData, UploadDate)
                       VALUES (@EmployeeCode, @Description, @FileName, @FileData, @UploadDate)";
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    command.Parameters.AddWithValue("@Description", description);
                    command.Parameters.AddWithValue("@FileName", fileName);
                    command.Parameters.AddWithValue("@FileData", fileData);
                    command.Parameters.AddWithValue("@UploadDate", DateTime.Now);
                    command.ExecuteNonQuery();
                }
            }
        }



        public static bool LoadDocumentFromDatabase(int docId, string outputFolderPath)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = "SELECT FilePath, FileData FROM Documents WHERE DocId = @DocId";

                using (SqlCommand cmd = new SqlCommand(sql, connection))
                {
                    cmd.Parameters.AddWithValue("@DocId", docId);
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            string fileNameWithPath = reader["FilePath"].ToString();
                            byte[] fileData = (byte[])reader["FileData"];

                            // الحصول على اسم الملف فقط بدون المسار
                            string fileName = Path.GetFileName(fileNameWithPath);

                            // بناء المسار الكامل داخل مجلد آمن
                            string fullOutputPath = Path.Combine(outputFolderPath, fileName);

                            // التأكد من وجود المجلد
                            if (!Directory.Exists(outputFolderPath))
                                Directory.CreateDirectory(outputFolderPath);

                            File.WriteAllBytes(fullOutputPath, fileData);
                            return true;
                        }
                    }
                }
            }

            return false; // فشل الاسترجاع
        }

        public static void SaveDocumentToDatabase(int employeeCode, string description, string filePath, byte[] fileData, string fileName)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"INSERT INTO Documents (EmployeeCode, Description, FilePath, FileData, FileName, UploadDate)
               VALUES (@EmployeeCode, @Description, @FilePath, @FileData, @FileName, @UploadDate)";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    command.Parameters.AddWithValue("@Description", description);
                    command.Parameters.AddWithValue("@FilePath", filePath);
                    command.Parameters.AddWithValue("@FileData", fileData);
                    command.Parameters.AddWithValue("@FileName", fileName);
                    command.Parameters.AddWithValue("@UploadDate", DateTime.Now);

                    command.ExecuteNonQuery();
                }
            }
        }


        public static int GetEmployeeCodeByName(string employeeName)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = "SELECT EmployeeCode FROM Employees WHERE Name = @Name";
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Name", employeeName);
                    var result = command.ExecuteScalar();
                    if (result != null && int.TryParse(result.ToString(), out int code))
                        return code;
                    else
                        return 0; // أو قيمة تعني لم يتم العثور
                }
            }
        }

        public static void UpdateEmployeePhoto(int employeeCode, string photoPath, byte[] photoData)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"
            UPDATE Employees 
            SET PhotoPath = @PhotoPath, PhotoData = @PhotoData
            WHERE EmployeeCode = @EmployeeCode";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@PhotoPath", photoPath);
                    command.Parameters.AddWithValue("@PhotoData", photoData);
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    command.ExecuteNonQuery();
                }
            }
        }
        public static byte[]? GetEmployeePhotoData(int employeeCode)
        {
            using (SqlConnection conn = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                conn.Open();
                string sql = "SELECT PhotoData FROM Employees WHERE EmployeeCode = @Code";
                using (SqlCommand cmd = new SqlCommand(sql, conn))
                {
                    cmd.Parameters.AddWithValue("@Code", employeeCode);
                    var result = cmd.ExecuteScalar();
                    if (result != DBNull.Value && result is byte[] bytes)
                        return bytes;
                    return null;
                }
            }
        }


        public static async Task<int> AddEmployeeAsync(Employee employee)
        {
            int employeeCode = 0;
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    string sql = @"INSERT INTO Employees (
    Name, MotherName, DateOfBirth, Province, Nationality,
    IdentityNumber, StartDate, AdministrativeOrder, StatisticalNumber,
    KeyCardNumber, PhoneNumber, Category, BadgeNumber, BadgeExpiryDate,
    MaritalStatus, WifeName, EducationLevel, ElectoralNumber,
    PhotoPath, PhotoData,
    IsBanned, BannedUntil, BannedBy, BannedReason, BannedDate, DepartmentId
)
VALUES (
    @Name, @MotherName, @DateOfBirth, @Province, @Nationality,
    @IdentityNumber, @StartDate, @AdministrativeOrder, @StatisticalNumber,
    @KeyCardNumber, @PhoneNumber, @Category, @BadgeNumber, @BadgeExpiryDate,
    @MaritalStatus, @WifeName, @EducationLevel, @ElectoralNumber,
    @PhotoPath, @PhotoData,
    @IsBanned, @BannedUntil, @BannedBy, @BannedReason, @BannedDate, @DepartmentId
)";


                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@Name", employee.Name ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@MotherName", employee.MotherName ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@DateOfBirth", employee.DateOfBirth.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@Province", employee.Province ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Nationality", employee.Nationality ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@IdentityNumber", employee.IdentityNumber ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@StartDate", employee.StartDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@AdministrativeOrder", employee.AdministrativeOrder ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@StatisticalNumber", employee.StatisticalNumber ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@KeyCardNumber", employee.KeyCardNumber ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@PhoneNumber", employee.PhoneNumber ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Category", employee.Category ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@BadgeNumber", employee.BadgeNumber ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@BadgeExpiryDate", employee.BadgeExpiryDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@MaritalStatus", employee.MaritalStatus ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@WifeName", employee.WifeName ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@EducationLevel", employee.EducationLevel ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@ElectoralNumber", employee.ElectoralNumber ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@PhotoPath", (object?)employee.PhotoPath ?? DBNull.Value);
                        command.Parameters.AddWithValue("@PhotoData", (object?)employee.PhotoData ?? DBNull.Value);
                        command.Parameters.AddWithValue("@IsBanned", employee.IsBanned);
                        command.Parameters.AddWithValue("@BannedUntil", (object?)employee.BannedUntil ?? DBNull.Value);
                        command.Parameters.AddWithValue("@BannedBy", (object?)employee.BannedBy ?? DBNull.Value);
                        command.Parameters.AddWithValue("@BannedReason", employee.BannedReason ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@BannedDate", (object?)employee.BannedDate ?? DBNull.Value);
                        command.Parameters.AddWithValue("@DepartmentId", employee.DepartmentId);

                        employeeCode = Convert.ToInt32(await command.ExecuteScalarAsync());
                    }
                }

                // تسجيل النشاط
                await ActivityLogService.LogActivityAsync(
                    $"إضافة موظف جديد: {employee.Name}",
                    "Employees",
                    employeeCode,
                    null,
                    employee,
                    "إضافة",
                    "عادي",
                    $"تم إضافة موظف جديد برقم {employeeCode}"
                );

                return employeeCode;
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                await ActivityLogService.LogFailedActivityAsync(
                    $"إضافة موظف جديد: {employee.Name}",
                    "Employees",
                    ex.Message,
                    null,
                    "حرج"
                );
                throw;
            }
        }

        // إبقاء الطريقة المتزامنة للتوافق مع الكود الموجود
        public static int AddEmployee(Employee employee)
        {
            return AddEmployeeAsync(employee).Result;
        }


        public static async Task UpdateEmployeeAsync(Employee employee)
        {
            Employee? oldEmployee = null;
            try
            {
                // الحصول على البيانات القديمة للمقارنة
                oldEmployee = GetEmployeeById(employee.EmployeeCode);

                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    string sql = @"UPDATE Employees SET 
                Name = @Name, 
                MotherName = @MotherName,
                DateOfBirth = @DateOfBirth,
                Province = @Province,
                Nationality = @Nationality,
                IdentityNumber = @IdentityNumber,
                StartDate = @StartDate,
                AdministrativeOrder = @AdministrativeOrder,
                StatisticalNumber = @StatisticalNumber,
                KeyCardNumber = @KeyCardNumber,
                PhoneNumber = @PhoneNumber,
                Category = @Category,
                BadgeNumber = @BadgeNumber,
                BadgeExpiryDate = @BadgeExpiryDate,
                PhotoPath = @PhotoPath,
                PhotoData = @PhotoData,
                MaritalStatus = @MaritalStatus,
                WifeName = @WifeName,
                EducationLevel = @EducationLevel,
                ElectoralNumber = @ElectoralNumber
                ElectoralNumber = @ElectoralNumber,
                IsBanned = @IsBanned,
                BannedUntil = @BannedUntil,
                BannedBy = @BannedBy,
                BannedReason = @BannedReason,
                BannedDate = @BannedDate,
                DepartmentId = @DepartmentId,
                WHERE EmployeeCode = @EmployeeCode";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@EmployeeCode", employee.EmployeeCode);
                        command.Parameters.AddWithValue("@Name", employee.Name ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@MotherName", employee.MotherName ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@DateOfBirth", employee.DateOfBirth.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@Province", employee.Province ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Nationality", employee.Nationality ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@IdentityNumber", employee.IdentityNumber ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@StartDate", employee.StartDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@AdministrativeOrder", employee.AdministrativeOrder ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@StatisticalNumber", employee.StatisticalNumber ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@KeyCardNumber", employee.KeyCardNumber ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@PhoneNumber", employee.PhoneNumber ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Category", employee.Category ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@BadgeNumber", employee.BadgeNumber ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@BadgeExpiryDate", employee.BadgeExpiryDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@PhotoPath", (object?)employee.PhotoPath ?? DBNull.Value);
                        command.Parameters.AddWithValue("@IsBanned", employee.IsBanned);
                        command.Parameters.AddWithValue("@BannedUntil", (object?)employee.BannedUntil ?? DBNull.Value);
                        command.Parameters.AddWithValue("@BannedBy", employee.BannedBy ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@BannedReason", employee.BannedReason ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@BannedDate", employee.BannedDate?.ToString("yyyy-MM-dd") ?? (object)DBNull.Value);

                        command.Parameters.AddWithValue("@DepartmentId", employee.DepartmentId);

                        // هذا مهم: استخدام النوع المناسب للـ varbinary
                        if (employee.PhotoData != null)
                            command.Parameters.Add("@PhotoData", SqlDbType.VarBinary).Value = employee.PhotoData;
                        else
                            command.Parameters.Add("@PhotoData", SqlDbType.VarBinary).Value = DBNull.Value;

                        command.Parameters.AddWithValue("@MaritalStatus", employee.MaritalStatus ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@WifeName", employee.WifeName ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@EducationLevel", employee.EducationLevel ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@ElectoralNumber", employee.ElectoralNumber ?? (object)DBNull.Value);

                        await command.ExecuteNonQueryAsync();
                    }
                }

                // تسجيل النشاط
                await ActivityLogService.LogActivityAsync(
                    $"تحديث بيانات الموظف: {employee.Name}",
                    "Employees",
                    employee.EmployeeCode,
                    oldEmployee,
                    employee,
                    "تحديث",
                    "عادي",
                    $"تم تحديث بيانات الموظف رقم {employee.EmployeeCode}"
                );
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                await ActivityLogService.LogFailedActivityAsync(
                    $"تحديث بيانات الموظف: {employee.Name}",
                    "Employees",
                    ex.Message,
                    employee.EmployeeCode,
                    "حرج"
                );
                throw;
            }
        }

        // إبقاء الطريقة المتزامنة للتوافق مع الكود الموجود
        public static void UpdateEmployee(Employee employee)
        {
            UpdateEmployeeAsync(employee).Wait();
        }



        public static async Task DeleteEmployeeAsync(int employeeCode)
        {
            Employee? employeeToDelete = null;
            try
            {
                // الحصول على بيانات الموظف قبل الحذف
                employeeToDelete = GetEmployeeById(employeeCode);

                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    await connection.OpenAsync();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            var docCommand = connection.CreateCommand();
                            docCommand.Transaction = transaction;
                            docCommand.CommandText = "DELETE FROM Documents WHERE EmployeeCode = @EmployeeCode";
                            docCommand.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                            await docCommand.ExecuteNonQueryAsync();

                            var empCommand = connection.CreateCommand();
                            empCommand.Transaction = transaction;
                            empCommand.CommandText = "DELETE FROM Employees WHERE EmployeeCode = @EmployeeCode";
                            empCommand.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                            await empCommand.ExecuteNonQueryAsync();

                            transaction.Commit();
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }

                // تسجيل النشاط
                await ActivityLogService.LogActivityAsync(
                    $"حذف الموظف: {employeeToDelete?.Name}",
                    "Employees",
                    employeeCode,
                    employeeToDelete,
                    null,
                    "حذف",
                    "مهم",
                    $"تم حذف الموظف رقم {employeeCode} نهائياً من النظام"
                );
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                await ActivityLogService.LogFailedActivityAsync(
                    $"حذف الموظف: {employeeToDelete?.Name ?? "غير معروف"}",
                    "Employees",
                    ex.Message,
                    employeeCode,
                    "حرج"
                );
                throw;
            }
        }

        // إبقاء الطريقة المتزامنة للتوافق مع الكود الموجود
        public static void DeleteEmployee(int employeeCode)
        {
            DeleteEmployeeAsync(employeeCode).Wait();
        }

        public static DataTable GetAllEmployees()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT 
    EmployeeCode as 'كود الموظف',
    Category as 'الصنف',
    Name as 'الاسم',
    MotherName as 'اسم الأم',
    MaritalStatus as 'الحالة الاجتماعية',
    WifeName as 'اسم الزوج/ـة',
    Province as 'المحافظة',
    Nationality as 'الجنسية',
    IdentityNumber as 'رقم الهوية',
    DateOfBirth as 'تاريخ الميلاد',
    EducationLevel as 'المستوى التعليمي',
    ElectoralNumber as 'الرقم الانتخابي',
    StartDate as 'تاريخ الامر الاداري',
    AdministrativeOrder as 'الأمر الإداري',
    StatisticalNumber as 'الرقم الإحصائي',
    KeyCardNumber as 'رقم الكي كارد',
    PhoneNumber as 'رقم الهاتف',
    BadgeNumber as 'رقم الباج',
    BadgeExpiryDate as 'تاريخ انتهاء الباج',
    IsBanned as 'محظور؟',
    BannedUntil as 'محظور حتى',
    BannedBy as 'تم الحظر بواسطة',
    BannedReason as 'سبب الحظر',
    BannedDate as 'تاريخ الحظر',
    DepartmentId as 'القسم',
    PhotoPath
    FROM Employees
    ORDER BY EmployeeCode";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static DataTable GetSampleEmployees(int limit = 20)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
               string sql = $@"SELECT TOP {limit}
    EmployeeCode as 'كود الموظف',
    Category as 'الصنف',
    Name as 'الاسم',
    MotherName as 'اسم الأم',
    MaritalStatus as 'الحالة الاجتماعية',
    WifeName as 'اسم الزوج/ـة',
    Province as 'المحافظة',
    Nationality as 'الجنسية',
    IdentityNumber as 'رقم الهوية',
    DateOfBirth as 'تاريخ الميلاد',
    EducationLevel as 'المستوى التعليمي',
    ElectoralNumber as 'الرقم الانتخابي',
    StartDate as 'تاريخ الامر الاداري',
    AdministrativeOrder as 'الأمر الإداري',
    StatisticalNumber as 'الرقم الإحصائي',
    KeyCardNumber as 'رقم الكي كارد',
    PhoneNumber as 'رقم الهاتف',
    BadgeNumber as 'رقم الباج',
    BadgeExpiryDate as 'تاريخ انتهاء الباج',
    IsBanned as 'محظور؟',
    BannedUntil as 'محظور حتى',
    BannedBy as 'تم الحظر بواسطة',
    BannedReason as 'سبب الحظر',
    BannedDate as 'تاريخ الحظر',
    DepartmentId as 'القسم',
    PhotoPath
    FROM Employees
    ORDER BY EmployeeCode";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static DataTable GetEmployeeDocuments(int employeeCode)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT 
                    DocId,
                    Description,
                    FilePath,
                    UploadDate
                    FROM Documents 
                    WHERE EmployeeCode = @EmployeeCode
                    ORDER BY UploadDate DESC";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    var table = new DataTable();
                    using (var adapter = new SqlDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }

        public static void AddDocument(int employeeCode, string description, string filePath)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"INSERT INTO Documents 
                    (EmployeeCode, Description, FilePath, UploadDate) 
                    VALUES 
                    (@EmployeeCode, @Description, @FilePath, @UploadDate)";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    command.Parameters.AddWithValue("@Description", description);
                    command.Parameters.AddWithValue("@FilePath", filePath);
                    command.Parameters.AddWithValue("@UploadDate", DateTime.Now);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteDocument(int docId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string getPathSql = "SELECT FilePath FROM Documents WHERE DocId = @DocId";
                string filePath = "";
                using (var command = new SqlCommand(getPathSql, connection))
                {
                    command.Parameters.AddWithValue("@DocId", docId);
                    var result = command.ExecuteScalar();
                    filePath = result?.ToString() ?? "";
                }

                string sql = "DELETE FROM Documents WHERE DocId = @DocId";
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@DocId", docId);
                    command.ExecuteNonQuery();
                }

                if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
        }

        //public static Employee GetEmployeeById(int employeeCode)
        //{
        //    using (var connection = new SqlConnection(ConnectionString))
        //    {
        //        connection.Open();
        //        string sql = "SELECT * FROM Employees WHERE EmployeeCode = @EmployeeCode";
        //        using (var command = new SqlCommand(sql, connection))
        //        {
        //            command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
        //            using (var reader = command.ExecuteReader())
        //            {
        //                if (reader.Read())
        //                {
        //                    var employee = new Employee
        //                    {
        //                        EmployeeCode = reader.GetInt32(reader.GetOrdinal("EmployeeCode")),
        //                        Name = reader.GetString(reader.GetOrdinal("Name")),
        //                        MotherName = reader.GetString(reader.GetOrdinal("MotherName")),
        //                        DateOfBirth = DateTime.Parse(reader.GetString(reader.GetOrdinal("DateOfBirth"))),
        //                        Province = reader.GetString(reader.GetOrdinal("Province")),
        //                        Nationality = reader.GetString(reader.GetOrdinal("Nationality")),
        //                        IdentityNumber = reader.GetString(reader.GetOrdinal("IdentityNumber")),
        //                        StartDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("StartDate"))),
        //                        AdministrativeOrder = reader.GetString(reader.GetOrdinal("AdministrativeOrder")),
        //                        StatisticalNumber = reader.GetString(reader.GetOrdinal("StatisticalNumber")),
        //                        KeyCardNumber = reader.GetString(reader.GetOrdinal("KeyCardNumber")),
        //                        PhoneNumber = reader.GetString(reader.GetOrdinal("PhoneNumber")),
        //                        Category = reader.GetString(reader.GetOrdinal("Category")),
        //                        BadgeNumber = reader.GetString(reader.GetOrdinal("BadgeNumber")),
        //                        MaritalStatus = reader.GetString(reader.GetOrdinal("MaritalStatus")),
        //                        WifeName = reader.GetString(reader.GetOrdinal("WifeName")),
        //                        EducationLevel = reader.GetString(reader.GetOrdinal("EducationLevel")),
        //                        ElectoralNumber = reader.GetString(reader.GetOrdinal("ElectoralNumber")),
        //                        BadgeExpiryDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("BadgeExpiryDate")))
        //                    };

        //                    if (!reader.IsDBNull(reader.GetOrdinal("PhotoPath")))
        //                    {
        //                        employee.PhotoPath = reader.GetString(reader.GetOrdinal("PhotoPath"));
        //                    }

        //                    return employee;
        //                }
        //                throw new Exception("Employee not found");
        //            }
        //        }
        //    }
        //}
        public static Employee GetEmployeeById(int employeeCode)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = "SELECT * FROM Employees WHERE EmployeeCode = @EmployeeCode";
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            var employee = new Employee();

                            int idx(string col) => reader.GetOrdinal(col);
                            bool isNull(string col) => reader.IsDBNull(idx(col));
                            string getStr(string col) => isNull(col) ? "" : reader.GetString(idx(col));
                            DateTime getDate(string col) => isNull(col) ? DateTime.MinValue : reader.GetDateTime(idx(col));


                            employee.EmployeeCode = reader.GetInt32(idx("EmployeeCode"));
                            employee.Name = getStr("Name");
                            employee.MotherName = getStr("MotherName");
                            employee.DateOfBirth = getDate("DateOfBirth");
                            employee.Province = getStr("Province");
                            employee.Nationality = getStr("Nationality");
                            employee.IdentityNumber = getStr("IdentityNumber");
                            employee.StartDate = getDate("StartDate");
                            employee.AdministrativeOrder = getStr("AdministrativeOrder");
                            employee.StatisticalNumber = getStr("StatisticalNumber");
                            employee.KeyCardNumber = getStr("KeyCardNumber");
                            employee.PhoneNumber = getStr("PhoneNumber");
                            employee.Category = getStr("Category");
                            employee.BadgeNumber = getStr("BadgeNumber");
                            employee.MaritalStatus = getStr("MaritalStatus");
                            employee.WifeName = getStr("WifeName");
                            employee.EducationLevel = getStr("EducationLevel");
                            employee.ElectoralNumber = getStr("ElectoralNumber");
                            employee.BadgeExpiryDate = getDate("BadgeExpiryDate");
                            employee.PhotoPath = getStr("PhotoPath");

                            return employee;
                        }

                        throw new Exception("Employee not found");
                    }
                }
            }
        }

        public static void AddEmployeeDocument(int employeeCode, string filePath, string description)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"INSERT INTO Documents (EmployeeCode, FilePath, Description, UploadDate) 
                              VALUES (@EmployeeCode, @FilePath, @Description, @UploadDate)";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    command.Parameters.AddWithValue("@FilePath", filePath);
                    command.Parameters.AddWithValue("@Description", description);
                    command.Parameters.AddWithValue("@UploadDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    command.ExecuteNonQuery();
                }
            }
        }

        public int ExecuteNonQuery(string sql, SqlParameter[]? parameters = null)
        {
            if (_connection == null)
            {
                _connection = new SqlConnection(ConStringHelper.GetConnectionString());
            }

            using var command = new SqlCommand(sql, _connection);
            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            _connection.Open();
            try
            {
                return command.ExecuteNonQuery();
            }
            finally
            {
                _connection.Close();
            }
        }

        public SqlDataReader ExecuteReader(string sql, SqlParameter[]? parameters = null)
        {
            if (_connection == null)
            {
                _connection = new SqlConnection(ConStringHelper.GetConnectionString());
            }

            var command = new SqlCommand(sql, _connection);
            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }
            _connection.Open();
            return command.ExecuteReader(CommandBehavior.CloseConnection);
        }

        public static DataTable SearchEmployees(string searchTerm)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT 
                    EmployeeCode as 'كود الموظف',
                    Name as 'الاسم',
                    MotherName as 'اسم الأم',
                    DateOfBirth as 'تاريخ الميلاد',
                    Province as 'المحافظة',
                    Nationality as 'الجنسية',
                    IdentityNumber as 'رقم الهوية',
                    StartDate as 'تاريخ الامر الاداري',
                    AdministrativeOrder as 'الأمر الإداري',
                    StatisticalNumber as 'الرقم الإحصائي',
                    KeyCardNumber as 'رقم الكي كارد',
                    PhoneNumber as 'رقم الهاتف',
                    Category as 'الصنف',
                    BadgeNumber as 'رقم الباج',
                    BadgeExpiryDate as 'تاريخ انتهاء الباج',
                    MaritalStatus as 'الحالة الاجتماعية',
                    WifeName as 'اسم الزوج/ـة',
                    EducationLevel as 'المستوى التعليمي',
                    ElectoralNumber as 'الرقم الانتخابي',
                    PhotoPath
                    FROM Employees
                    WHERE Name LIKE @Search 
                    OR MotherName LIKE @Search 
                    OR Province LIKE @Search 
                    OR Nationality LIKE @Search 
                    OR IdentityNumber LIKE @Search 
                    OR AdministrativeOrder LIKE @Search 
                    OR StatisticalNumber LIKE @Search 
                    OR KeyCardNumber LIKE @Search 
                    OR PhoneNumber LIKE @Search 
                    OR Category LIKE @Search 
                    OR MaritalStatus LIKE @Search
                    OR WifeName LIKE @Search
                    OR EducationLevel LIKE @Search
                    OR ElectoralNumber LIKE @Search
                    OR BadgeNumber LIKE @Search";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Search", $"%{searchTerm}%");
                    var table = new DataTable();
                    using (var adapter = new SqlDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }

        public static string GenerateAllEmployeesHtml()
        {
            var employees = GetAllEmployees();
            System.Text.StringBuilder html = new System.Text.StringBuilder();
            html.Append(@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>قائمة الموظفين</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
        
        body { 
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px; 
            direction: rtl;
            background-color: #f5f5f5;
        }
        .report-container {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            margin: 0 auto;
            overflow-x: auto;
        }
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #45678a 0%, #2c3e50 100%);
            color: white;
            border-radius: 10px;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        .report-header h1 {
            font-size: 28px;
            margin: 0;
            padding: 0;
        }
        .search-container {
            margin: 20px 0;
            text-align: center;
        }
        .search-box {
            width: 50%;
            max-width: 400px;
            padding: 10px 15px;
            border: 2px solid #45678a;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
        }
        table { 
            width: 100%;
            border-collapse: collapse; 
            margin: 20px 0;
            background: white;
        }
        th, td { 
            border: 1px solid #ddd; 
            padding: 6px 8px;
            text-align: center;
            font-size: 13px;
            white-space: nowrap;
        }
        th { 
            background: linear-gradient(135deg, #45678a 0%, #2c3e50 100%);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        tr:nth-child(even) { 
            background-color: #f8f9fa; 
        }
        tr:hover {
            background-color: #f1f4f7;
        }
        .photo-cell {
            width: 50px;
            text-align: center;
        }
        .photo-cell img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #45678a;
            vertical-align: middle;
        }
        .actions {
            text-align: center;
            white-space: nowrap;
        }
        .button {
            display: inline-block;
            padding: 4px 8px;
            background: #45678a;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 12px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #334d6e;
        }
        .date-printed {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
        @media print {
            @page {
                size: landscape;
            }
            body {
                margin: 0;
                background: white;
            }
            .search-container {
                display: none;
            }
            .button {
                display: none;
            }
        }
    </style>
    <script>
        function searchEmployees() {
            var input = document.getElementById('searchInput');
            var filter = input.value.toLowerCase();
            var table = document.getElementById('employeesTable');
            var rows = table.getElementsByTagName('tr');

            for (var i = 1; i < rows.length; i++) {
                var display = false;
                var cells = rows[i].getElementsByTagName('td');
                for (var j = 0; j < cells.length; j++) {
                    var cell = cells[j];
                    if (cell) {
                        var text = cell.textContent || cell.innerText;
                        if (text.toLowerCase().indexOf(filter) > -1) {
                            display = true;
                            break;
                        }
                    }
                }
                rows[i].style.display = display ? '' : 'none';
            }
        }
    </script>
</head>
<body>
    <div class=""report-container"">
        <div class=""report-header"">
            <h1>قائمة الموظفين</h1>
        </div>
        
        <div class=""search-container"">
            <input type=""text"" id=""searchInput"" class=""search-box"" 
                   placeholder=""اكتب للبحث في جميع الحقول..."" 
                   onkeyup=""searchEmployees()"">
        </div>

        <table id=""employeesTable"">
            <thead>
                <tr>
                    <th>صورة</th>
                    <th>كود الموظف</th>
                    <th>الصنف</th>
                    <th>الاسم</th>
                    <th>اسم الأم</th>
                    <th>الحالة الاجتماعية</th>
                    <th>اسم الزوج/ـة</th>
                    <th>المحافظة</th>
                    <th>الجنسية</th>
                    <th>رقم الهوية</th>
                    <th>تاريخ الميلاد</th>
                    <th>المستوى التعليمي</th>
                    <th>الرقم الانتخابي</th> 
                    <th>تاريخ الامر الاداري</th>
                    <th>الأمر الإداري</th>
                    <th>الرقم الإحصائي</th>
                    <th>رقم الكي كارد</th>
                    <th>رقم الهاتف</th>
                    <th>رقم الباج</th>
                    <th>تاريخ انتهاء الباج</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>");

            foreach (DataRow row in employees.Rows)
            {
                string photoHtml = "<div style='width: 40px; height: 40px; background: #f0f0f0; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto; color: #666; font-size: 12px;'>-</div>";
                string? photoPath = row["PhotoPath"]?.ToString();
                if (!string.IsNullOrEmpty(photoPath) && File.Exists(photoPath))
                {
                    string base64Image = Convert.ToBase64String(File.ReadAllBytes(photoPath));
                    string imageFormat = Path.GetExtension(photoPath).TrimStart('.').ToLower();
                    photoHtml = $"<img src='data:image/{imageFormat};base64,{base64Image}' alt='صورة الموظف'>";
                }

                html.AppendFormat(@"
                <tr>
                    <td class=""photo-cell"">{0}</td>
                    <td>{1}</td>
                    <td>{2}</td>
                    <td>{3}</td>
                    <td>{4}</td>
                    <td>{5}</td>
                    <td>{6}</td>
                    <td>{7}</td>
                    <td>{8}</td>
                    <td>{9}</td>
                    <td>{10}</td>
                    <td>{11}</td>
                    <td>{12}</td>
                    <td>{13}</td>
                    <td>{14}</td>
                    <td>{15}</td>
                    <td>{16}</td>
                    <td>{17}</td> 
                    <td>{18}</td>
                    <td>{19}</td>
                   
                    <td class=""actions"">
                        <a href=""employee_{1}.html"" class=""button"" target=""_blank"">عرض التفاصيل</a>
                    </td>
                </tr>",
                    photoHtml,
                    row["كود الموظف"],
                    row["الصنف"],
                    row["الاسم"],
                    row["اسم الأم"],
                    row["الحالة الاجتماعية"],
                    row["اسم الزوج/ـة"],
                    row["المحافظة"],
                    row["الجنسية"],
                    row["رقم الهوية"],
                    Convert.ToDateTime(row["تاريخ الميلاد"]).ToString("dd/MM/yyyy"),
                    row["المستوى التعليمي"],
                    row["الرقم الانتخابي"],
                    Convert.ToDateTime(row["تاريخ الامر الاداري"]).ToString("dd/MM/yyyy"),
                    row["الأمر الإداري"],
                    row["الرقم الإحصائي"],
                    row["رقم الكي كارد"],
                    row["رقم الهاتف"],
                    row["رقم الباج"],
                    Convert.ToDateTime(row["تاريخ انتهاء الباج"]).ToString("dd/MM/yyyy")
                );

                // إنشاء تقرير تفصيلي لكل موظف
                int employeeCode = Convert.ToInt32(row["كود الموظف"]);
                GenerateEmployeeReport(employeeCode);
            }

            html.AppendFormat(@"
            </tbody>
               </table>
                 <div class=""date-printed"">
                    تم إصدار هذا التقرير بتاريخ {0} الساعة {1}
                    </div>
                   </div>
                 </body>
               </html>", DateTime.Now.ToString("dd/MM/yyyy"), DateTime.Now.ToString("HH:mm"));

            string htmlFilePath = Path.Combine("html", "index.html");
            Directory.CreateDirectory("html");
            File.WriteAllText(htmlFilePath, html.ToString());

            return htmlFilePath;
        }

        public static void AddVacation(Vacation vacation)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string getEmployeeIdSql = "SELECT EmployeeCode FROM Employees WHERE Name = @EmployeeName";
                    int employeeId;
                    using (var command = new SqlCommand(getEmployeeIdSql, connection))
                    {
                        command.Parameters.AddWithValue("@EmployeeName", vacation.EmployeeName);
                        var result = command.ExecuteScalar();
                        if (result == null)
                        {
                            throw new Exception($"لم يتم العثور على موظف باسم {vacation.EmployeeName}");
                        }
                        employeeId = Convert.ToInt32(result);
                    }

                    string sql = @"INSERT INTO Vacations (
                        EmployeeId, EmployeeName, VacationType, StartDate, EndDate, DaysCount, Reason, Status)
                    VALUES (
                        @EmployeeId, @EmployeeName, @VacationType, @StartDate, @EndDate, @DaysCount, @Reason, @Status)";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@EmployeeId", employeeId);
                        command.Parameters.AddWithValue("@EmployeeName", vacation.EmployeeName);
                        command.Parameters.AddWithValue("@VacationType", vacation.VacationType);
                        command.Parameters.AddWithValue("@StartDate", vacation.StartDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@EndDate", vacation.EndDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@DaysCount", vacation.DaysCount);
                        command.Parameters.AddWithValue("@Reason", (object?)vacation.Reason ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Status", vacation.Status ?? "في الانتظار");
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"خطأ في إضافة الإجازة: {ex.Message}");
                throw;
            }
        }

        public static void UpdateVacation(Vacation vacation)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string getEmployeeIdSql = "SELECT EmployeeCode FROM Employees WHERE Name = @EmployeeName";
                    int employeeId;
                    using (var command = new SqlCommand(getEmployeeIdSql, connection))
                    {
                        command.Parameters.AddWithValue("@EmployeeName", vacation.EmployeeName);
                        var result = command.ExecuteScalar();
                        if (result == null)
                        {
                            throw new Exception($"لم يتم العثور على موظف باسم {vacation.EmployeeName}");
                        }
                        employeeId = Convert.ToInt32(result);
                    }

                    string sql = @"UPDATE Vacations SET 
                        EmployeeId = @EmployeeId,
                        EmployeeName = @EmployeeName,
                        VacationType = @VacationType,
                        StartDate = @StartDate,
                        EndDate = @EndDate,
                        DaysCount = @DaysCount,
                        Reason = @Reason,
                        Status = @Status,
                        RejectionReason = @RejectionReason,
                        ApprovalDate = @ApprovalDate,
                        ApprovedBy = @ApprovedBy
                    WHERE VacationId = @VacationId";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@VacationId", vacation.VacationId);
                        command.Parameters.AddWithValue("@EmployeeId", employeeId);
                        command.Parameters.AddWithValue("@EmployeeName", vacation.EmployeeName);
                        command.Parameters.AddWithValue("@VacationType", vacation.VacationType);
                        command.Parameters.AddWithValue("@StartDate", vacation.StartDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@EndDate", vacation.EndDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@DaysCount", vacation.DaysCount);
                        command.Parameters.AddWithValue("@Reason", (object?)vacation.Reason ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Status", vacation.Status ?? "في الانتظار");
                        command.Parameters.AddWithValue("@RejectionReason", (object?)vacation.RejectionReason ?? DBNull.Value);
                        command.Parameters.AddWithValue("@ApprovalDate", (object?)vacation.ApprovalDate ?? DBNull.Value);
                        command.Parameters.AddWithValue("@ApprovedBy", (object?)vacation.ApprovedBy ?? DBNull.Value);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"خطأ في تحديث الإجازة: {ex.Message}");
                throw;
            }
        }

        public static void DeleteVacation(int vacationId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = "DELETE FROM Vacations WHERE VacationId = @VacationId";
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@VacationId", vacationId);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static DataTable GetAllVacations()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT 
                    VacationId,
                    EmployeeName,
                    VacationType,
                    StartDate,
                    EndDate,
                    DaysCount,
                    Reason,
                    Status,
                    RejectionReason,
                    ApprovalDate,
                    ApprovedBy
                FROM Vacations
                ORDER BY VacationId DESC";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        // دالة جلب الطلبات المعلقة فقط
        public static DataTable GetPendingVacationRequests()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT 
                    VacationId,
                    EmployeeName,
                    VacationType,
                    StartDate,
                    EndDate,
                    DaysCount,
                    Reason,
                    Status
                FROM Vacations
                WHERE Status = N'في الانتظار'
                ORDER BY VacationId DESC";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static DataTable SearchVacations(string searchTerm)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT 
                    VacationId,
                    EmployeeName,
                    VacationType,
                    StartDate,
                    EndDate,
                    DaysCount,
                    Reason,
                    Status,
                    RejectionReason,
                    ApprovalDate,
                    ApprovedBy
                FROM Vacations
                WHERE EmployeeName LIKE @Search 
                OR VacationType LIKE @Search 
                OR Reason LIKE @Search
                OR Status LIKE @Search
                OR RejectionReason LIKE @Search
                OR ApprovedBy LIKE @Search";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Search", $"%{searchTerm}%");
                    var table = new DataTable();
                    using (var adapter = new SqlDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }

        // دالة جلب الإجازات المعلقة فقط (للاستخدام في تطبيق الويب)
        public static DataTable GetPendingVacations()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT 
                    VacationId,
                    EmployeeName,
                    VacationType,
                    StartDate,
                    EndDate,
                    DaysCount,
                    Reason,
                    Status,
                    RejectionReason,
                    ApprovalDate,
                    ApprovedBy
                FROM Vacations
                WHERE Status IS NULL OR Status = 'معلق' OR Status = '' OR Status = 'في الانتظار'
                ORDER BY VacationId DESC";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        // دالة جلب الإجازات المعتمدة والمرفوضة
        public static DataTable GetProcessedVacations()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT 
                    VacationId,
                    EmployeeName,
                    VacationType,
                    StartDate,
                    EndDate,
                    DaysCount,
                    Reason,
                    Status,
                    RejectionReason,
                    ApprovalDate,
                    ApprovedBy
                FROM Vacations
                WHERE Status = 'معتمد' OR Status = 'مرفوض'
                ORDER BY ApprovalDate DESC";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static Vacation GetVacationById(int vacationId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = "SELECT * FROM Vacations WHERE VacationId = @VacationId";
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@VacationId", vacationId);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Vacation
                            {
                                VacationId = reader.GetInt32(reader.GetOrdinal("VacationId")),
                                EmployeeName = reader.GetString(reader.GetOrdinal("EmployeeName")),
                                VacationType = reader.GetString(reader.GetOrdinal("VacationType")),
                                StartDate = reader.GetDateTime(reader.GetOrdinal("StartDate")),
                                EndDate = reader.GetDateTime(reader.GetOrdinal("EndDate")),
                                DaysCount = reader.GetInt32(reader.GetOrdinal("DaysCount")),
                                Reason = reader.IsDBNull(reader.GetOrdinal("Reason")) ? null : reader.GetString(reader.GetOrdinal("Reason")),
                                Status = reader.IsDBNull(reader.GetOrdinal("Status")) ? "في الانتظار" : reader.GetString(reader.GetOrdinal("Status")),
                                RejectionReason = reader.IsDBNull(reader.GetOrdinal("RejectionReason")) ? null : reader.GetString(reader.GetOrdinal("RejectionReason")),
                                ApprovalDate = reader.IsDBNull(reader.GetOrdinal("ApprovalDate")) ? null : reader.GetDateTime(reader.GetOrdinal("ApprovalDate")),
                                ApprovedBy = reader.IsDBNull(reader.GetOrdinal("ApprovedBy")) ? null : reader.GetString(reader.GetOrdinal("ApprovedBy"))
                            };
                        }
                        throw new Exception("الإجازة غير موجودة");
                    }
                }
            }
        }


        public static void AddUser(User user)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"INSERT INTO Users (Username, FullName, Password, UserType)
                             VALUES (@Username, @FullName, @Password, @UserType)";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Username", user.Username);
                    command.Parameters.AddWithValue("@FullName", user.FullName);
                    command.Parameters.AddWithValue("@Password", user.Password);
                    command.Parameters.AddWithValue("@UserType", user.UserType);
                    command.ExecuteNonQuery();
                }
            }
        }

        //public static void UpdateUser(User user)
        //{
        //    using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
        //    {
        //        connection.Open();
        //        string sql = user.Password != null ?
        //            @"UPDATE Users SET 
        //                Username = @Username,
        //                FullName = @FullName,
        //                Password = @Password,
        //                UserType = @UserType
        //              WHERE UserId = @UserId" :
        //            @"UPDATE Users SET 
        //                Username = @Username,
        //                FullName = @FullName,
        //                UserType = @UserType
        //              WHERE UserId = @UserId";

        //        using (var command = new SqlCommand(sql, connection))
        //        {
        //            command.Parameters.AddWithValue("@UserId", user.UserId);
        //            command.Parameters.AddWithValue("@Username", user.Username);
        //            command.Parameters.AddWithValue("@FullName", user.FullName);
        //            command.Parameters.AddWithValue("@UserType", user.UserType);
        //            if (user.Password != null)
        //                command.Parameters.AddWithValue("@Password", user.Password);
        //            command.ExecuteNonQuery();
        //        }
        //    }
        //}
        public static void UpdateUser(User user)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = user.Password != null ?
                    @"UPDATE Users SET 
                Username = @Username,
                FullName = @FullName,
                Password = @Password,
                UserType = @UserType,
                DepartmentId = @DepartmentId
              WHERE UserId = @UserId" :
                    @"UPDATE Users SET 
                Username = @Username,
                FullName = @FullName,
                UserType = @UserType,
                DepartmentId = @DepartmentId
              WHERE UserId = @UserId";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@UserId", user.UserId);
                    command.Parameters.AddWithValue("@Username", user.Username);
                    command.Parameters.AddWithValue("@FullName", user.FullName);
                    command.Parameters.AddWithValue("@UserType", user.UserType);
                    command.Parameters.AddWithValue("@DepartmentId", (object)user.DepartmentId ?? DBNull.Value);

                    if (user.Password != null)
                        command.Parameters.AddWithValue("@Password", user.Password);

                    command.ExecuteNonQuery();
                }
            }
        }
        public static DataTable GetUsersByDepartment(int? departmentId)
        {
            using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
            connection.Open();

            string sql;
            if (departmentId.HasValue)
                sql = @"SELECT U.UserId, U.Username, U.FullName, U.UserType, U.DepartmentId, D.DepartmentName
                FROM Users U
                LEFT JOIN Departments D ON U.DepartmentId = D.DepartmentId
                WHERE U.DepartmentId = @DepartmentId
                ORDER BY U.UserId";
            else
                sql = @"SELECT U.UserId, U.Username, U.FullName, U.UserType, U.DepartmentId, D.DepartmentName
                FROM Users U
                LEFT JOIN Departments D ON U.DepartmentId = D.DepartmentId
                ORDER BY U.UserId";

            using var command = new SqlCommand(sql, connection);
            if (departmentId.HasValue)
                command.Parameters.AddWithValue("@DepartmentId", departmentId.Value);

            using var adapter = new SqlDataAdapter(command);
            var table = new DataTable();
            adapter.Fill(table);
            return table;
        }

        // دالة للحصول على القسم الذي يديره المستخدم
        public static int? GetManagedDepartmentId(int userId)
        {
            using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
            connection.Open();

            string sql = "SELECT DepartmentId FROM Departments WHERE ManagerUserId = @UserId AND IsActive = 1";
            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@UserId", userId);

            System.Diagnostics.Debug.WriteLine($"البحث عن القسم المُدار للمستخدم: {userId}");
            System.Diagnostics.Debug.WriteLine($"SQL: {sql}");

            var result = command.ExecuteScalar();
            System.Diagnostics.Debug.WriteLine($"نتيجة البحث: {result}");

            return result != null ? (int?)Convert.ToInt32(result) : null;
        }

        // دالة لتعيين مدير لقسم
        public static void AssignDepartmentManager(int departmentId, int managerId)
        {
            using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
            connection.Open();

            string sql = "UPDATE Departments SET ManagerUserId = @ManagerId WHERE DepartmentId = @DepartmentId";
            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@ManagerId", managerId);
            command.Parameters.AddWithValue("@DepartmentId", departmentId);

            command.ExecuteNonQuery();
        }

        // دالة للحصول على موظفي قسم معين
        public static DataTable GetEmployeesByDepartment(int departmentId)
        {
            using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
            connection.Open();

            string sql = @"
                SELECT * FROM Employees
                WHERE EmployeeCode IN (
                    SELECT UserId FROM Users WHERE DepartmentId = @DepartmentId
                )
                ORDER BY EmployeeCode";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DepartmentId", departmentId);

            using var adapter = new SqlDataAdapter(command);
            var table = new DataTable();
            adapter.Fill(table);
            return table;
        }

        // دالة للبحث في موظفي قسم معين
        public static DataTable SearchEmployeesInDepartment(string searchTerm, int departmentId)
        {
            using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
            connection.Open();

            string sql = @"
                SELECT * FROM Employees
                WHERE EmployeeCode IN (
                    SELECT UserId FROM Users WHERE DepartmentId = @DepartmentId
                )
                AND (
                    Name LIKE @SearchTerm OR
                    MotherName LIKE @SearchTerm OR
                    IdentityNumber LIKE @SearchTerm OR
                    KeyCardNumber LIKE @SearchTerm OR
                    PhoneNumber LIKE @SearchTerm OR
                    CAST(EmployeeCode AS NVARCHAR) LIKE @SearchTerm
                )
                ORDER BY EmployeeCode";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DepartmentId", departmentId);
            command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");

            using var adapter = new SqlDataAdapter(command);
            var table = new DataTable();
            adapter.Fill(table);
            return table;
        }

        public static void DeleteUser(int userId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();

                string checkSql = "SELECT Username FROM Users WHERE UserId = @UserId";
                using (var checkCommand = new SqlCommand(checkSql, connection))
                {
                    checkCommand.Parameters.AddWithValue("@UserId", userId);
                    string? username = checkCommand.ExecuteScalar()?.ToString();
                    if (username == "admin")
                        throw new Exception("لا يمكن حذف حساب المدير الافتراضي");
                }

                string sql = "DELETE FROM Users WHERE UserId = @UserId";
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@UserId", userId);
                    command.ExecuteNonQuery();
                }
            }
        }

        //public static DataTable GetAllUsers()
        //{
        //    using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
        //    {
        //        connection.Open();
        //        string sql = @"SELECT 
        //            UserId,
        //            Username,
        //            FullName,
        //            UserType
        //        FROM Users
        //        ORDER BY UserId";

        //        using (var adapter = new SqlDataAdapter(sql, connection))
        //        {
        //            var table = new DataTable();
        //            adapter.Fill(table);
        //            return table;
        //        }
        //    }
        //}
        public static DataTable GetAllUsers()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"
            SELECT 
                u.UserId,
                u.Username,
                u.FullName,
                u.UserType,
                u.DepartmentId,
                d.DepartmentName
            FROM Users u
            LEFT JOIN Departments d ON u.DepartmentId = d.DepartmentId
            ORDER BY u.UserId";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static User GetUserById(int userId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = "SELECT * FROM Users WHERE UserId = @UserId";
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@UserId", userId);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            var user = new User
                            {
                                UserId = reader.GetInt32(reader.GetOrdinal("UserId")),
                                Username = reader.GetString(reader.GetOrdinal("Username")),
                                FullName = reader.GetString(reader.GetOrdinal("FullName")),
                                UserType = reader.GetString(reader.GetOrdinal("UserType"))
                            };

                            // التحقق من وجود حقل DepartmentId
                            if (!reader.IsDBNull(reader.GetOrdinal("DepartmentId")))
                            {
                                user.DepartmentId = reader.GetInt32(reader.GetOrdinal("DepartmentId"));
                            }

                            // التحقق من وجود حقل EmployeeCode
                            if (!reader.IsDBNull(reader.GetOrdinal("EmployeeCode")))
                            {
                                user.EmployeeCode = reader.GetString(reader.GetOrdinal("EmployeeCode"));
                            }

                            return user;
                        }
                        throw new Exception("المستخدم غير موجود");
                    }
                }
            }
        }

        // وظائف إدارة الأقسام

        public static DataTable GetAllDepartments()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT
                    DepartmentId,
                    DepartmentName,
                    DepartmentCode,
                    Description,
                    ManagerUserId,
                    CreatedDate,
                    CreatedBy,
                    IsActive
                FROM Departments
                ORDER BY DepartmentName";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static Department GetDepartmentById(int departmentId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = "SELECT * FROM Departments WHERE DepartmentId = @DepartmentId";
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@DepartmentId", departmentId);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Department
                            {
                                DepartmentId = reader.GetInt32(reader.GetOrdinal("DepartmentId")),
                                DepartmentName = reader.GetString(reader.GetOrdinal("DepartmentName")),
                                DepartmentCode = reader.GetString(reader.GetOrdinal("DepartmentCode")),
                                Description = reader.IsDBNull(reader.GetOrdinal("Description")) ? null : reader.GetString(reader.GetOrdinal("Description")),
                                ManagerUserId = reader.IsDBNull(reader.GetOrdinal("ManagerUserId")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("ManagerUserId")),
                                CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate")),
                                CreatedBy = reader.GetInt32(reader.GetOrdinal("CreatedBy")),
                                IsActive = reader.GetBoolean(reader.GetOrdinal("IsActive"))
                            };
                        }
                        throw new Exception("القسم غير موجود");
                    }
                }
            }
        }

        public static void AddDepartment(Department department)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"INSERT INTO Departments 
                              (DepartmentName, DepartmentCode, Description, ManagerUserId, CreatedDate, CreatedBy, IsActive)
                              VALUES 
                              (@DepartmentName, @DepartmentCode, @Description, @ManagerUserId, @CreatedDate, @CreatedBy, @IsActive);
                              SELECT CAST(SCOPE_IDENTITY() as int)";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@DepartmentName", department.DepartmentName);
                    command.Parameters.AddWithValue("@DepartmentCode", department.DepartmentCode);
                    command.Parameters.AddWithValue("@Description", (object)department.Description ?? DBNull.Value);
                    command.Parameters.AddWithValue("@ManagerUserId", (object)department.ManagerUserId ?? DBNull.Value);
                    command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                    command.Parameters.AddWithValue("@CreatedBy", department.CreatedBy);
                    command.Parameters.AddWithValue("@IsActive", department.IsActive);

                    // الحصول على الهوية المنشأة
                    int newId = (int)command.ExecuteScalar();
                    department.DepartmentId = newId;
                }
            }
        }

        public static void UpdateDepartment(Department department)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"UPDATE Departments 
                               SET DepartmentName = @DepartmentName,
                                   DepartmentCode = @DepartmentCode,
                                   Description = @Description,
                                   ManagerUserId = @ManagerUserId,
                                   IsActive = @IsActive
                               WHERE DepartmentId = @DepartmentId";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@DepartmentId", department.DepartmentId);
                    command.Parameters.AddWithValue("@DepartmentName", department.DepartmentName);
                    command.Parameters.AddWithValue("@DepartmentCode", department.DepartmentCode);
                    command.Parameters.AddWithValue("@Description", (object)department.Description ?? DBNull.Value);
                    command.Parameters.AddWithValue("@ManagerUserId", (object)department.ManagerUserId ?? DBNull.Value);
                    command.Parameters.AddWithValue("@IsActive", department.IsActive);

                    command.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteDepartment(int departmentId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();

                // التحقق مما إذا كان هناك مستخدمون مرتبطون بهذا القسم
                string checkSql = "SELECT COUNT(*) FROM Users WHERE DepartmentId = @DepartmentId";
                using (var checkCommand = new SqlCommand(checkSql, connection))
                {
                    checkCommand.Parameters.AddWithValue("@DepartmentId", departmentId);
                    int count = (int)checkCommand.ExecuteScalar();
                    if (count > 0)
                    {
                        throw new Exception("لا يمكن حذف القسم لأنه يحتوي على مستخدمين. قم بنقل المستخدمين إلى قسم آخر أولاً.");
                    }
                }

                // حذف القسم
                string sql = "DELETE FROM Departments WHERE DepartmentId = @DepartmentId";
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@DepartmentId", departmentId);
                    command.ExecuteNonQuery();
                }
            }
        }



        public static void UpdateSettings(string companyName, string? companyLogo, string theme, string language)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"UPDATE Settings SET 
                    CompanyName = @CompanyName,
                    CompanyLogo = @CompanyLogo,
                    Theme = @Theme,
                    Language = @Language,
                    LastModified = GETDATE()";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@CompanyName", companyName);
                    command.Parameters.AddWithValue("@CompanyLogo", (object?)companyLogo ?? DBNull.Value);
                    command.Parameters.AddWithValue("@Theme", theme);
                    command.Parameters.AddWithValue("@Language", language);
                    command.ExecuteNonQuery();
                }
            }
        }
        public static List<Vacation> GetVacationsForEmployee(string employeeName)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT 
            VacationId,
            EmployeeName,
            VacationType,
            StartDate,
            EndDate,
            DaysCount,
            Reason,
            Status,
            RejectionReason,
            ApprovalDate,
            ApprovedBy
        FROM Vacations
        WHERE EmployeeName = @EmployeeName
        ORDER BY StartDate DESC";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeName", employeeName);
                    var vacations = new List<Vacation>();

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            vacations.Add(new Vacation
                            {
                                VacationId = reader.GetInt32(reader.GetOrdinal("VacationId")),
                                EmployeeName = reader.GetString(reader.GetOrdinal("EmployeeName")),
                                VacationType = reader.GetString(reader.GetOrdinal("VacationType")),
                                StartDate = reader.GetDateTime(reader.GetOrdinal("StartDate")),
                                EndDate = reader.GetDateTime(reader.GetOrdinal("EndDate")),
                                DaysCount = reader.GetInt32(reader.GetOrdinal("DaysCount")),
                                Reason = reader.IsDBNull(reader.GetOrdinal("Reason")) ? null : reader.GetString(reader.GetOrdinal("Reason")),
                                Status = reader.IsDBNull(reader.GetOrdinal("Status")) ? "في الانتظار" : reader.GetString(reader.GetOrdinal("Status")),
                                RejectionReason = reader.IsDBNull(reader.GetOrdinal("RejectionReason")) ? null : reader.GetString(reader.GetOrdinal("RejectionReason")),
                                ApprovalDate = reader.IsDBNull(reader.GetOrdinal("ApprovalDate")) ? null : reader.GetDateTime(reader.GetOrdinal("ApprovalDate")),
                                ApprovedBy = reader.IsDBNull(reader.GetOrdinal("ApprovedBy")) ? null : reader.GetString(reader.GetOrdinal("ApprovedBy"))
                            });
                        }
                    }
                    return vacations;
                }
            }
        }

        // دالة اعتماد الإجازة
        public static void ApproveVacation(int vacationId, string approvedBy)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = @"UPDATE Vacations SET 
                        Status = @Status,
                        Notes = @Notes,
                        ApprovalDate = @ApprovalDate,
                        ApprovedBy = @ApprovedBy,
                        RejectionReason = NULL
                    WHERE VacationId = @VacationId";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@VacationId", vacationId);
                        command.Parameters.AddWithValue("@Status", "معتمد");
                        command.Parameters.AddWithValue("@Notes", "معتمدة - " + approvedBy);
                        command.Parameters.AddWithValue("@ApprovalDate", DateTime.Now);
                        command.Parameters.AddWithValue("@ApprovedBy", approvedBy);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"خطأ في اعتماد الإجازة: {ex.Message}");
                throw;
            }
        }

        // دالة رفض الإجازة
        public static void RejectVacation(int vacationId, string rejectionReason, string rejectedBy)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();

                    // إنشاء ملاحظات إدارية مثل موقع الويب
                    string adminNotes = $"مرفوضة - {rejectedBy}";
                    if (!string.IsNullOrEmpty(rejectionReason))
                        adminNotes += $" - {rejectionReason}";

                    string sql = @"UPDATE Vacations SET 
                        Status = @Status,
                        RejectionReason = @RejectionReason,
                        Notes = @Notes,
                        ApprovalDate = @ApprovalDate,
                        ApprovedBy = @ApprovedBy
                    WHERE VacationId = @VacationId";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@VacationId", vacationId);
                        command.Parameters.AddWithValue("@Status", "مرفوض");
                        command.Parameters.AddWithValue("@RejectionReason", rejectionReason ?? "");
                        command.Parameters.AddWithValue("@Notes", adminNotes);
                        command.Parameters.AddWithValue("@ApprovalDate", DateTime.Now);
                        command.Parameters.AddWithValue("@ApprovedBy", rejectedBy ?? "");
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"خطأ في رفض الإجازة: {ex.Message}");
                throw;
            }
        }

        public static string GenerateEmployeeReport(int employeeCode)
        {
            var employee = GetEmployeeById(employeeCode);
            if (employee == null)
            {
                throw new Exception("لم يتم العثور على الموظف");
            }

            TimeSpan serviceYears = DateTime.Now - employee.StartDate;
            string serviceText;
            int years = (int)(serviceYears.TotalDays / 365.25);
            int months = (int)((serviceYears.TotalDays % 365.25) / 30.44);

            if (years > 0 && months > 0)
            {
                serviceText = $"{years} سنة و {months} شهر";
            }
            else if (years > 0)
            {
                serviceText = $"{years} سنة";
            }
            else if (months > 0)
            {
                serviceText = $"{months} شهر";
            }
            else
            {
                serviceText = "أقل من شهر";
            }

            System.Text.StringBuilder html = new System.Text.StringBuilder();
            html.Append(@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>تقرير الموظف</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
        
        body { 
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px; 
            direction: rtl;
            background-color: #f5f5f5;
        }        .report-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            margin: 0 auto;
            max-width: 800px;
            position: relative;
            left: 50%;
            transform: translateX(-50%);
        }
        .employee-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #45678a 0%, #2c3e50 100%);
            color: white;
            border-radius: 10px;
            position: relative;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        .employee-header h1 {
            font-size: 32px;
            margin: 0;
            font-weight: 700;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }
        .employee-header .employee-name {
            font-size: 24px;
            margin-top: 10px;
            color: #ecf0f1;
        }        .content-wrapper {
            display: flex;
            flex-direction: row;
            gap: 15px;
            margin: 15px auto;
            max-width: 800px;
            align-items: flex-start;
        }
        .details-section {
            flex: 2;
        }
        .photo-section {
            flex: 1;
            max-width: 300px;
            position: sticky;
            top: 20px;
        }.info-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .info-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            margin-bottom: 5px;
        }
        .info-item:hover {
            transform: translateX(-5px);
            box-shadow: 2px 2px 10px rgba(0,0,0,0.1);
        }
        .info-label {
            font-weight: 600;
            color: #45678a;
            margin-left: 10px;
            white-space: nowrap;
        }
        .info-value {
            flex: 1;
            padding: 4px 8px;
            color: #2c3e50;
        }
        .photo-container {
            width: 100%;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .employee-photo {
            width: 100%;
            height: auto;
            display: block;
        }        .report-footer {
            margin-top: 20px;
            text-align: center;
            color: #666;
            font-size: 14px;
            padding-top: 15px;
            border-top: 1px solid #e1e8ed;
            page-break-before: avoid;
            page-break-inside: avoid;
        }        @media print {
            body {
                margin: 0;
                background: white;
                display: flex;
                justify-content: center;
            }
            .report-container {
                box-shadow: none;
                margin: 0 auto;
                padding: 15mm;
                width: 210mm;
                min-height: 297mm;
                transform: none;
                left: auto;
            }
            .content-wrapper {
                gap: 10px;
            }
            .photo-section {
                max-width: 200px;
            }
            .info-item {
                padding: 8px;
                margin-bottom: 4px;
            }
            .employee-header {
                margin-bottom: 20px;
                padding: 15px;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .info-group {
                gap: 5px;
            }            @page {
                margin: 0;
                size: A4 portrait;
            }
        }
    </style>
</head>
<body>
    <div class=""report-container"">
        <div class=""employee-header"">
            <h1>تقرير بيانات الموظف</h1>
            <div class=""employee-name"">" + employee.Name + @"</div>
        </div>
        
        <div class=""content-wrapper"">
            <div class=""details-section"">
                <div class=""info-group"">");

            html.AppendFormat(@"
                    <div class=""info-item"">
                        <span class=""info-label"">كود الموظف:</span>
                        <span class=""info-value"">{0}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">الصنف:</span>
                        <span class=""info-value"">{11}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">اسم الأم:</span>
                        <span class=""info-value"">{1}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">تاريخ الميلاد:</span>
                        <span class=""info-value"">{2}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">المحافظة:</span>
                        <span class=""info-value"">{3}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">الجنسية:</span>
                        <span class=""info-value"">{4}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">رقم الهوية:</span>
                        <span class=""info-value"">{5}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">تاريخ الامر الاداري:</span>
                        <span class=""info-value"">{6}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">مدة الخدمة:</span>
                        <span class=""info-value"">{12}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">رقم الكي كارد:</span>
                        <span class=""info-value"">{7}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">رقم الباج:</span>
                        <span class=""info-value"">{8}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">تاريخ انتهاء الباج:</span>
                        <span class=""info-value"">{9}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">رقم الهاتف:</span>
                        <span class=""info-value"">{10}</span>
                    </div>
                </div>
            </div>
            <div class=""photo-section"">
                <div class=""photo-container"">",
                        employee.EmployeeCode,
                        employee.MotherName,
                        employee.DateOfBirth.ToString("yyyy-MM-dd"),
                        employee.Province,
                        employee.Nationality,
                        employee.IdentityNumber,
                        employee.StartDate.ToString("yyyy-MM-dd"),
                        employee.KeyCardNumber,
                        employee.BadgeNumber,
                        employee.BadgeExpiryDate.ToString("yyyy-MM-dd"),
                        employee.PhoneNumber,
                        employee.Category,
                        serviceText);

            if (!string.IsNullOrEmpty(employee.PhotoPath) && File.Exists(employee.PhotoPath))
            {
                string base64Image = Convert.ToBase64String(File.ReadAllBytes(employee.PhotoPath));
                string imageFormat = Path.GetExtension(employee.PhotoPath).TrimStart('.').ToLower();
                html.AppendFormat(@"
                    <img src=""data:image/{0};base64,{1}"" alt=""صورة الموظف"" class=""employee-photo"">",
                            imageFormat, base64Image);
            }
            else
            {
                html.Append(@"
                    <div style=""padding: 20px; text-align: center; color: #666;"">
                        لا تتوفر صورة
                    </div>");
            }

            html.AppendFormat(@"
                </div>
            </div>
        </div>

        <div class=""report-footer"">
            تم إصدار هذا التقرير بتاريخ {0} الساعة {1}
        </div>
    </div>
</body>
</html>", DateTime.Now.ToString("dd/MM/yyyy"), DateTime.Now.ToString("HH:mm"));

            string htmlFilePath = Path.Combine("html", $"employee_{employeeCode}.html");
            Directory.CreateDirectory("html");
            File.WriteAllText(htmlFilePath, html.ToString(), System.Text.Encoding.UTF8);

            return htmlFilePath;
        }

        public static void AddCourse(Course course)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"INSERT INTO Courses 
                    (EmployeeName, CourseType, CourseNumber, Category, StartDate, EndDate, DaysCount, GraduationGrade) 
                    VALUES 
                    (@EmployeeName, @CourseType, @CourseNumber, @Category, @StartDate, @EndDate, @DaysCount, @GraduationGrade)";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeName", course.EmployeeName);
                    command.Parameters.AddWithValue("@CourseType", course.CourseType);
                    command.Parameters.AddWithValue("@CourseNumber", course.CourseNumber);
                    command.Parameters.AddWithValue("@Category", course.Category);
                    command.Parameters.AddWithValue("@StartDate", course.StartDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@EndDate", course.EndDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@DaysCount", course.DaysCount);
                    command.Parameters.AddWithValue("@GraduationGrade", course.GraduationGrade);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static void UpdateCourse(Course course)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"UPDATE Courses 
                    SET EmployeeName = @EmployeeName, 
                        CourseType = @CourseType, 
                        CourseNumber = @CourseNumber, 
                        Category = @Category,
                        StartDate = @StartDate, 
                        EndDate = @EndDate, 
                        DaysCount = @DaysCount,
                        GraduationGrade = @GraduationGrade
                    WHERE CourseId = @CourseId";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@CourseId", course.CourseId);
                    command.Parameters.AddWithValue("@EmployeeName", course.EmployeeName);
                    command.Parameters.AddWithValue("@CourseType", course.CourseType);
                    command.Parameters.AddWithValue("@CourseNumber", course.CourseNumber);
                    command.Parameters.AddWithValue("@Category", course.Category);
                    command.Parameters.AddWithValue("@StartDate", course.StartDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@EndDate", course.EndDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@DaysCount", course.DaysCount);
                    command.Parameters.AddWithValue("@GraduationGrade", course.GraduationGrade);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteCourse(int courseId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = "DELETE FROM Courses WHERE CourseId = @CourseId";
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@CourseId", courseId);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static DataTable GetAllCourses()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT 
                    CourseId as 'المعرف',
                    EmployeeName as 'اسم الموظف',
                    CourseNumber as 'رقم الدورة',
                    CourseType as 'نوع الدورة',
                    Category as 'الصنف',
                    StartDate as 'تاريخ البداية',
                    EndDate as 'تاريخ النهاية',
                    DaysCount as 'عدد الأيام',
                    GraduationGrade as 'درجة التخرج'
                FROM Courses
                ORDER BY CourseId DESC";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static DataTable SearchCourses(string searchText)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT 
                    CourseId as 'المعرف',
                    EmployeeName as 'اسم الموظف',
                    CourseType as 'نوع الدورة',
                    CourseNumber as 'رقم الدورة',
                    Category as 'الصنف',
                    StartDate as 'تاريخ البداية',
                    EndDate as 'تاريخ النهاية',
                    DaysCount as 'عدد الأيام'
                FROM Courses 
                WHERE EmployeeName LIKE @SearchText 
                OR CourseType LIKE @SearchText 
                OR CourseNumber LIKE @SearchText
                OR Category LIKE @SearchText
                ORDER BY CourseId DESC";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    adapter.SelectCommand.Parameters.AddWithValue("@SearchText", "%" + searchText + "%");
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static List<Course> LoadCourses()
        {
            List<Course> courses = new List<Course>();
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = "SELECT * FROM Courses";
                using (var command = new SqlCommand(sql, connection))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Course course = new Course
                            {
                                CourseId = Convert.ToInt32(reader["CourseId"]),
                                EmployeeName = reader["EmployeeName"]?.ToString() ?? string.Empty,
                                CourseType = reader["CourseType"]?.ToString() ?? string.Empty,
                                CourseNumber = reader["CourseNumber"]?.ToString() ?? string.Empty,
                                Category = reader["Category"]?.ToString() ?? string.Empty,
                                StartDate = DateTime.TryParse(reader["StartDate"]?.ToString(), out DateTime startDate) ? startDate : DateTime.MinValue,
                                EndDate = DateTime.TryParse(reader["EndDate"]?.ToString(), out DateTime endDate) ? endDate : DateTime.MinValue,
                                DaysCount = Convert.ToInt32(reader["DaysCount"]),
                                GraduationGrade = reader["GraduationGrade"]?.ToString() ?? string.Empty
                            };
                            courses.Add(course);
                        }
                    }
                }
            }
            return courses;
        }

        public static string GenerateCourseReport(string employeeName)
        {
            var courses = LoadCourses().Where(c => c.EmployeeName == employeeName).ToList();

            System.Text.StringBuilder html = new System.Text.StringBuilder();
            html.Append(@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>تقرير دورات الموظف</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
        
        body { 
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px; 
            direction: rtl;
            background-color: #f5f5f5;
        }
        .report-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            margin: 0 auto;
            max-width: 1200px;
        }
        .report-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #45678a 0%, #2c3e50 100%);
            color: white;
            border-radius: 10px;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        .report-header h1 {
            font-size: 28px;
            margin: 0;
            padding: 0;
        }
        .report-header .employee-name {
            font-size: 24px;
            margin-top: 10px;
            color: #ecf0f1;
        }
        .courses-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .course-card {
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: transform 0.2s;
        }
        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .course-header {
            border-bottom: 2px solid #45678a;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .course-title {
            font-size: 18px;
            color: #2c3e50;
            margin: 0;
            font-weight: 600;
        }
        .course-number {
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 5px;
        }
        .course-info {
            display: grid;
            gap: 10px;
        }
        .info-item {
            display: flex;
            align-items: center;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 6px;
        }
        .info-label {
            font-weight: 600;
            color: #45678a;
            min-width: 120px;
        }
        .info-value {
            color: #2c3e50;
        }
        .grade-badge {
            display: inline-block;
            padding: 6px 12px;
            background: #45678a;
            color: white;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }        .report-footer {
            margin-top: 20px;
            text-align: center;
            color: #666;
            font-size: 14px;
            padding-top: 15px;
            border-top: 1px solid #e1e8ed;
            page-break-before: avoid;
            page-break-inside: avoid;
        }
        .summary-section {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e1e8ed;
        }
        .summary-title {
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 600;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .summary-item {
            background: white;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e1e8ed;
        }
        .summary-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        .summary-value {
            font-size: 20px;
            color: #45678a;
            font-weight: 600;
        }
        @media print {
            body {
                background: white;
            }
            .report-container {
                box-shadow: none;
            }
            .course-card {
                break-inside: avoid;
            }
            .report-header {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .grade-badge {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class=""report-container"">
        <div class=""report-header"">
            <h1>تقرير الدورات التدريبية</h1>
            <div class=""employee-name"">" + employeeName + @"</div>
        </div>

        <div class=""summary-section"">
            <h3 class=""summary-title"">ملخص الدورات</h3>
            <div class=""summary-grid"">
                <div class=""summary-item"">
                    <div class=""summary-label"">عدد الدورات</div>
                    <div class=""summary-value"">" + courses.Count + @"</div>
                </div>
                <div class=""summary-item"">
                    <div class=""summary-label"">مجموع الأيام</div>
                    <div class=""summary-value"">" + courses.Sum(c => c.DaysCount) + @"</div>
                </div>
                <div class=""summary-item"">
                    <div class=""summary-label"">معدل درجات التخرج</div>
                    <div class=""summary-value"">" + (courses.Any() ? courses.Average(c =>
                        double.TryParse(c.GraduationGrade, out double grade) ? grade : 0
                    ).ToString("F2") : "0") + @"</div>
                </div>
            </div>
        </div>
        
        <div class=""courses-container"">");

            foreach (var course in courses)
            {
                html.AppendFormat(@"
            <div class=""course-card"">
                <div class=""course-header"">
                    <h3 class=""course-title"">{0}</h3>
                    <div class=""course-number"">رقم الدورة: {1}</div>
                </div>
                <div class=""course-info"">
                    <div class=""info-item"">
                        <span class=""info-label"">الصنف:</span>
                        <span class=""info-value"">{2}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">تاريخ البداية:</span>
                        <span class=""info-value"">{3:dd/MM/yyyy}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">تاريخ النهاية:</span>
                        <span class=""info-value"">{4:dd/MM/yyyy}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">عدد الأيام:</span>
                        <span class=""info-value"">{5}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">درجة التخرج:</span>
                        <span class=""info-value""><span class=""grade-badge"">{6}</span></span>
                    </div>
                </div>
            </div>",
                course.CourseType,
                course.CourseNumber,
                course.Category,
                course.StartDate,
                course.EndDate,
                course.DaysCount,
                course.GraduationGrade);
            }

            html.AppendFormat(@"
        </div>
        <div class=""report-footer"">
            تم إصدار هذا التقرير بتاريخ {0} الساعة {1}
        </div>
    </div>
</body>
</html>", DateTime.Now.ToString("dd/MM/yyyy"), DateTime.Now.ToString("HH:mm"));

            string htmlFilePath = Path.Combine("html", $"courses_{employeeName}_{DateTime.Now:yyyyMMdd_HHmmss}.html");
            Directory.CreateDirectory("html");
            File.WriteAllText(htmlFilePath, html.ToString());

            return htmlFilePath;
        }

        public static string GenerateAllCoursesHtml()
        {
            var courses = GetAllCourses();
            System.Text.StringBuilder html = new System.Text.StringBuilder();
            html.Append(@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>تقرير الدورات</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
        
        body { 
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px; 
            direction: rtl;
            background-color: #f5f5f5;
        }
        .report-container {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            margin: 0 auto;
            overflow-x: auto;
        }
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #45678a 0%, #2c3e50 100%);
            color: white;
            border-radius: 10px;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        .report-header h1 {
            font-size: 28px;
            margin: 0;
            padding: 0;
        }
        .search-container {
            margin: 20px 0;
            text-align: center;
        }
        .search-box {
            width: 50%;
            max-width: 400px;
            padding: 10px 15px;
            border: 2px solid #45678a;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
        }
        table { 
            width: 100%;
            border-collapse: collapse; 
            margin: 20px 0;
            background: white;
        }
        th, td { 
            border: 1px solid #ddd; 
            padding: 8px 12px;
            text-align: center;
            font-size: 14px;
        }
        th { 
            background: linear-gradient(135deg, #45678a 0%, #2c3e50 100%);
            color: white;
            font-weight: 600;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        tr:nth-child(even) { 
            background-color: #f8f9fa; 
        
        }
        tr:hover {
            background-color: #f1f4f7;
        }
        .date-printed {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
        @media print {
            @page {
                size: landscape;
            }
            body {
                margin: 0;
                background: white;
            }
            .search-container {
                display: none;
            }
        }
    </style>
    <script>
        function searchCourses() {
            var input = document.getElementById('searchInput');
            var filter = input.value.toLowerCase();
            var table = document.getElementById('coursesTable');
            var rows = table.getElementsByTagName('tr');

            for (var i = 1; i < rows.length; i++) {
                var display = false;
                var cells = rows[i].getElementsByTagName('td');
                for (var j = 0; j < cells.length; j++) {
                    var cell = cells[j];
                    if (cell) {
                        var text = cell.textContent || cell.innerText;
                        if (text.toLowerCase().indexOf(filter) > -1) {
                            display = true;
                            break;
                        }
                    }
                }
                rows[i].style.display = display ? '' : 'none';
            }
        }
    </script>
</head>
<body>
    <div class=""report-container"">
        <div class=""report-header"">
            <h1>تقرير الدورات التدريبية</h1>
        </div>
        
        <div class=""search-container"">
            <input type=""text"" id=""searchInput"" class=""search-box"" 
                   placeholder=""اكتب للبحث في جميع الحقول..."" 
                   onkeyup=""searchCourses()"">
        </div>

        <table id=""coursesTable"">
            <thead>
                <tr>
                    <th>المعرف</th>
                    <th>الصنف</th>
                    <th>اسم الموظف</th>
                    <th>رقم الدورة</th>
                    <th>نوع الدورة</th>
                    <th>تاريخ البداية</th>
                    <th>تاريخ النهاية</th>
                    <th>عدد الأيام</th>
                    <th>درجة التخرج</th>
                </tr>
            </thead>
            <tbody>");

            foreach (DataRow row in courses.Rows)
            {
                html.AppendFormat(@"
                <tr>
                    <td>{0}</td>
                    <td>{1}</td>
                    <td>{2}</td>
                    <td>{3}</td>
                    <td>{4}</td>
                    <td>{5}</td>
                    <td>{6}</td>
                    <td>{7}</td>
                    <td>{8}</td>
                </tr>",
                    row["المعرف"],
                    row["الصنف"],
                    row["اسم الموظف"],
                    row["رقم الدورة"],
                    row["نوع الدورة"],
                    Convert.ToDateTime(row["تاريخ البداية"]).ToString("dd/MM/yyyy"),
                    Convert.ToDateTime(row["تاريخ النهاية"]).ToString("dd/MM/yyyy"),
                    row["عدد الأيام"],
                    row["درجة التخرج"]
                );
            }

            html.AppendFormat(@"
            </tbody>
        </table>
        <div class=""date-printed"">
            تم إصدار هذا التقرير بتاريخ {0} الساعة {1}
        </div>
    </div>
</body>
</html>", DateTime.Now.ToString("dd/MM/yyyy"), DateTime.Now.ToString("HH:mm"));

            string htmlFilePath = Path.Combine("html", "courses.html");
            Directory.CreateDirectory("html");
            File.WriteAllText(htmlFilePath, html.ToString());

            return htmlFilePath;
        }

        public static int GetEmployeeIdByName(string name)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = "SELECT EmployeeCode FROM Employees WHERE Name = @Name";
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Name", name);
                    var result = command.ExecuteScalar();
                    if (result != null && result != DBNull.Value)
                    {
                        return Convert.ToInt32(result);
                    }
                    throw new Exception("لم يتم العثور على الموظف");
                }
            }
        }

        public static void LogMessage(string message)
        {
            try
            {
                string logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "app.log");
                string logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}{Environment.NewLine}";
                File.AppendAllText(logPath, logEntry);
                System.Diagnostics.Debug.WriteLine(logEntry);
            }
            catch
            {
                // تجاهل أي أخطاء في التسجيل
            }
        }

        public static DataTable GetVacationsEndingTomorrow()
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    var tomorrow = DateTime.Now.Date.AddDays(1);
                    LogMessage($"البحث عن الإجازات التي تنتهي في: {tomorrow:yyyy-MM-dd}");

                    // استعلام التشخيص المعدل
                    string debugQuery = @"
                SELECT v.*, e.Name as EmployeeName 
                FROM Vacations v 
                JOIN Employees e ON v.EmployeeId = e.EmployeeCode";
                    using (var debugCommand = new SqlCommand(debugQuery, connection))
                    {
                        using (var reader = debugCommand.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var id = reader["EmployeeId"];
                                var name = reader["EmployeeName"];
                                var endDate = Convert.ToDateTime(reader["EndDate"]).ToString("yyyy-MM-dd");
                                LogMessage($"إجازة موجودة: الموظف {name} (ID: {id}) - تاريخ الانتهاء: {endDate}");
                            }
                        }
                    }

                    // الاستعلام الرئيسي المعدل
                    string query = @"
                SELECT v.*, e.Name as EmployeeName
                FROM Vacations v
                JOIN Employees e ON v.EmployeeId = e.EmployeeCode
                WHERE CAST(v.EndDate AS date) = CAST(@tomorrow AS date)";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@tomorrow", tomorrow);

                        LogMessage($"تنفيذ الاستعلام: {query}");
                        LogMessage($"مع المعامل @tomorrow = {tomorrow:yyyy-MM-dd}");

                        var dt = new DataTable();
                        using (var adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(dt);
                        }
                        LogMessage($"تم العثور على {dt.Rows.Count} إجازة تنتهي غداً");

                        // طباعة النتائج للتشخيص
                        foreach (DataRow row in dt.Rows)
                        {
                            var empId = row["EmployeeId"];
                            var empName = row["EmployeeName"];
                            var endDate = Convert.ToDateTime(row["EndDate"]).ToString("yyyy-MM-dd");
                            LogMessage($"نتيجة الاستعلام: الموظف {empName} (ID: {empId}) - تاريخ الانتهاء: {endDate}");
                        }

                        return dt;
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"خطأ في الحصول على الإجازات: {ex.Message}");
                if (ex.InnerException != null)
                {
                    LogMessage($"تفاصيل الخطأ الداخلي: {ex.InnerException.Message}");
                }
                LogMessage($"Stack Trace: {ex.StackTrace}");
                throw;
            }
        }

        public static DataTable GetCoursesEndingTomorrow()
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    var tomorrow = DateTime.Now.Date.AddDays(1);
                    LogMessage($"البحث عن الدورات التي تنتهي في: {tomorrow:yyyy-MM-dd}");

                    // الاستعلام المعدل
                    string query = @"
                SELECT c.*, e.EmployeeCode, e.Name as EmployeeName
                FROM Courses c
                JOIN Employees e ON e.Name = c.EmployeeName
                WHERE CAST(c.EndDate AS date) = CAST(@tomorrow AS date)";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@tomorrow", tomorrow);

                        LogMessage($"تنفيذ الاستعلام: {query}");
                        LogMessage($"مع المعامل @tomorrow = {tomorrow:yyyy-MM-dd}");

                        var dt = new DataTable();
                        using (var adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(dt);
                        }
                        LogMessage($"تم العثور على {dt.Rows.Count} دورة تنتهي غداً");

                        // طباعة النتائج للتشخيص
                        foreach (DataRow row in dt.Rows)
                        {
                            var empCode = row["EmployeeCode"];
                            var empName = row["EmployeeName"];
                            var endDate = Convert.ToDateTime(row["EndDate"]).ToString("yyyy-MM-dd");
                            LogMessage($"نتيجة الاستعلام: الموظف {empName} (كود: {empCode}) - تاريخ الانتهاء: {endDate}");
                        }

                        return dt;
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"خطأ في الحصول على الدورات: {ex.Message}");
                if (ex.InnerException != null)
                {
                    LogMessage($"تفاصيل الخطأ الداخلي: {ex.InnerException.Message}");
                }
                LogMessage($"Stack Trace: {ex.StackTrace}");
                throw;
            }
        }

        public static bool HasUsers()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                try
                {
                    connection.Open();

                    // التحقق من وجود جدول Users أولاً
                    string checkTableSql = @"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                                            WHERE TABLE_NAME = 'Users'";
                    using (var checkCommand = new SqlCommand(checkTableSql, connection))
                    {
                        int tableExists = Convert.ToInt32(checkCommand.ExecuteScalar());
                        if (tableExists == 0)
                        {
                            // إنشاء جدول Users إذا لم يكن موجوداً
                            CreateUsersTable(connection);
                            return false; // لا يوجد مستخدمين لأن الجدول تم إنشاؤه للتو
                        }
                    }

                    // التحقق من وجود جدول ActivityLogs وإنشاؤه إذا لم يكن موجوداً
                    string checkActivityTableSql = @"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                                                    WHERE TABLE_NAME = 'ActivityLogs'";
                    using (var checkCommand = new SqlCommand(checkActivityTableSql, connection))
                    {
                        int tableExists = Convert.ToInt32(checkCommand.ExecuteScalar());
                        if (tableExists == 0)
                        {
                            // تم حذف كود إنشاء الجدول - الجدول موجود بالفعل
                        }
                    }

                    string sql = "SELECT COUNT(*) FROM Users";
                    using (var command = new SqlCommand(sql, connection))
                    {
                        return Convert.ToInt32(command.ExecuteScalar()) > 0;
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"خطأ في HasUsers: {ex.Message}", ex);
                }
            }
        }

        private static void CreateUsersTable(SqlConnection connection)
        {
            string createTableSql = @"
                CREATE TABLE Users (
                    UserId INT IDENTITY(1,1) PRIMARY KEY,
                    Username NVARCHAR(100) NOT NULL UNIQUE,
                    FullName NVARCHAR(200) NULL,
                    Password NVARCHAR(100) NOT NULL,
                    UserType NVARCHAR(50) NOT NULL,
                    CreatedDate DATETIME2 DEFAULT GETDATE(),
                    IsActive BIT DEFAULT 1
                )";

            using (var command = new SqlCommand(createTableSql, connection))
            {
                command.ExecuteNonQuery();
            }
        }




        public static int AddAttendance(Attendance attendance)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();

                string sql = @"INSERT INTO Attendance (
            EmployeeCode, EmployeeName, Date, CheckInTime, CheckOutTime,
            WorkingHours, OvertimeHours, Status, Notes, WorkPeriodId, CreatedDate)
        VALUES (
            @EmployeeCode, @EmployeeName, @Date, @CheckInTime, @CheckOutTime,
            @WorkingHours, @OvertimeHours, @Status, @Notes, @WorkPeriodId, @CreatedDate);

        SELECT SCOPE_IDENTITY();";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", attendance.EmployeeCode);
                    command.Parameters.AddWithValue("@EmployeeName", attendance.EmployeeName);
                    command.Parameters.AddWithValue("@Date", attendance.Date);
                    command.Parameters.AddWithValue("@CheckInTime", (object?)attendance.CheckInTime ?? DBNull.Value);
                    command.Parameters.AddWithValue("@CheckOutTime", (object?)attendance.CheckOutTime ?? DBNull.Value);
                    command.Parameters.AddWithValue("@WorkingHours", attendance.WorkingHours);
                    command.Parameters.AddWithValue("@OvertimeHours", attendance.OvertimeHours);
                    command.Parameters.AddWithValue("@Status", attendance.Status);
                    command.Parameters.AddWithValue("@Notes", (object?)attendance.Notes ?? DBNull.Value);
                    command.Parameters.AddWithValue("@WorkPeriodId", (object?)attendance.WorkPeriodId ?? DBNull.Value);
                    command.Parameters.AddWithValue("@CreatedDate", attendance.CreatedDate);

                    return Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        public static void UpdateAttendance(Attendance attendance)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"UPDATE Attendance SET
                    EmployeeCode = @EmployeeCode,
                    EmployeeName = @EmployeeName,
                    Date = @Date,
                    CheckInTime = @CheckInTime,
                    CheckOutTime = @CheckOutTime,
                    WorkingHours = @WorkingHours,
                    OvertimeHours = @OvertimeHours,
                    Status = @Status,
                    Notes = @Notes,
                    WorkPeriodId = @WorkPeriodId
                WHERE AttendanceId = @AttendanceId";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@AttendanceId", attendance.AttendanceId);
                    command.Parameters.AddWithValue("@EmployeeCode", attendance.EmployeeCode);
                    command.Parameters.AddWithValue("@EmployeeName", attendance.EmployeeName);
                    command.Parameters.AddWithValue("@Date", attendance.Date.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@CheckInTime", attendance.CheckInTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@CheckOutTime", attendance.CheckOutTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@WorkingHours", attendance.WorkingHours);
                    command.Parameters.AddWithValue("@OvertimeHours", attendance.OvertimeHours);
                    command.Parameters.AddWithValue("@Status", attendance.Status);
                    command.Parameters.AddWithValue("@Notes", attendance.Notes ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@WorkPeriodId", attendance.WorkPeriodId ?? (object)DBNull.Value);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static bool DeleteAttendance(int attendanceId)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "DELETE FROM Attendance WHERE AttendanceId = @AttendanceId";
                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@AttendanceId", attendanceId);
                        int rowsAffected = command.ExecuteNonQuery();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف سجل الحضور: {ex.Message}");
                return false;
            }
        }

        public static DataTable GetAllAttendance()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT
                    AttendanceId as 'المعرف',
                    EmployeeCode as 'كود الموظف',
                    EmployeeName as 'اسم الموظف',
                    Date as 'التاريخ',
                    CheckInTime as 'وقت الحضور',
                    CheckOutTime as 'وقت الانصراف',
                    WorkingHours as 'ساعات العمل',
                    OvertimeHours as 'الساعات الإضافية',
                    Status as 'الحالة',
                    Notes as 'ملاحظات'
                FROM Attendance
                ORDER BY Date DESC, CheckInTime DESC";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static DataTable GetAttendanceByEmployee(int employeeCode)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT
                    AttendanceId as 'المعرف',
                    Date as 'التاريخ',
                    CheckInTime as 'وقت الحضور',
                    CheckOutTime as 'وقت الانصراف',
                    WorkingHours as 'ساعات العمل',
                    OvertimeHours as 'الساعات الإضافية',
                    Status as 'الحالة',
                    Notes as 'ملاحظات'
                FROM Attendance
                WHERE EmployeeCode = @EmployeeCode
                ORDER BY Date DESC";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    var table = new DataTable();
                    using (var adapter = new SqlDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }

        public static DataTable GetAttendanceByDateRange(DateTime startDate, DateTime endDate)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT
                    AttendanceId as 'المعرف',
                    EmployeeCode as 'كود الموظف',
                    EmployeeName as 'اسم الموظف',
                    Date as 'التاريخ',
                    CheckInTime as 'وقت الحضور',
                    CheckOutTime as 'وقت الانصراف',
                    WorkingHours as 'ساعات العمل',
                    OvertimeHours as 'الساعات الإضافية',
                    Status as 'الحالة',
                    Notes as 'ملاحظات'
                FROM Attendance
                WHERE Date BETWEEN @StartDate AND @EndDate
                ORDER BY Date DESC, CheckInTime DESC";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));
                    var table = new DataTable();
                    using (var adapter = new SqlDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }

        public static Attendance? GetTodayAttendance(int employeeCode)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT * FROM Attendance
                       WHERE EmployeeCode = @EmployeeCode AND CAST(Date AS DATE) = CAST(GETDATE() AS DATE)";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);

                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Attendance
                            {
                                AttendanceId = reader.GetInt32(reader.GetOrdinal("AttendanceId")),
                                EmployeeCode = reader.GetInt32(reader.GetOrdinal("EmployeeCode")),
                                EmployeeName = reader.GetString(reader.GetOrdinal("EmployeeName")),
                                Date = reader.GetDateTime(reader.GetOrdinal("Date")),
                                CheckInTime = reader.IsDBNull(reader.GetOrdinal("CheckInTime")) ? null : reader.GetDateTime(reader.GetOrdinal("CheckInTime")),
                                CheckOutTime = reader.IsDBNull(reader.GetOrdinal("CheckOutTime")) ? null : reader.GetDateTime(reader.GetOrdinal("CheckOutTime")),
                                WorkingHours = reader.IsDBNull(reader.GetOrdinal("WorkingHours")) ? 0 : reader.GetDouble(reader.GetOrdinal("WorkingHours")),
                                OvertimeHours = reader.IsDBNull(reader.GetOrdinal("OvertimeHours")) ? 0 : reader.GetDouble(reader.GetOrdinal("OvertimeHours")),
                                Status = reader.GetString(reader.GetOrdinal("Status")),
                                Notes = reader.IsDBNull(reader.GetOrdinal("Notes")) ? null : reader.GetString(reader.GetOrdinal("Notes")),
                                WorkPeriodId = reader.IsDBNull(reader.GetOrdinal("WorkPeriodId")) ? null : reader.GetInt32(reader.GetOrdinal("WorkPeriodId")),
                                CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate"))
                            };
                        }
                    }
                }
            }
            return null;
        }

        public static bool CheckIn(int employeeCode, string employeeName)
        {
            var todayAttendance = GetTodayAttendance(employeeCode);

            if (todayAttendance != null && todayAttendance.CheckInTime.HasValue)
            {
                return false; // الموظف سجل حضوره بالفعل اليوم
            }

            if (todayAttendance == null)
            {
                // إنشاء سجل حضور جديد
                var attendance = new Attendance
                {
                    EmployeeCode = employeeCode,
                    EmployeeName = employeeName,
                    Date = DateTime.Today,
                    CheckInTime = DateTime.Now,
                    Status = "حاضر"
                };

                // فحص التأخير
                attendance.CheckLateStatus(new TimeSpan(8, 0, 0)); // وقت العمل الرسمي 8:00 صباحاً

                AddAttendance(attendance);
            }
            else
            {
                // تحديث وقت الحضور
                todayAttendance.CheckInTime = DateTime.Now;
                todayAttendance.Status = "حاضر";
                todayAttendance.CheckLateStatus(new TimeSpan(8, 0, 0));
                UpdateAttendance(todayAttendance);
            }

            return true;
        }

        public static bool CheckOut(int employeeCode)
        {
            var todayAttendance = GetTodayAttendance(employeeCode);

            if (todayAttendance == null || !todayAttendance.CheckInTime.HasValue)
            {
                return false; // لم يسجل الموظف حضوره بعد
            }

            if (todayAttendance.CheckOutTime.HasValue)
            {
                return false; // الموظف سجل انصرافه بالفعل
            }

            // تسجيل وقت الانصراف وحساب ساعات العمل
            todayAttendance.CheckOutTime = DateTime.Now;
            todayAttendance.CalculateWorkingHours();
            UpdateAttendance(todayAttendance);

            return true;
        }

        // دالة مساعدة لجلب الموظفين بأسماء الأعمدة الإنجليزية للاستخدام في نظام الحضور
        public static DataTable GetEmployeesForAttendance()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT
                    EmployeeCode,
                    Name,
                    Category,
                    Province
                FROM Employees
                ORDER BY Name";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }


        public static int AddWorkPeriod(WorkPeriod workPeriod)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = @"INSERT INTO WorkPeriods (
                EmployeeCode, EmployeeName, ProjectName, Description, StartDate, EndDate,
                WorkingDays, DailyWorkingHours, Status, CreatedDate)
            VALUES (
                @EmployeeCode, @EmployeeName, @ProjectName, @Description, @StartDate, @EndDate,
                @WorkingDays, @DailyWorkingHours, @Status, @CreatedDate);
            SELECT CAST(SCOPE_IDENTITY() AS INT);"; // تغيير هنا لـ SQL Server

                    using (var command = new SqlCommand(sql, connection))
                    {
                        // معالجة القيم NULL
                        command.Parameters.Add("@EmployeeCode", SqlDbType.Int).Value = workPeriod.EmployeeCode;
                        command.Parameters.Add("@EmployeeName", SqlDbType.NVarChar, 100).Value = workPeriod.EmployeeName ?? (object)DBNull.Value;
                        command.Parameters.Add("@ProjectName", SqlDbType.NVarChar, 100).Value = workPeriod.ProjectName ?? (object)DBNull.Value;
                        command.Parameters.Add("@Description", SqlDbType.NVarChar, -1).Value = workPeriod.Description ?? (object)DBNull.Value;
                        command.Parameters.Add("@StartDate", SqlDbType.Date).Value = workPeriod.StartDate;
                        command.Parameters.Add("@EndDate", SqlDbType.Date).Value = workPeriod.EndDate;
                        command.Parameters.Add("@WorkingDays", SqlDbType.NVarChar, 50).Value = workPeriod.WorkingDays ?? (object)DBNull.Value;
                        command.Parameters.Add("@DailyWorkingHours", SqlDbType.Float).Value = workPeriod.DailyWorkingHours;
                        command.Parameters.Add("@Status", SqlDbType.NVarChar, 50).Value = workPeriod.Status ?? (object)DBNull.Value;
                        command.Parameters.Add("@CreatedDate", SqlDbType.DateTime).Value = workPeriod.CreatedDate;

                        return (int)command.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in AddWorkPeriod: {ex.Message}");
                throw; // إعادة رمي الاستثناء للتعامل معه في الطبقة الأعلى
            }
        }

        public static void UpdateWorkPeriod(WorkPeriod workPeriod)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = @"UPDATE WorkPeriods SET
                EmployeeCode = @EmployeeCode,
                EmployeeName = @EmployeeName,
                ProjectName = @ProjectName,
                Description = @Description,
                StartDate = @StartDate,
                EndDate = @EndDate,
                WorkingDays = @WorkingDays,
                DailyWorkingHours = @DailyWorkingHours,
                Status = @Status
            WHERE WorkPeriodId = @WorkPeriodId";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.Add("@WorkPeriodId", SqlDbType.Int).Value = workPeriod.WorkPeriodId;
                        command.Parameters.Add("@EmployeeCode", SqlDbType.Int).Value = workPeriod.EmployeeCode;
                        command.Parameters.Add("@EmployeeName", SqlDbType.NVarChar, 100).Value = workPeriod.EmployeeName ?? (object)DBNull.Value;
                        command.Parameters.Add("@ProjectName", SqlDbType.NVarChar, 100).Value = workPeriod.ProjectName ?? (object)DBNull.Value;
                        command.Parameters.Add("@Description", SqlDbType.NVarChar, -1).Value = workPeriod.Description ?? (object)DBNull.Value;
                        command.Parameters.Add("@StartDate", SqlDbType.Date).Value = workPeriod.StartDate;
                        command.Parameters.Add("@EndDate", SqlDbType.Date).Value = workPeriod.EndDate;
                        command.Parameters.Add("@WorkingDays", SqlDbType.NVarChar, 50).Value = workPeriod.WorkingDays ?? (object)DBNull.Value;
                        command.Parameters.Add("@DailyWorkingHours", SqlDbType.Float).Value = workPeriod.DailyWorkingHours;
                        command.Parameters.Add("@Status", SqlDbType.NVarChar, 50).Value = workPeriod.Status ?? (object)DBNull.Value;

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in UpdateWorkPeriod: {ex.Message}");
                throw;
            }
        }

        public static void DeleteWorkPeriod(int workPeriodId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // حذف التبعيات في DailyWorkStatus أولاً
                        string deleteDependenciesSql = "DELETE FROM DailyWorkStatus WHERE WorkPeriodId = @WorkPeriodId";
                        using (var cmd1 = new SqlCommand(deleteDependenciesSql, connection, transaction))
                        {
                            cmd1.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                            cmd1.ExecuteNonQuery();
                        }

                        // حذف فترة العمل نفسها
                        string deleteWorkPeriodSql = "DELETE FROM WorkPeriods WHERE WorkPeriodId = @WorkPeriodId";
                        using (var cmd2 = new SqlCommand(deleteWorkPeriodSql, connection, transaction))
                        {
                            cmd2.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                            cmd2.ExecuteNonQuery();
                        }

                        transaction.Commit();
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        public static DataTable GetAllWorkPeriods()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT
                    WorkPeriodId as 'المعرف',
                    EmployeeCode as 'كود الموظف',
                    EmployeeName as 'اسم الموظف',
                    ProjectName as 'مكان العمل',
                    StartDate as 'تاريخ البداية',
                    EndDate as 'تاريخ النهاية',
                    WorkingDays as 'أيام العمل',
                    DailyWorkingHours as 'ساعات العمل اليومية',
                    Status as 'الحالة',
                    Description as 'الوصف'
                FROM WorkPeriods
                ORDER BY StartDate DESC";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static DataTable GetWorkPeriodsByEmployee(int employeeCode)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT
                    WorkPeriodId as 'المعرف',
                    ProjectName as 'مكان العمل',
                    StartDate as 'تاريخ البداية',
                    EndDate as 'تاريخ النهاية',
                    WorkingDays as 'أيام العمل',
                    DailyWorkingHours as 'ساعات العمل اليومية',
                    Status as 'الحالة',
                    Description as 'الوصف'
                FROM WorkPeriods
                WHERE EmployeeCode = @EmployeeCode
                ORDER BY StartDate DESC";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    var table = new DataTable();
                    using (var adapter = new SqlDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }

        public static List<WorkPeriod> GetActiveWorkPeriods(int employeeCode)
        {
            var workPeriods = new List<WorkPeriod>();

            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = @"SELECT * FROM WorkPeriods
                        WHERE EmployeeCode = @EmployeeCode
                        AND Status IN ('نشط', 'نشطة')
                        AND StartDate <= @Today
                        AND EndDate >= @Today";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                        command.Parameters.AddWithValue("@Today", DateTime.Today.ToString("yyyy-MM-dd"));

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                try
                                {
                                    var workPeriod = new WorkPeriod
                                    {
                                        WorkPeriodId = reader.GetInt32("WorkPeriodId"),
                                        EmployeeCode = reader.GetInt32("EmployeeCode"),
                                        EmployeeName = reader.IsDBNull("EmployeeName") ? "" : reader.GetString("EmployeeName"),
                                        ProjectName = reader.IsDBNull("ProjectName") ? "مكان عمل غير محدد" : reader.GetString("ProjectName"),
                                        Description = reader.IsDBNull("Description") ? "" : reader.GetString("Description"),
                                        WorkingDays = reader.IsDBNull("WorkingDays") ? "الأحد,الاثنين,الثلاثاء,الأربعاء,الخميس" : reader.GetString("WorkingDays"),
                                        DailyWorkingHours = reader.IsDBNull("DailyWorkingHours") ? 8.0 : reader.GetDouble("DailyWorkingHours"),
                                        Status = reader.IsDBNull("Status") ? "نشط" : reader.GetString("Status")
                                    };

                                    // معالجة التواريخ بحذر
                                    if (!reader.IsDBNull("StartDate") && DateTime.TryParse(reader.GetString("StartDate"), out DateTime startDate))
                                    {
                                        workPeriod.StartDate = startDate;
                                    }
                                    else
                                    {
                                        workPeriod.StartDate = DateTime.Today;
                                    }

                                    if (!reader.IsDBNull("EndDate") && DateTime.TryParse(reader.GetString("EndDate"), out DateTime endDate))
                                    {
                                        workPeriod.EndDate = endDate;
                                    }
                                    else
                                    {
                                        workPeriod.EndDate = DateTime.Today.AddDays(30);
                                    }

                                    if (!reader.IsDBNull("CreatedDate") && DateTime.TryParse(reader.GetString("CreatedDate"), out DateTime createdDate))
                                    {
                                        workPeriod.CreatedDate = createdDate;
                                    }
                                    else
                                    {
                                        workPeriod.CreatedDate = DateTime.Now;
                                    }

                                    workPeriods.Add(workPeriod);
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"خطأ في قراءة فترة العمل: {ex.Message}");
                                    continue;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على فترات العمل النشطة: {ex.Message}");
            }

            return workPeriods;
        }
        public static WorkPeriod? GetWorkPeriodById(int workPeriodId)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT * FROM WorkPeriods WHERE WorkPeriodId = @WorkPeriodId";
                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                // التحقق من وجود الأعمدة أولاً
                                int workPeriodIdOrdinal = reader.GetOrdinal("WorkPeriodId");
                                int employeeCodeOrdinal = reader.GetOrdinal("EmployeeCode");
                                int employeeNameOrdinal = reader.GetOrdinal("EmployeeName");
                                int dailyWorkingHoursOrdinal = reader.GetOrdinal("DailyWorkingHours");
                                int statusOrdinal = reader.GetOrdinal("Status");

                                return new WorkPeriod
                                {
                                    WorkPeriodId = reader.GetInt32(workPeriodIdOrdinal),
                                    EmployeeCode = reader.GetInt32(employeeCodeOrdinal),
                                    EmployeeName = reader.GetString(employeeNameOrdinal),
                                    ProjectName = reader.IsDBNull("ProjectName") ? null : reader.GetString("ProjectName"),
                                    Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                                    StartDate = reader.IsDBNull("StartDate") ? DateTime.MinValue : reader.GetDateTime("StartDate"),
                                    EndDate = reader.IsDBNull("EndDate") ? DateTime.MinValue : reader.GetDateTime("EndDate"),
                                    WorkingDays = reader.IsDBNull("WorkingDays") ? null : reader.GetString("WorkingDays"),
                                    DailyWorkingHours = reader.IsDBNull(dailyWorkingHoursOrdinal) ? 0 : reader.GetDouble(dailyWorkingHoursOrdinal),
                                    Status = reader.IsDBNull(statusOrdinal) ? "غير محدد" : reader.GetString(statusOrdinal),
                                    CreatedDate = reader.IsDBNull("CreatedDate") ? DateTime.MinValue : reader.GetDateTime("CreatedDate")
                                };
                            }
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in GetWorkPeriodById: {ex.Message}");
                return null;
            }
        }

        public static DataTable GetWorkPeriodsByEmployee(int employeeCode, DateTime startDate, DateTime endDate)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT
                    WorkPeriodId as 'المعرف',
                    EmployeeName as 'اسم الموظف',
                    ProjectName as 'مكان العمل',
                    StartDate as 'تاريخ البداية',
                    EndDate as 'تاريخ النهاية',
                    WorkingDays as 'أيام العمل',
                    DailyWorkingHours as 'ساعات العمل اليومية',
                    Status as 'الحالة',
                    CreatedDate as 'تاريخ الإنشاء',
                    Description as 'الوصف'
                FROM WorkPeriods
                WHERE EmployeeCode = @EmployeeCode
                AND ((StartDate >= @StartDate AND StartDate <= @EndDate)
                     OR (EndDate >= @StartDate AND EndDate <= @EndDate)
                     OR (StartDate <= @StartDate AND EndDate >= @EndDate))
                ORDER BY StartDate DESC";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));

                    var table = new DataTable();
                    using (var adapter = new SqlDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }

        public static DataTable GetAllWorkPeriods(DateTime startDate, DateTime endDate)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT
                    WorkPeriodId as 'المعرف',
                    EmployeeCode as 'كود الموظف',
                    EmployeeName as 'اسم الموظف',
                    ProjectName as 'مكان العمل',
                    StartDate as 'تاريخ البداية',
                    EndDate as 'تاريخ النهاية',
                    WorkingDays as 'أيام العمل',
                    DailyWorkingHours as 'ساعات العمل اليومية',
                    Status as 'الحالة',
                    CreatedDate as 'تاريخ الإنشاء',
                     Description as 'الوصف'
                FROM WorkPeriods
                WHERE ((StartDate >= @StartDate AND StartDate <= @EndDate)
                       OR (EndDate >= @StartDate AND EndDate <= @EndDate)
                       OR (StartDate <= @StartDate AND EndDate >= @EndDate))
                ORDER BY EmployeeName, StartDate DESC";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));

                    var table = new DataTable();
                    using (var adapter = new SqlDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }


        public static int CreateWorkGroup(string groupName, string description, string createdBy)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = @"INSERT INTO WorkGroups (GroupName, Description, CreatedDate, CreatedBy, LastModified, IsActive)
                        VALUES (@GroupName, @Description, @CreatedDate, @CreatedBy, @LastModified, 1);
                        SELECT CAST(SCOPE_IDENTITY() AS INT);"; // تغيير لـ SQL Server

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.Add("@GroupName", SqlDbType.NVarChar, 100).Value = groupName;
                        command.Parameters.Add("@Description", SqlDbType.NVarChar, -1).Value = string.IsNullOrEmpty(description) ? (object)DBNull.Value : description;
                        command.Parameters.Add("@CreatedDate", SqlDbType.DateTime).Value = DateTime.Now;
                        command.Parameters.Add("@CreatedBy", SqlDbType.NVarChar, 100).Value = createdBy ?? (object)DBNull.Value;
                        command.Parameters.Add("@LastModified", SqlDbType.DateTime).Value = DateTime.Now;

                        return (int)command.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in CreateWorkGroup: {ex.Message}");
                throw;
            }
        }
        public static void AddMemberToWorkGroup(int groupId, int employeeCode, string employeeName, string memberType, string keyCardNumber = "")
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();

                // التحقق من وجود الجدول أولاً وإنشاؤه إذا لم يكن موجوداً
                string tableCheckSql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'WorkGroupMembers'";
                using (var tableCheckCmd = new SqlCommand(tableCheckSql, connection))
                {
                    int tableExists = Convert.ToInt32(tableCheckCmd.ExecuteScalar());
                    System.Diagnostics.Debug.WriteLine($"جدول WorkGroupMembers موجود: {tableExists == 1}");

                    if (tableExists == 0)
                    {
                        System.Diagnostics.Debug.WriteLine("🔧 إنشاء جدول WorkGroupMembers...");
                        string createTableSql = @"
                            CREATE TABLE WorkGroupMembers (
                                MemberId INT IDENTITY(1,1) PRIMARY KEY,
                                GroupId INT NOT NULL,
                                EmployeeCode INT NOT NULL,
                                EmployeeName NVARCHAR(255) NOT NULL,
                                MemberType NVARCHAR(50) NOT NULL,
                                KeyCardNumber NVARCHAR(50) NOT NULL DEFAULT '',
                                AddedDate DATETIME NOT NULL DEFAULT GETDATE(),
                                IsActive BIT NOT NULL DEFAULT 1,
                                FOREIGN KEY (GroupId) REFERENCES WorkGroups(GroupId)
                            )";

                        using (var createCmd = new SqlCommand(createTableSql, connection))
                        {
                            createCmd.ExecuteNonQuery();
                            System.Diagnostics.Debug.WriteLine("✅ تم إنشاء جدول WorkGroupMembers بنجاح");
                        }
                    }

                }

                // التحقق من عدم وجود العضو مسبقاً
                string checkSql = "SELECT COUNT(*) FROM WorkGroupMembers WHERE GroupId = @GroupId AND EmployeeCode = @EmployeeCode";
                using (var checkCommand = new SqlCommand(checkSql, connection))
                {
                    checkCommand.Parameters.AddWithValue("@GroupId", groupId);
                    checkCommand.Parameters.AddWithValue("@EmployeeCode", employeeCode);

                    int count = Convert.ToInt32(checkCommand.ExecuteScalar());
                    System.Diagnostics.Debug.WriteLine($"الموظف {employeeCode} موجود مسبقاً في المجموعة {groupId}: {count > 0}");

                    if (count > 0)
                    {
                        // تحديث نوع العضوية ورقم البطاقة إذا كان موجوداً
                        string updateSql = "UPDATE WorkGroupMembers SET MemberType = @MemberType, KeyCardNumber = @KeyCardNumber WHERE GroupId = @GroupId AND EmployeeCode = @EmployeeCode";
                        using (var updateCommand = new SqlCommand(updateSql, connection))
                        {
                            updateCommand.Parameters.AddWithValue("@GroupId", groupId);
                            updateCommand.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                            updateCommand.Parameters.AddWithValue("@MemberType", memberType);
                            updateCommand.Parameters.AddWithValue("@KeyCardNumber", keyCardNumber);
                            int rowsAffected = updateCommand.ExecuteNonQuery();
                            System.Diagnostics.Debug.WriteLine($"تم تحديث الموظف {employeeName} إلى نوع: {memberType} - صفوف متأثرة: {rowsAffected}");
                        }
                        return;
                    }
                }

                // إضافة عضو جديد
                string sql = @"INSERT INTO WorkGroupMembers (GroupId, EmployeeCode, EmployeeName, MemberType, KeyCardNumber, AddedDate)
                              VALUES (@GroupId, @EmployeeCode, @EmployeeName, @MemberType, @KeyCardNumber, @AddedDate)";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@GroupId", groupId);
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    command.Parameters.AddWithValue("@EmployeeName", employeeName);
                    command.Parameters.AddWithValue("@MemberType", memberType);
                    command.Parameters.AddWithValue("@KeyCardNumber", keyCardNumber);
                    command.Parameters.AddWithValue("@AddedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                    try
                    {
                        int rowsAffected = command.ExecuteNonQuery();
                        System.Diagnostics.Debug.WriteLine($"✅ تم إضافة عضو جديد: {employeeName} من نوع: '{memberType}' للمجموعة: {groupId} - صفوف متأثرة: {rowsAffected}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة العضو: {ex.Message}");
                        throw;
                    }
                }
            }
        }



        public static DataTable GetAllWorkGroups()
        {
            return GetAllWorkGroupsWithRealCounts();
        }

        public static DataTable GetAllWorkGroupsWithRealCounts()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();

                System.Diagnostics.Debug.WriteLine("=== عرض جميع المجموعات مع العدد الفعلي ===");

                // فحص سريع للبيانات الموجودة
                System.Diagnostics.Debug.WriteLine("=== فحص أنواع الأعضاء الموجودة ===");
                string checkSql = @"
                SELECT 
                    wg.GroupName,
                    wgm.MemberType,
                    COUNT(*) as Count
                FROM WorkGroupMembers wgm
                INNER JOIN WorkGroups wg ON wgm.GroupId = wg.GroupId
                WHERE wg.IsActive = 1
                GROUP BY wg.GroupName, wgm.MemberType
                ORDER BY wg.GroupName, wgm.MemberType";

                using (var checkCmd = new SqlCommand(checkSql, connection))
                using (var checkReader = checkCmd.ExecuteReader())
                {
                    while (checkReader.Read())
                    {
                        System.Diagnostics.Debug.WriteLine($"📊 {checkReader["GroupName"]} - نوع: '{checkReader["MemberType"]}' - العدد: {checkReader["Count"]}");
                    }
                }

                // استعلام مُبسط يستخدم JOIN بدلاً من subqueries
                string sql = @"
                SELECT 
                    wg.GroupId as 'معرف المجموعة',
                    wg.GroupName as 'اسم المجموعة',
                    wg.CreatedDate as 'تاريخ الإنشاء',
                    wg.CreatedBy as 'أنشأ بواسطة',
                    wg.Description as 'الوصف',
                    -- حساب العدد باستخدام GROUP BY و SUM
                    COUNT(wgm.MemberId) as 'إجمالي الأعضاء',
                    SUM(CASE WHEN wgm.MemberType = N'حضور' THEN 1 ELSE 0 END) as 'حضور',
                    SUM(CASE WHEN wgm.MemberType = N'إجازة' THEN 1 ELSE 0 END) as 'إجازة',
                    SUM(CASE WHEN wgm.MemberType = N'غياب' THEN 1 ELSE 0 END) as 'غياب',
                    SUM(CASE WHEN wgm.MemberType = N'هروب' THEN 1 ELSE 0 END) as 'هروب',
                    SUM(CASE WHEN wgm.MemberType = N'نشط' THEN 1 ELSE 0 END) as 'نشط',
                    SUM(CASE WHEN wgm.MemberType = N'مكتمل' THEN 1 ELSE 0 END) as 'مكتمل',
                    SUM(CASE WHEN wgm.MemberType = N'ملغى' THEN 1 ELSE 0 END) as 'ملغى',
                    SUM(CASE WHEN wgm.MemberType = N'لم يبدأ' THEN 1 ELSE 0 END) as 'لم يبدأ',
                    -- للتوافق مع الكود الموجود (عمودين رئيسيين - فقط الحالات الأساسية)
                    SUM(CASE WHEN wgm.MemberType = N'حضور' THEN 1 ELSE 0 END) as 'موظفين العمل',
                    SUM(CASE WHEN wgm.MemberType = N'إجازة' THEN 1 ELSE 0 END) as 'موظفين الإجازة'
                FROM WorkGroups wg
                LEFT JOIN WorkGroupMembers wgm ON wg.GroupId = wgm.GroupId
                WHERE wg.IsActive = 1
                GROUP BY wg.GroupId, wg.GroupName, wg.CreatedDate, wg.CreatedBy, wg.Description
                ORDER BY wg.CreatedDate DESC";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);

                    System.Diagnostics.Debug.WriteLine($"تم تحميل {table.Rows.Count} مجموعة مع العدد الفعلي");
                    foreach (DataRow row in table.Rows)
                    {
                        System.Diagnostics.Debug.WriteLine($"مجموعة: {row["اسم المجموعة"]} - إجمالي: {row["إجمالي الأعضاء"]} - حضور: {row["حضور"]} - إجازة: {row["إجازة"]} - غياب: {row["غياب"]} - هروب: {row["هروب"]}");
                        System.Diagnostics.Debug.WriteLine($"  └── موظفين العمل: {row["موظفين العمل"]} - موظفين الإجازة: {row["موظفين الإجازة"]}");
                    }

                    return table;
                }
            }
        }

        public static DataTable GetWorkGroupMembers(int groupId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"
                SELECT 
                    wgm.MemberId,
                    wgm.EmployeeCode,
                    e.Name as EmployeeName,
                    wgm.MemberType,
                    wgm.AddedDate
                FROM WorkGroupMembers wgm
                INNER JOIN Employees e ON wgm.EmployeeCode = e.EmployeeCode
                WHERE wgm.GroupId = @GroupId
                ORDER BY wgm.MemberType, e.Name";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    adapter.SelectCommand.Parameters.AddWithValue("@GroupId", groupId);
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static DataTable GetAllWorkGroups(string workStatus, string vacationStatus)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();

                // اختبار سريع للتأكد من وجود البيانات
                System.Diagnostics.Debug.WriteLine("=== اختبار سريع للبيانات ===");

                string quickTestSql = @"SELECT 
                    wgm.GroupId, 
                    wg.GroupName,
                    wgm.MemberType, 
                    COUNT(*) as Count 
                FROM WorkGroupMembers wgm
                INNER JOIN WorkGroups wg ON wgm.GroupId = wg.GroupId
                WHERE wg.IsActive = 1
                GROUP BY wgm.GroupId, wg.GroupName, wgm.MemberType
                ORDER BY wgm.GroupId, wgm.MemberType";

                using (var quickTestCmd = new SqlCommand(quickTestSql, connection))
                using (var quickTestReader = quickTestCmd.ExecuteReader())
                {
                    while (quickTestReader.Read())
                    {
                        System.Diagnostics.Debug.WriteLine($"مجموعة {quickTestReader["GroupId"]} ({quickTestReader["GroupName"]}): نوع '{quickTestReader["MemberType"]}' = {quickTestReader["Count"]}");
                    }
                }

                // اختبار لعرض جميع أنواع الأعضاء الموجودة في قاعدة البيانات مع التفاصيل
                System.Diagnostics.Debug.WriteLine("=== جميع أنواع الأعضاء الموجودة مع التفاصيل ===");
                string allMemberTypesSql = @"
                SELECT 
                    MemberType,
                    COUNT(*) as Count,
                    LEN(MemberType) as Length,
                    ASCII(LEFT(MemberType, 1)) as FirstCharASCII,
                    ASCII(RIGHT(MemberType, 1)) as LastCharASCII
                FROM WorkGroupMembers 
                GROUP BY MemberType 
                ORDER BY MemberType";
                using (var allMemberTypesCmd = new SqlCommand(allMemberTypesSql, connection))
                using (var allMemberTypesReader = allMemberTypesCmd.ExecuteReader())
                {
                    while (allMemberTypesReader.Read())
                    {
                        string memberType = allMemberTypesReader["MemberType"].ToString();
                        int count = (int)allMemberTypesReader["Count"];
                        int length = (int)allMemberTypesReader["Length"];
                        int firstChar = (int)allMemberTypesReader["FirstCharASCII"];
                        int lastChar = (int)allMemberTypesReader["LastCharASCII"];

                        System.Diagnostics.Debug.WriteLine($"نوع عضوية: '{memberType}' - العدد: {count} - الطول: {length} - الحرف الأول: {firstChar} - الحرف الأخير: {lastChar}");

                        // اختبار المطابقة
                        bool matchesAttendance = memberType == "حضور";
                        bool matchesVacation = memberType == "إجازة";
                        System.Diagnostics.Debug.WriteLine($"  مطابقة 'حضور': {matchesAttendance} - مطابقة 'إجازة': {matchesVacation}");

                        // عرض الأحرف بالتفصيل للنصوص القصيرة
                        if (length <= 10)
                        {
                            var chars = memberType.Select(c => $"'{c}'({(int)c})").ToArray();
                            System.Diagnostics.Debug.WriteLine($"  الأحرف: {string.Join(", ", chars)}");
                        }
                    }
                }

                // عرض عينة من البيانات الفعلية
                System.Diagnostics.Debug.WriteLine("=== عينة من البيانات الفعلية ===");
                string sampleSql = @"
                SELECT TOP 10 
                    wg.GroupName, 
                    wgm.MemberType, 
                    wgm.EmployeeCode,
                    '[' + wgm.MemberType + ']' as MemberTypeWithBrackets,
                    DATALENGTH(wgm.MemberType) as ByteLength
                FROM WorkGroupMembers wgm
                INNER JOIN WorkGroups wg ON wgm.GroupId = wg.GroupId
                ORDER BY wgm.MemberId";
                using (var sampleCmd = new SqlCommand(sampleSql, connection))
                using (var sampleReader = sampleCmd.ExecuteReader())
                {
                    while (sampleReader.Read())
                    {
                        System.Diagnostics.Debug.WriteLine($"عينة: المجموعة='{sampleReader["GroupName"]}', النوع='{sampleReader["MemberType"]}', مع أقواس={sampleReader["MemberTypeWithBrackets"]}, طول البايت={sampleReader["ByteLength"]}");
                    }
                }

                // اختبار لعرض العدد الإجمالي للأعضاء في كل مجموعة
                System.Diagnostics.Debug.WriteLine("=== العدد الإجمالي للأعضاء في كل مجموعة ===");
                string totalCountSql = @"SELECT 
                    wg.GroupId, 
                    wg.GroupName, 
                    COUNT(wgm.MemberId) as TotalMembers
                FROM WorkGroups wg
                LEFT JOIN WorkGroupMembers wgm ON wg.GroupId = wgm.GroupId
                WHERE wg.IsActive = 1
                GROUP BY wg.GroupId, wg.GroupName
                ORDER BY wg.GroupId";

                using (var totalCountCmd = new SqlCommand(totalCountSql, connection))
                using (var totalCountReader = totalCountCmd.ExecuteReader())
                {
                    while (totalCountReader.Read())
                    {
                        System.Diagnostics.Debug.WriteLine($"مجموعة {totalCountReader["GroupId"]} ({totalCountReader["GroupName"]}): إجمالي الأعضاء = {totalCountReader["TotalMembers"]}");
                    }
                }

                // الاستعلام الأساسي - حساب العدد الإجمالي وتقسيمه حسب النوع
                string sql = @"SELECT 
                    wg.GroupId as 'معرف المجموعة',
                    wg.GroupName as 'اسم المجموعة',
                    wg.CreatedDate as 'تاريخ الإنشاء',
                    wg.CreatedBy as 'أنشأ بواسطة',
                    ISNULL((SELECT COUNT(*) FROM WorkGroupMembers WHERE GroupId = wg.GroupId AND MemberType = 'حضور'), 0) as 'موظفين العمل',                   
                    ISNULL((SELECT COUNT(*) FROM WorkGroupMembers WHERE GroupId = wg.GroupId AND MemberType = 'إجازة'), 0) as 'موظفين الإجازة',
                    wg.Description as 'الوصف'
                FROM WorkGroups wg
                WHERE wg.IsActive = 1
                ORDER BY wg.CreatedDate DESC";

                // اختبار الاستعلام الأساسي مع تتبع النتائج
                System.Diagnostics.Debug.WriteLine("=== اختبار الاستعلام الأساسي مع تتبع النتائج ===");
                using (var testMainCmd = new SqlCommand(sql, connection))
                using (var testMainReader = testMainCmd.ExecuteReader())
                {
                    while (testMainReader.Read())
                    {
                        System.Diagnostics.Debug.WriteLine($"نتيجة الاستعلام الأساسي: {testMainReader["اسم المجموعة"]} - عمل: {testMainReader["موظفين العمل"]} - إجازة: {testMainReader["موظفين الإجازة"]}");
                    }
                }

                System.Diagnostics.Debug.WriteLine("=== تنفيذ الاستعلام الأساسي ===");

                // إنشاء استعلام جديد يعرض العدد الفعلي بدلاً من البحث عن الحالات المختارة
                string realCountSql = @"
                SELECT 
                    wg.GroupId as 'معرف المجموعة',
                    wg.GroupName as 'اسم المجموعة',
                    wg.CreatedDate as 'تاريخ الإنشاء',
                    wg.CreatedBy as 'أنشأ بواسطة',
                    wg.Description as 'الوصف',
                    -- حساب العدد الفعلي لجميع الأنواع (بغض النظر عن ComboBox)
                    ISNULL((SELECT COUNT(*) FROM WorkGroupMembers wgm WHERE wgm.GroupId = wg.GroupId), 0) as 'إجمالي الأعضاء',
                    -- عرض التوزيع الفعلي للأنواع
                    ISNULL((SELECT COUNT(*) FROM WorkGroupMembers wgm WHERE wgm.GroupId = wg.GroupId AND wgm.MemberType = 'حضور'), 0) as 'حضور',
                    ISNULL((SELECT COUNT(*) FROM WorkGroupMembers wgm WHERE wgm.GroupId = wg.GroupId AND wgm.MemberType = 'إجازة'), 0) as 'إجازة',
                    ISNULL((SELECT COUNT(*) FROM WorkGroupMembers wgm WHERE wgm.GroupId = wg.GroupId AND wgm.MemberType = 'غياب'), 0) as 'غياب',
                    ISNULL((SELECT COUNT(*) FROM WorkGroupMembers wgm WHERE wgm.GroupId = wg.GroupId AND wgm.MemberType = 'هروب'), 0) as 'هروب',
                    ISNULL((SELECT COUNT(*) FROM WorkGroupMembers wgm WHERE wgm.GroupId = wg.GroupId AND wgm.MemberType = 'نشط'), 0) as 'نشط',
                    ISNULL((SELECT COUNT(*) FROM WorkGroupMembers wgm WHERE wgm.GroupId = wg.GroupId AND wgm.MemberType = 'مكتمل'), 0) as 'مكتمل',
                    ISNULL((SELECT COUNT(*) FROM WorkGroupMembers wgm WHERE wgm.GroupId = wg.GroupId AND wgm.MemberType = 'ملغى'), 0) as 'ملغى',
                    ISNULL((SELECT COUNT(*) FROM WorkGroupMembers wgm WHERE wgm.GroupId = wg.GroupId AND wgm.MemberType = 'لم يبدأ'), 0) as 'لم يبدأ',
                    -- عمودين للتوافق مع الكود الموجود (يمكن تخصيصهما لاحقاً)
                    ISNULL((SELECT COUNT(*) FROM WorkGroupMembers wgm WHERE wgm.GroupId = wg.GroupId AND wgm.MemberType = @WorkStatus), 0) as 'موظفين العمل',
                    ISNULL((SELECT COUNT(*) FROM WorkGroupMembers wgm WHERE wgm.GroupId = wg.GroupId AND wgm.MemberType = @VacationStatus), 0) as 'موظفين الإجازة'
                FROM WorkGroups wg
                WHERE wg.IsActive = 1
                ORDER BY wg.CreatedDate DESC";

                // اختبار الاستعلام الجديد
                System.Diagnostics.Debug.WriteLine("=== اختبار الاستعلام الجديد (العدد الفعلي) ===");
                using (var realCountCmd = new SqlCommand(realCountSql, connection))
                {
                    realCountCmd.Parameters.AddWithValue("@WorkStatus", workStatus);
                    realCountCmd.Parameters.AddWithValue("@VacationStatus", vacationStatus);

                    using (var realCountReader = realCountCmd.ExecuteReader())
                    {
                        while (realCountReader.Read())
                        {
                            System.Diagnostics.Debug.WriteLine($"العدد الفعلي: {realCountReader["اسم المجموعة"]} - إجمالي: {realCountReader["إجمالي الأعضاء"]} - حضور: {realCountReader["حضور"]} - إجازة: {realCountReader["إجازة"]} - غياب: {realCountReader["غياب"]} - هروب: {realCountReader["هروب"]}");
                        }
                    }
                }

                // استخدام الاستعلام الجديد كاستعلام أساسي
                sql = realCountSql;

                System.Diagnostics.Debug.WriteLine($"ملاحظة: ComboBox للمرجع فقط - WorkStatus='{workStatus}', VacationStatus='{vacationStatus}'");

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    // إضافة المعاملات للاستعلام
                    adapter.SelectCommand.Parameters.AddWithValue("@WorkStatus", workStatus);
                    adapter.SelectCommand.Parameters.AddWithValue("@VacationStatus", vacationStatus);

                    var table = new DataTable();
                    adapter.Fill(table);

                    System.Diagnostics.Debug.WriteLine($"تم تحميل {table.Rows.Count} مجموعة من قاعدة البيانات");
                    foreach (DataRow row in table.Rows)
                    {
                        System.Diagnostics.Debug.WriteLine($"مجموعة: {row["اسم المجموعة"]} - موظفين العمل: {row["موظفين العمل"]} - موظفين الإجازة: {row["موظفين الإجازة"]}");
                    }

                    return table;
                }
            }
        }



        public static void LoadWorkGroupToForm(int groupId, out List<int> workEmployees, out List<int> vacationEmployees)
        {
            workEmployees = new List<int>();
            vacationEmployees = new List<int>();

            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT EmployeeCode, MemberType FROM WorkGroupMembers WHERE GroupId = @GroupId";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@GroupId", groupId);
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                // التعامل مع أنواع البيانات المختلفة
                                var employeeCodeValue = reader["EmployeeCode"];
                                int employeeCode = Convert.ToInt32(employeeCodeValue);
                                string memberType = reader.GetString("MemberType");

                                System.Diagnostics.Debug.WriteLine($"تحميل عضو: {employeeCode} - {memberType}");

                                // تصنيف الموظفين حسب نوع الحالة (كما تم حفظهم)
                                // حالات العمل: حضور، غياب، هروب، نشط، مكتمل
                                if (memberType == "حضور" || memberType == "غياب" || memberType == "هروب" || memberType == "نشط" || memberType == "مكتمل")
                                {
                                    workEmployees.Add(employeeCode);
                                    System.Diagnostics.Debug.WriteLine($"  ➤ إضافة للعمل: {employeeCode} - {memberType}");
                                }
                                // حالات الإجازة: إجازة، ملغي، لم يبدأ
                                else if (memberType == "إجازة" || memberType == "ملغي" || memberType == "لم يبدأ")
                                {
                                    vacationEmployees.Add(employeeCode);
                                    System.Diagnostics.Debug.WriteLine($"  ➤ إضافة للإجازة: {employeeCode} - {memberType}");
                                }
                                else
                                {
                                    // حالات أخرى غير متوقعة
                                    System.Diagnostics.Debug.WriteLine($"  ⚠️ حالة غير متوقعة: {employeeCode} - {memberType}");
                                }
                            }
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"تم تحميل {workEmployees.Count} موظف عمل و {vacationEmployees.Count} موظف إجازة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل أعضاء المجموعة: {ex.Message}");
                throw;
            }
        }

        public static void DeleteWorkGroup(int groupId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // حذف أعضاء المجموعة
                        string deleteMembersSql = "DELETE FROM WorkGroupMembers WHERE GroupId = @GroupId";
                        using (var command = new SqlCommand(deleteMembersSql, connection))
                        {
                            command.Transaction = transaction;
                            command.Parameters.AddWithValue("@GroupId", groupId);
                            command.ExecuteNonQuery();
                        }

                        // حذف المجموعة
                        string deleteGroupSql = "DELETE FROM WorkGroups WHERE GroupId = @GroupId";
                        using (var command = new SqlCommand(deleteGroupSql, connection))
                        {
                            command.Transaction = transaction;
                            command.Parameters.AddWithValue("@GroupId", groupId);
                            command.ExecuteNonQuery();
                        }

                        transaction.Commit();
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        public static void ClearWorkGroupMembers(int groupId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = "DELETE FROM WorkGroupMembers WHERE GroupId = @GroupId";
                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@GroupId", groupId);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static void UpdateWorkGroup(int groupId, string groupName, string description)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"UPDATE WorkGroups SET
                    GroupName = @GroupName,
                    Description = @Description,
                    LastModified = @LastModified
                WHERE GroupId = @GroupId";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@GroupId", groupId);
                    command.Parameters.AddWithValue("@GroupName", groupName);
                    command.Parameters.AddWithValue("@Description", description ?? "");
                    command.Parameters.AddWithValue("@LastModified", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    command.ExecuteNonQuery();
                }
            }
        }

        public static DataTable SearchWorkPeriods(string searchTerm, DateTime startDate, DateTime endDate)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = @"SELECT
                     WorkPeriodId as 'المعرف',
                     EmployeeCode as 'كود الموظف',
                     EmployeeName as 'اسم الموظف',
                     ProjectName as 'مكان العمل',
                     StartDate as 'تاريخ البداية',
                     EndDate as 'تاريخ النهاية',
                     WorkingDays as 'أيام العمل',
                     DailyWorkingHours as 'ساعات العمل اليومية',
                     Status as 'الحالة',
                     CreatedDate as 'تاريخ الإنشاء',
                     Description as 'الوصف'
                     FROM WorkPeriods
                     WHERE (EmployeeName LIKE @SearchTerm
                     OR ProjectName LIKE @SearchTerm
                     OR Description LIKE @SearchTerm)
                     AND StartDate >= @StartDate AND EndDate <= @EndDate
                     ORDER BY StartDate DESC, EmployeeName";


                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");
                        command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));

                        var table = new DataTable();
                        using (var adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(table);
                        }

                        System.Diagnostics.Debug.WriteLine($"البحث عن '{searchTerm}' - تم العثور على {table.Rows.Count} فترة عمل");
                        return table;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث عن فترات العمل: {ex.Message}");
                return new DataTable();
            }
        }

        public static DataTable GetWorkPeriodsByGroup(string groupName, DateTime startDate, DateTime endDate)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();

                    System.Diagnostics.Debug.WriteLine($"البحث في المجموعة: '{groupName}' من {startDate:yyyy-MM-dd} إلى {endDate:yyyy-MM-dd}");

                    // التحقق من وجود المجموعة أولاً
                    string checkGroupSql = "SELECT COUNT(*) FROM WorkGroups WHERE GroupName = @GroupName AND IsActive = 1";
                    using (var checkCmd = new SqlCommand(checkGroupSql, connection))
                    {
                        checkCmd.Parameters.AddWithValue("@GroupName", groupName);
                        int groupCount = (int)checkCmd.ExecuteScalar();
                        System.Diagnostics.Debug.WriteLine($"عدد المجموعات الموجودة باسم '{groupName}': {groupCount}");
                    }

                    // التحقق من وجود أعضاء في المجموعة
                    string checkMembersSql = @"SELECT COUNT(*) FROM WorkGroupMembers wgm 
                        INNER JOIN WorkGroups wg ON wgm.GroupId = wg.GroupId 
                        WHERE wg.GroupName = @GroupName AND wg.IsActive = 1";
                    using (var checkMembersCmd = new SqlCommand(checkMembersSql, connection))
                    {
                        checkMembersCmd.Parameters.AddWithValue("@GroupName", groupName);
                        int memberCount = (int)checkMembersCmd.ExecuteScalar();
                        System.Diagnostics.Debug.WriteLine($"عدد أعضاء المجموعة '{groupName}': {memberCount}");
                    }

                    // التحقق من وجود فترات عمل للموظفين في المجموعة
                    string checkWorkPeriodsSql = @"SELECT COUNT(DISTINCT wp.WorkPeriodId) 
                        FROM WorkPeriods wp
                        INNER JOIN WorkGroupMembers wgm ON wp.EmployeeCode = wgm.EmployeeCode
                        INNER JOIN WorkGroups wg ON wgm.GroupId = wg.GroupId
                        WHERE wg.GroupName = @GroupName AND wg.IsActive = 1";
                    using (var checkWorkPeriodsCmd = new SqlCommand(checkWorkPeriodsSql, connection))
                    {
                        checkWorkPeriodsCmd.Parameters.AddWithValue("@GroupName", groupName);
                        int workPeriodCount = (int)checkWorkPeriodsCmd.ExecuteScalar();
                        System.Diagnostics.Debug.WriteLine($"عدد فترات العمل للمجموعة '{groupName}': {workPeriodCount}");
                    }

                    // البحث عن فترات العمل التي تحتوي على موظفين من المجموعة المحددة
                    string sql = @"SELECT 
                        wp.WorkPeriodId as 'المعرف',
                        wp.EmployeeCode as 'كود الموظف',
                        wp.EmployeeName as 'اسم الموظف',
                        wp.ProjectName as 'مكان العمل',
                        wp.StartDate as 'تاريخ البداية',
                        wp.EndDate as 'تاريخ النهاية',
                        wp.WorkingDays as 'أيام العمل',
                        wp.DailyWorkingHours as 'ساعات العمل اليومية',
                        wp.Status as 'الحالة',
                        wg.GroupName as 'اسم المجموعة',
                        wp.CreatedDate as 'تاريخ الإنشاء',
                        wp.Description as 'الوصف',
                        wgm.MemberType as 'نوع العضوية',
                        wgm.MemberId as 'معرف العضو'
                    FROM WorkPeriods wp
                    INNER JOIN WorkGroupMembers wgm ON wp.EmployeeCode = wgm.EmployeeCode
                    INNER JOIN WorkGroups wg ON wgm.GroupId = wg.GroupId
                    WHERE wg.GroupName = @GroupName
                    AND wg.IsActive = 1
                    AND (
                        (CAST(wp.StartDate AS DATE) >= CAST(@StartDate AS DATE) AND CAST(wp.StartDate AS DATE) <= CAST(@EndDate AS DATE))
                        OR (CAST(wp.EndDate AS DATE) >= CAST(@StartDate AS DATE) AND CAST(wp.EndDate AS DATE) <= CAST(@EndDate AS DATE))
                        OR (CAST(wp.StartDate AS DATE) <= CAST(@StartDate AS DATE) AND CAST(wp.EndDate AS DATE) >= CAST(@EndDate AS DATE))
                    )
                    ORDER BY wp.StartDate DESC, 
                        CASE wgm.MemberType 
                            WHEN 'حضور' THEN 1
                            WHEN 'إجازة' THEN 2 
                            ELSE 3 
                        END,
                        wgm.MemberId, wp.EmployeeName";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@GroupName", groupName);
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);

                        var table = new DataTable();
                        using (var adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(table);
                        }

                        System.Diagnostics.Debug.WriteLine($"تم العثور على {table.Rows.Count} فترة عمل للمجموعة {groupName}");

                        // طباعة النتائج للتحقق
                        if (table.Rows.Count > 0)
                        {
                            System.Diagnostics.Debug.WriteLine("النتائج الموجودة:");
                            for (int i = 0; i < Math.Min(3, table.Rows.Count); i++)
                            {
                                var row = table.Rows[i];
                                System.Diagnostics.Debug.WriteLine($"  {i + 1}. {row["اسم الموظف"]} - {row["تاريخ البداية"]} - {row["تاريخ النهاية"]}");
                            }
                        }

                        return table;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث بالمجموعة: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                return new DataTable();
            }
        }
        // لتعديل المجموعة )
        public static void DeleteGroupMembers(int groupId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = "DELETE FROM WorkGroupMembers WHERE GroupId = @GroupId";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@GroupId", groupId);
                    command.ExecuteNonQuery();
                }
            }
        }

        // لعرض المجموعة حسب الحالة)
        public static DataTable GetWorkPeriodsByGroupId(int groupId, DateTime startDate, DateTime endDate)
        {
            DataTable dt = new DataTable();

            string sql = @"
        SELECT DISTINCT
            wp.WorkPeriodId as 'المعرف',
            wp.EmployeeCode as 'كود الموظف',
            wp.EmployeeName as 'اسم الموظف',
            wp.ProjectName as 'مكان العمل',
            wp.StartDate as 'تاريخ البداية',
            wp.EndDate as 'تاريخ النهاية',
            wp.WorkingDays as 'أيام العمل',
            wp.DailyWorkingHours as 'ساعات العمل اليومية',
            wp.Status as 'الحالة',
            wp.CreatedDate as 'تاريخ الإنشاء',
            wp.Description as 'الوصف'
        FROM WorkPeriods wp
        INNER JOIN WorkGroupMembers wgm ON wp.EmployeeCode = wgm.EmployeeCode
        WHERE wgm.GroupId = @GroupId
          AND ((wp.StartDate >= @StartDate AND wp.StartDate <= @EndDate)
               OR (wp.EndDate >= @StartDate AND wp.EndDate <= @EndDate)
               OR (wp.StartDate <= @StartDate AND wp.EndDate >= @EndDate))
        ORDER BY wp.StartDate DESC, wp.EmployeeName";

            using (var conn = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                conn.Open();
                using (var cmd = new SqlCommand(sql, conn))
                {
                    cmd.Parameters.AddWithValue("@GroupId", groupId);
                    cmd.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
                    cmd.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));

                    using (var adapter = new SqlDataAdapter(cmd))
                    {
                        adapter.Fill(dt);
                    }
                }
            }
            return dt;
        }
        public static DataTable GetWorkGroupsByDateRange(DateTime startDate, DateTime endDate)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();

                    // البحث عن المجموعات التي لها فترات عمل في النطاق الزمني المحدد
                    string sql = @"SELECT DISTINCT
                        wg.GroupId as 'معرف المجموعة',
                        wg.GroupName as 'اسم المجموعة',
                        COUNT(DISTINCT wp.WorkPeriodId) as 'عدد فترات العمل',
                        COUNT(DISTINCT wgm.EmployeeCode) as 'عدد الموظفين',
                        MIN(wp.StartDate) as 'أقدم فترة',
                        MAX(wp.EndDate) as 'أحدث فترة',
                        wg.CreatedDate as 'تاريخ الإنشاء',
                        wg.Description as 'الوصف'
                    FROM WorkGroups wg
                    INNER JOIN WorkGroupMembers wgm ON wg.GroupId = wgm.GroupId
                    INNER JOIN WorkPeriods wp ON wgm.EmployeeCode = wp.EmployeeCode
                    WHERE ((wp.StartDate >= @StartDate AND wp.StartDate <= @EndDate)
                           OR (wp.EndDate >= @StartDate AND wp.EndDate <= @EndDate)
                           OR (wp.StartDate <= @StartDate AND wp.EndDate >= @EndDate))
                    AND wg.IsActive = 1
                    GROUP BY wg.GroupId, wg.GroupName, wg.Description, wg.CreatedDate
                    ORDER BY wg.CreatedDate DESC";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));

                        var table = new DataTable();
                        using (var adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(table);
                        }

                        return table;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث عن المجموعات بالتاريخ: {ex.Message}");
                return new DataTable();
            }
        }


        public static DataTable SearchAttendanceByName(string employeeName, DateTime startDate, DateTime endDate)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = @"SELECT
                AttendanceId as 'المعرف',
                EmployeeCode as 'كود الموظف',
                EmployeeName as 'اسم الموظف',
                Date as 'التاريخ',
                CheckInTime as 'وقت الحضور',
                CheckOutTime as 'وقت الانصراف',
                WorkingHours as 'ساعات العمل',
                OvertimeHours as 'الساعات الإضافية',
                Status as 'الحالة',
                Notes as 'ملاحظات'
            FROM Attendance
            WHERE EmployeeName LIKE @EmployeeName
            AND Date >= @StartDate AND Date <= @EndDate
            ORDER BY Date DESC, CheckInTime DESC";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@EmployeeName", $"%{employeeName}%");
                        command.Parameters.AddWithValue("@StartDate", startDate);
                        command.Parameters.AddWithValue("@EndDate", endDate);

                        var table = new DataTable();
                        using (var adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(table);
                        }

                        System.Diagnostics.Debug.WriteLine($"البحث عن '{employeeName}' - تم العثور على {table.Rows.Count} سجل");
                        return table;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث بالاسم: {ex.Message}");
                return new DataTable();
            }
        }

        public static void CreateAbsenceRecordsForDate(DateTime date)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();

                    // الحصول على جميع الموظفين النشطين
                    string getAllEmployeesSql = "SELECT EmployeeCode, Name FROM Employees";
                    var allEmployees = new List<(int Code, string Name)>();

                    using (var command = new SqlCommand(getAllEmployeesSql, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                allEmployees.Add((reader.GetInt32("EmployeeCode"), reader.GetString("Name")));
                            }
                        }
                    }

                    // الحصول على الموظفين الذين لديهم سجلات حضور لهذا التاريخ
                    string getAttendedEmployeesSql = "SELECT DISTINCT EmployeeCode FROM Attendance WHERE Date = @Date";
                    var attendedEmployees = new HashSet<int>();

                    using (var command = new SqlCommand(getAttendedEmployeesSql, connection))
                    {
                        command.Parameters.AddWithValue("@Date", date.ToString("yyyy-MM-dd"));
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                attendedEmployees.Add(reader.GetInt32("EmployeeCode"));
                            }
                        }
                    }

                    // إنشاء سجلات غياب للموظفين الذين لم يحضروا
                    int absenceCount = 0;
                    foreach (var employee in allEmployees)
                    {
                        if (!attendedEmployees.Contains(employee.Code))
                        {
                            var absenceRecord = new Attendance
                            {
                                EmployeeCode = employee.Code,
                                EmployeeName = employee.Name,
                                Date = date,
                                Status = "غائب",
                                CheckInTime = null,
                                CheckOutTime = null,
                                WorkingHours = 0,
                                OvertimeHours = 0,
                                Notes = "غياب تلقائي - لم يسجل حضور",
                                CreatedDate = DateTime.Now
                            };

                            AddAttendance(absenceRecord);
                            absenceCount++;
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"تم إنشاء {absenceCount} سجل غياب للتاريخ {date:yyyy-MM-dd}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء سجلات الغياب: {ex.Message}");
                throw;
            }
        }

        public static void ProcessDailyAttendance(DateTime date)
        {
            try
            {
                // إنشاء سجلات الغياب للموظفين الذين لم يحضروا
                CreateAbsenceRecordsForDate(date);

                System.Diagnostics.Debug.WriteLine($"تم معالجة حضور يوم {date:yyyy-MM-dd}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في معالجة الحضور اليومي: {ex.Message}");
                throw;
            }
        }

        public static void ProcessAttendanceForDateRange(DateTime startDate, DateTime endDate)
        {
            try
            {
                DateTime currentDate = startDate;
                while (currentDate <= endDate)
                {
                    // تجاهل أيام الجمعة والسبت (عطلة نهاية الأسبوع)
                    if (currentDate.DayOfWeek != DayOfWeek.Friday && currentDate.DayOfWeek != DayOfWeek.Saturday)
                    {
                        CreateAbsenceRecordsForDate(currentDate);
                    }
                    currentDate = currentDate.AddDays(1);
                }

                System.Diagnostics.Debug.WriteLine($"تم معالجة الحضور للفترة من {startDate:yyyy-MM-dd} إلى {endDate:yyyy-MM-dd}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في معالجة الحضور للفترة: {ex.Message}");
                throw;
            }
        }

        public static Attendance GetAttendanceByEmployeeAndDate(int employeeCode, DateTime date)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = @"SELECT TOP 1 * FROM Attendance
                                 WHERE EmployeeCode = @EmployeeCode
                                 AND Date = @Date
                                 ORDER BY CreatedDate DESC";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                        command.Parameters.AddWithValue("@Date", date.ToString("yyyy-MM-dd"));

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new Attendance
                                {
                                    AttendanceId = reader.GetInt32("AttendanceId"),
                                    EmployeeCode = reader.GetInt32("EmployeeCode"),
                                    EmployeeName = reader.GetString("EmployeeName"),
                                    Date = DateTime.Parse(reader.GetString("Date")),
                                    CheckInTime = reader.IsDBNull("CheckInTime") ? null : DateTime.Parse(reader.GetString("CheckInTime")),
                                    CheckOutTime = reader.IsDBNull("CheckOutTime") ? null : DateTime.Parse(reader.GetString("CheckOutTime")),
                                    WorkingHours = reader.GetDouble("WorkingHours"),
                                    OvertimeHours = reader.GetDouble("OvertimeHours"),
                                    Status = reader.GetString("Status"),
                                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes"),
                                    CreatedDate = DateTime.Parse(reader.GetString("CreatedDate"))
                                };
                            }
                        }
                    }
                }

                return null; // لم يتم العثور على سجل
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث عن سجل الحضور: {ex.Message}");
                return null;
            }
        }

        #region إدارة التتبع اليومي لفترات العمل

        /// <summary>
        /// إنشاء سجلات يومية لفترة عمل
        /// </summary>
        public static void CreateDailyWorkStatusRecords(int workPeriodId, int employeeCode, string employeeName,
     DateTime startDate, DateTime endDate, string defaultStatus)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();

                    // حذف السجلات الموجودة لهذه الفترة (في حالة إعادة الإنشاء)
                    string deleteSql = "DELETE FROM DailyWorkStatus WHERE WorkPeriodId = @WorkPeriodId AND EmployeeCode = @EmployeeCode";
                    using (var deleteCommand = new SqlCommand(deleteSql, connection))
                    {
                        deleteCommand.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                        deleteCommand.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                        deleteCommand.ExecuteNonQuery();
                    }

                    // إنشاء سجل لكل يوم في الفترة
                    string insertSql = @"INSERT INTO DailyWorkStatus
                (WorkPeriodId, EmployeeCode, EmployeeName, Date, Status, CreatedDate)
                VALUES (@WorkPeriodId, @EmployeeCode, @EmployeeName, @Date, @Status, @CreatedDate)";

                    for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
                    {
                        using (var insertCommand = new SqlCommand(insertSql, connection))
                        {
                            insertCommand.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                            insertCommand.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                            insertCommand.Parameters.AddWithValue("@EmployeeName", employeeName);
                            insertCommand.Parameters.AddWithValue("@Date", date.Date);
                            insertCommand.Parameters.AddWithValue("@Status", defaultStatus);
                            insertCommand.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                            insertCommand.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء السجلات اليومية: {ex.Message}");
                throw;
            }
        }


        /// <summary>
        /// تحديث حالة موظف في يوم محدد
        /// </summary>
        public static void UpdateDailyWorkStatus(int workPeriodId, int employeeCode, DateTime date, string status, string notes = "")
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = @"UPDATE DailyWorkStatus
                SET Status = @Status, Notes = @Notes, ModifiedDate = @ModifiedDate
                WHERE WorkPeriodId = @WorkPeriodId AND EmployeeCode = @EmployeeCode AND Date = @Date";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                        command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                        command.Parameters.AddWithValue("@Date", date.Date);
                        command.Parameters.AddWithValue("@Status", status);
                        command.Parameters.AddWithValue("@Notes", notes);
                        command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الحالة اليومية: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على التتبع اليومي لموظف في فترة عمل
        /// </summary>
        public static DataTable GetDailyWorkStatus(int workPeriodId, int employeeCode)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = @"SELECT
                Date as 'التاريخ',
                Status as 'الحالة',
                Notes as 'الملاحظات',
                ModifiedDate as 'تاريخ التعديل'
                FROM DailyWorkStatus
                WHERE WorkPeriodId = @WorkPeriodId AND EmployeeCode = @EmployeeCode
                ORDER BY Date";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                        command.Parameters.AddWithValue("@EmployeeCode", employeeCode);

                        var table = new DataTable();
                        using (var adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(table);
                        }
                        return table;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على التتبع اليومي: {ex.Message}");
                return new DataTable();
            }
        }

        /// <summary>
        /// الحصول على ملخص التتبع اليومي لموظف
        /// </summary>
        public static (int totalDays, int presentDays, int absentDays, int vacationDays, double attendanceRate)
    GetDailyWorkStatusSummary(int workPeriodId, int employeeCode)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = @"SELECT
                COUNT(*) as TotalDays,
                SUM(CASE WHEN Status = N'حضور' THEN 1 ELSE 0 END) as PresentDays,
                SUM(CASE WHEN Status = N'غياب' THEN 1 ELSE 0 END) as AbsentDays,
                SUM(CASE WHEN Status = N'إجازة' THEN 1 ELSE 0 END) as VacationDays
                FROM DailyWorkStatus
                WHERE WorkPeriodId = @WorkPeriodId AND EmployeeCode = @EmployeeCode";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                        command.Parameters.AddWithValue("@EmployeeCode", employeeCode);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                int totalDays = reader.IsDBNull(0) ? 0 : reader.GetInt32(0);
                                int presentDays = reader.IsDBNull(1) ? 0 : reader.GetInt32(1);
                                int absentDays = reader.IsDBNull(2) ? 0 : reader.GetInt32(2);
                                int vacationDays = reader.IsDBNull(3) ? 0 : reader.GetInt32(3);
                                double attendanceRate = totalDays > 0 ? (double)presentDays / totalDays * 100 : 0;

                                return (totalDays, presentDays, absentDays, vacationDays, attendanceRate);
                            }
                        }
                    }
                }
                return (0, 0, 0, 0, 0);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على ملخص التتبع اليومي: {ex.Message}");
                return (0, 0, 0, 0, 0);
            }
        }

        /// <summary>
        /// الحصول على تفاصيل التتبع اليومي لموظف (كل يوم بتاريخه وحالته)
        /// </summary>
        public static DataTable GetEmployeeDailyWorkDetails(int workPeriodId, int employeeCode)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = @"SELECT 
                        CONVERT(varchar, Date, 23) as WorkDate,
                        Status,
                        Notes
                    FROM DailyWorkStatus
                    WHERE WorkPeriodId = @WorkPeriodId AND EmployeeCode = @EmployeeCode
                    ORDER BY Date";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                        command.Parameters.AddWithValue("@EmployeeCode", employeeCode);

                        var table = new DataTable();
                        using (var adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(table);
                        }
                        return table;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على تفاصيل التتبع اليومي: {ex.Message}");
                return new DataTable();
            }
        }


        /// <summary>
        /// الحصول على موظفي فترة عمل محددة
        /// </summary>

        public static DataTable GetWorkPeriodEmployees(int workPeriodId)
        {
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();

                    // أولاً، تحقق من وجود فترة العمل
                    string checkWorkPeriodSql = @"SELECT COUNT(*) FROM WorkPeriods WHERE WorkPeriodId = @WorkPeriodId";
                    using (var checkCommand = new SqlCommand(checkWorkPeriodSql, connection))
                    {
                        checkCommand.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                        int workPeriodExists = (int)checkCommand.ExecuteScalar();
                        System.Diagnostics.Debug.WriteLine($"فترة العمل {workPeriodId} موجودة: {workPeriodExists > 0}");

                        if (workPeriodExists == 0)
                        {
                            System.Diagnostics.Debug.WriteLine($"فترة العمل {workPeriodId} غير موجودة في قاعدة البيانات");
                            return new DataTable();
                        }
                    }

                    string sql = @"SELECT e.EmployeeCode, e.Name as EmployeeName, 
                                  e.Category, e.BadgeNumber, e.PhoneNumber
                           FROM Employees e
                           INNER JOIN WorkPeriods wp ON e.EmployeeCode = wp.EmployeeCode
                           WHERE wp.WorkPeriodId = @WorkPeriodId AND e.IsActive = 1
                           ORDER BY e.Name";

                    using (var command = new SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);

                        var table = new DataTable();
                        using (var adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(table);
                        }

                        System.Diagnostics.Debug.WriteLine($"تم العثور على {table.Rows.Count} موظف لفترة العمل {workPeriodId}");

                        // إذا لم يوجد موظفين، تحقق من بيانات فترة العمل
                        if (table.Rows.Count == 0)
                        {
                            string debugSql = @"SELECT WorkPeriodId, EmployeeCode, EmployeeName, ProjectName FROM WorkPeriods WHERE WorkPeriodId = @WorkPeriodId";
                            using (var debugCommand = new SqlCommand(debugSql, connection))
                            {
                                debugCommand.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                                using (var reader = debugCommand.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        System.Diagnostics.Debug.WriteLine($"فترة العمل {workPeriodId} تحتوي على:");
                                        System.Diagnostics.Debug.WriteLine($"  كود الموظف: {reader["EmployeeCode"]}");
                                        System.Diagnostics.Debug.WriteLine($"  اسم الموظف: {reader["EmployeeName"]}");
                                        System.Diagnostics.Debug.WriteLine($"  اسم المشروع: {reader["ProjectName"]}");
                                    }
                                }
                            }
                        }

                        return table;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على موظفي فترة العمل: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                return new DataTable();
            }
        }
        // دوال إدارة حضور الدورات
        public static void AddCourseAttendance(CourseAttendance attendance)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"INSERT INTO CourseAttendance 
                    (CourseId, EmployeeName, AttendanceDate, IsPresent, Notes, RecordedBy, RecordedDate) 
                    VALUES 
                    (@CourseId, @EmployeeName, @AttendanceDate, @IsPresent, @Notes, @RecordedBy, @RecordedDate)";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@CourseId", attendance.CourseId);
                    command.Parameters.AddWithValue("@EmployeeName", attendance.EmployeeName);
                    command.Parameters.AddWithValue("@AttendanceDate", attendance.AttendanceDate);
                    command.Parameters.AddWithValue("@IsPresent", attendance.IsPresent);
                    command.Parameters.AddWithValue("@Notes", attendance.Notes ?? "");
                    command.Parameters.AddWithValue("@RecordedBy", attendance.RecordedBy);
                    command.Parameters.AddWithValue("@RecordedDate", attendance.RecordedDate);

                    command.ExecuteNonQuery();
                }
            }
        }

        public static void UpdateCourseAttendance(CourseAttendance attendance)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"UPDATE CourseAttendance 
                    SET CourseId = @CourseId, 
                        EmployeeName = @EmployeeName, 
                        AttendanceDate = @AttendanceDate, 
                        IsPresent = @IsPresent, 
                        Notes = @Notes, 
                        RecordedBy = @RecordedBy, 
                        RecordedDate = @RecordedDate
                    WHERE AttendanceId = @AttendanceId";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@AttendanceId", attendance.AttendanceId);
                    command.Parameters.AddWithValue("@CourseId", attendance.CourseId);
                    command.Parameters.AddWithValue("@EmployeeName", attendance.EmployeeName);
                    command.Parameters.AddWithValue("@AttendanceDate", attendance.AttendanceDate);
                    command.Parameters.AddWithValue("@IsPresent", attendance.IsPresent);
                    command.Parameters.AddWithValue("@Notes", attendance.Notes ?? "");
                    command.Parameters.AddWithValue("@RecordedBy", attendance.RecordedBy);
                    command.Parameters.AddWithValue("@RecordedDate", attendance.RecordedDate);

                    command.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteCourseAttendance(int attendanceId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = "DELETE FROM CourseAttendance WHERE AttendanceId = @AttendanceId";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@AttendanceId", attendanceId);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static DataTable GetCourseAttendance()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT 
                   AttendanceId,
                      CourseId,
                      EmployeeName as 'اسم الموظف',
                      AttendanceDate as 'تاريخ الحضور',
                      IsPresent as 'الحضور', 
                      CASE WHEN IsPresent = 1 THEN 'حاضر' ELSE 'غائب' END as 'نتيجة الحضور',
                      Notes as 'ملاحظات',
                      RecordedBy as 'مسجل بواسطة',
                     RecordedDate as 'تاريخ التسجيل'
                    FROM CourseAttendance 
                    ORDER BY AttendanceDate DESC";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    DataTable table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        // دوال إدارة تقييم الدورات
        public static void AddCourseEvaluation(CourseEvaluation evaluation)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"INSERT INTO CourseEvaluations 
                    (CourseId, EmployeeName, Rating, Comments, EvaluationDate) 
                    VALUES 
                    (@CourseId, @EmployeeName, @Rating, @Comments, @EvaluationDate)";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@CourseId", evaluation.CourseId);
                    command.Parameters.AddWithValue("@EmployeeName", evaluation.EmployeeName);
                    command.Parameters.AddWithValue("@Rating", evaluation.Rating);
                    command.Parameters.AddWithValue("@Comments", evaluation.Comments ?? "");
                    command.Parameters.AddWithValue("@EvaluationDate", evaluation.EvaluationDate);

                    command.ExecuteNonQuery();
                }
            }
        }

        public static void UpdateCourseEvaluation(CourseEvaluation evaluation)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"UPDATE CourseEvaluations 
                    SET CourseId = @CourseId, 
                        EmployeeName = @EmployeeName, 
                        Rating = @Rating, 
                        Comments = @Comments, 
                        EvaluationDate = @EvaluationDate
                    WHERE EvaluationId = @EvaluationId";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EvaluationId", evaluation.EvaluationId);
                    command.Parameters.AddWithValue("@CourseId", evaluation.CourseId);
                    command.Parameters.AddWithValue("@EmployeeName", evaluation.EmployeeName);
                    command.Parameters.AddWithValue("@Rating", evaluation.Rating);
                    command.Parameters.AddWithValue("@Comments", evaluation.Comments ?? "");
                    command.Parameters.AddWithValue("@EvaluationDate", evaluation.EvaluationDate);

                    command.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteCourseEvaluation(int evaluationId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = "DELETE FROM CourseEvaluations WHERE EvaluationId = @EvaluationId";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EvaluationId", evaluationId);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static DataTable GetCourseEvaluations()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT 
                    EvaluationId,
                    CourseId,
                    EmployeeName as 'اسم الموظف',
                    Rating as 'التقييم',
                    Comments as 'التعليقات',
                    EvaluationDate as 'تاريخ التقييم'
                    FROM CourseEvaluations 
                    ORDER BY EvaluationDate DESC";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    DataTable table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        // دوال إدارة إشعارات الدورات
        public static void AddCourseNotification(CourseNotification notification)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"INSERT INTO CourseNotifications 
                    (CourseId, EmployeeName, NotificationType, Message, SentDate, IsRead, Priority) 
                    VALUES 
                    (@CourseId, @EmployeeName, @NotificationType, @Message, @SentDate, @IsRead, @Priority)";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@CourseId", notification.CourseId);
                    command.Parameters.AddWithValue("@EmployeeName", notification.EmployeeName);
                    command.Parameters.AddWithValue("@NotificationType", notification.NotificationType);
                    command.Parameters.AddWithValue("@Message", notification.Message);
                    command.Parameters.AddWithValue("@SentDate", notification.SentDate);
                    command.Parameters.AddWithValue("@IsRead", notification.IsRead);
                    command.Parameters.AddWithValue("@Priority", notification.Priority);

                    command.ExecuteNonQuery();
                }
            }
        }

        public static void UpdateCourseNotification(CourseNotification notification)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"UPDATE CourseNotifications 
                    SET CourseId = @CourseId, 
                        EmployeeName = @EmployeeName, 
                        NotificationType = @NotificationType, 
                        Message = @Message, 
                        SentDate = @SentDate, 
                        IsRead = @IsRead, 
                        Priority = @Priority
                    WHERE NotificationId = @NotificationId";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@NotificationId", notification.NotificationId);
                    command.Parameters.AddWithValue("@CourseId", notification.CourseId);
                    command.Parameters.AddWithValue("@EmployeeName", notification.EmployeeName);
                    command.Parameters.AddWithValue("@NotificationType", notification.NotificationType);
                    command.Parameters.AddWithValue("@Message", notification.Message);
                    command.Parameters.AddWithValue("@SentDate", notification.SentDate);
                    command.Parameters.AddWithValue("@IsRead", notification.IsRead);
                    command.Parameters.AddWithValue("@Priority", notification.Priority);

                    command.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteCourseNotification(int notificationId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = "DELETE FROM CourseNotifications WHERE NotificationId = @NotificationId";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@NotificationId", notificationId);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static DataTable GetCourseNotifications()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT 
                    NotificationId,
                    CourseId,
                    EmployeeName as 'اسم الموظف',
                    NotificationType as 'نوع الإشعار',
                    Message as 'الرسالة',
                    SentDate as 'تاريخ الإرسال',
                    IsRead as 'مقروء',
                    CASE WHEN IsRead = 1 THEN 'مقروء' ELSE 'غير مقروء' END as 'الحالة',
                    Priority as 'الأولوية'
                    FROM CourseNotifications 
                    ORDER BY SentDate DESC";

                using (var adapter = new SqlDataAdapter(sql, connection))
                {
                    DataTable table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        // دوال إدارة تفاصيل الدورات المتقدمة
        public static void UpdateCourseDetails(Course course)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"UPDATE Courses 
                    SET Status = @Status, 
                        Location = @Location, 
                        MaxParticipants = @MaxParticipants, 
                        CurrentParticipants = @CurrentParticipants, 
                        Description = @Description, 
                        Prerequisites = @Prerequisites, 
                        CertificateIssued = @CertificateIssued, 
                        Cost = @Cost, 
                        Priority = @Priority, 
                        NotificationSent = @NotificationSent, 
                        UpdatedBy = @UpdatedBy, 
                        UpdatedDate = @UpdatedDate, 
                        IsActive = @IsActive
                    WHERE CourseId = @CourseId";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@CourseId", course.CourseId);
                    command.Parameters.AddWithValue("@Status", course.Status ?? "");
                    command.Parameters.AddWithValue("@Location", course.Location ?? "");
                    command.Parameters.AddWithValue("@MaxParticipants", course.MaxParticipants);
                    command.Parameters.AddWithValue("@CurrentParticipants", course.CurrentParticipants);
                    command.Parameters.AddWithValue("@Description", course.Description ?? "");
                    command.Parameters.AddWithValue("@Prerequisites", course.Prerequisites ?? "");
                    command.Parameters.AddWithValue("@CertificateIssued", course.CertificateIssued);
                    command.Parameters.AddWithValue("@Cost", course.Cost);
                    command.Parameters.AddWithValue("@Priority", course.Priority ?? "");
                    command.Parameters.AddWithValue("@NotificationSent", course.NotificationSent);
                    command.Parameters.AddWithValue("@UpdatedBy", course.UpdatedBy ?? "");
                    command.Parameters.AddWithValue("@UpdatedDate", course.UpdatedDate);
                    command.Parameters.AddWithValue("@IsActive", course.IsActive);

                    command.ExecuteNonQuery();
                }
            }
        }

        public static Course GetCourseDetails(int courseId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT * FROM Courses WHERE CourseId = @CourseId";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@CourseId", courseId);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Course
                            {
                                CourseId = reader.GetInt32("CourseId"),
                                EmployeeName = reader["EmployeeName"]?.ToString() ?? "",
                                CourseType = reader["CourseType"]?.ToString() ?? "",
                                CourseNumber = reader["CourseNumber"]?.ToString() ?? "",
                                Category = reader["Category"]?.ToString() ?? "",
                                StartDate = reader.GetDateTime("StartDate"),
                                EndDate = reader.GetDateTime("EndDate"),
                                DaysCount = reader.GetInt32("DaysCount"),
                                GraduationGrade = reader["GraduationGrade"]?.ToString() ?? "",
                                Status = reader["Status"]?.ToString() ?? "",
                                Location = reader["Location"]?.ToString() ?? "",
                                MaxParticipants = reader["MaxParticipants"] != DBNull.Value ? reader.GetInt32("MaxParticipants") : 0,
                                CurrentParticipants = reader["CurrentParticipants"] != DBNull.Value ? reader.GetInt32("CurrentParticipants") : 0,
                                Description = reader["Description"]?.ToString() ?? "",
                                Prerequisites = reader["Prerequisites"]?.ToString() ?? "",
                                CertificateIssued = reader["CertificateIssued"] != DBNull.Value && reader.GetBoolean("CertificateIssued"),
                                Cost = reader["Cost"] != DBNull.Value ? reader.GetDecimal("Cost") : 0,
                                Priority = reader["Priority"]?.ToString() ?? "",
                                NotificationSent = reader["NotificationSent"] != DBNull.Value && reader.GetBoolean("NotificationSent"),
                                CreatedBy = reader["CreatedBy"]?.ToString() ?? "",
                                CreatedDate = reader["CreatedDate"] != DBNull.Value ? reader.GetDateTime("CreatedDate") : DateTime.Now,
                                UpdatedBy = reader["UpdatedBy"]?.ToString() ?? "",
                                UpdatedDate = reader["UpdatedDate"] != DBNull.Value ? reader.GetDateTime("UpdatedDate") : DateTime.Now,
                                IsActive = reader["IsActive"] == DBNull.Value || reader.GetBoolean("IsActive")
                            };
                        }
                    }
                }
            }
            return null;
        }

        public static DataTable GetCourseEnrolledEmployees(int courseId)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                connection.Open();
                string sql = @"SELECT DISTINCT EmployeeName as 'اسم الموظف' 
                              FROM Courses 
                              WHERE CourseId = @CourseId";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@CourseId", courseId);
                    using (var adapter = new SqlDataAdapter(command))
                    {
                        DataTable table = new DataTable();
                        adapter.Fill(table);
                        return table;
                    }
                }
            }
        }
        #endregion
    }
}        