# نظام فلترة الأقسام - دليل المطور

## نظرة عامة

تم تطوير نظام فلترة شامل يضمن أن مدير القسم يرى فقط بيانات موظفي قسمه في جميع النماذج والعمليات في النظام. هذا النظام يحافظ على أمان البيانات ويضمن عدم تسريب معلومات الموظفين بين الأقسام المختلفة.

## المكونات الرئيسية

### 1. EmployeeDepartmentHelper.cs
الكلاس الرئيسي للفلترة ويحتوي على:

#### دوال فلترة الموظفين:
- `GetFilteredEmployees(User? currentUser, string searchTerm = "")` - فلترة قائمة الموظفين
- `GetEmployeesByDepartment(int departmentId)` - موظفي قسم معين
- `SearchEmployeesInDepartment(string searchTerm, int departmentId)` - البحث في موظفي قسم

#### دوال فلترة الحضور:
- `GetFilteredEmployeesForAttendance(User? currentUser)` - موظفين للحضور
- `GetFilteredAttendanceByDateRange(User? currentUser, DateTime startDate, DateTime endDate)` - بيانات الحضور
- `GetAttendanceByDateRangeAndDepartment(DateTime startDate, DateTime endDate, int departmentId)` - حضور قسم معين

#### دوال فلترة الإجازات:
- `GetFilteredVacations(User? currentUser)` - جميع الإجازات مفلترة
- `GetVacationsByDepartment(int departmentId)` - إجازات قسم معين

#### دوال التحقق من الصلاحيات:
- `ValidateUserAccess(User? currentUser, int employeeCode)` - التحقق من صلاحية الوصول
- `IsDepartmentManager(User? currentUser)` - التحقق من كون المستخدم مدير قسم
- `IsGeneralManager(User? currentUser)` - التحقق من كون المستخدم مدير عام

### 2. DepartmentFilterManager.cs
مدير الفلترة المركزي ويوفر:

#### دوال التطبيق العامة:
- `ApplyDepartmentFilter(Form form, User? currentUser, DataGridView dataGridView, DataTable dataSource)` - تطبيق فلترة شاملة
- `FilterDataByDepartment(User currentUser, DataTable originalData)` - فلترة البيانات

#### دوال التحقق من الصلاحيات:
- `CanAddRecord(User? currentUser, int? employeeCode = null)` - صلاحية الإضافة
- `CanEditRecord(User? currentUser, int employeeCode)` - صلاحية التعديل
- `CanDeleteRecord(User? currentUser, int employeeCode)` - صلاحية الحذف

## النماذج المُحدثة

### ✅ تم التحديث بالكامل:

#### 1. Form1.cs (إدارة الموظفين)
- إضافة Constructor يستقبل المستخدم الحالي
- تطبيق فلترة على تحميل البيانات
- تطبيق فلترة على البحث
- تحديث عنوان النافذة
- عرض رسائل تحذيرية

#### 2. AttendanceForm.cs (الحضور والغياب)
- إضافة Constructor يستقبل المستخدم الحالي
- فلترة قائمة الموظفين
- فلترة بيانات الحضور
- فلترة البحث
- التحقق من صلاحية الوصول

#### 3. VacationForm.cs (الإجازات)
- إضافة Constructor يستقبل المستخدم الحالي
- فلترة قائمة الموظفين
- فلترة بيانات الإجازات
- تحديث عنوان النافذة

#### 4. DocumentManagementForm.cs (إدارة المستندات)
- إضافة Constructor يستقبل المستخدم الحالي
- التحقق من صلاحية الوصول عند فتح النموذج
- التحقق من صلاحية الإضافة والحذف
- منع الوصول غير المخول

#### 5. UserForm.cs (إدارة المستخدمين)
- كان مُحدث مسبقاً
- يطبق فلترة على المستخدمين حسب القسم

#### 6. MainForm.cs (النموذج الرئيسي)
- تحديث استدعاء النماذج لتمرير المستخدم الحالي
- إدارة صلاحيات الوصول للأزرار

## كيفية الاستخدام

### 1. إعداد قاعدة البيانات

#### جدول Users:
```sql
-- التأكد من وجود عمود DepartmentId
ALTER TABLE Users ADD DepartmentId INT NULL;
ALTER TABLE Users ADD IsActive BIT DEFAULT 1;

-- ربط المستخدمين بالأقسام
UPDATE Users SET DepartmentId = 1 WHERE UserId IN (1, 2, 3); -- قسم 1
UPDATE Users SET DepartmentId = 2 WHERE UserId IN (4, 5, 6); -- قسم 2
```

#### جدول Departments:
```sql
-- التأكد من وجود عمود ManagerUserId
ALTER TABLE Departments ADD ManagerUserId INT NULL;

-- تعيين مدراء الأقسام
UPDATE Departments SET ManagerUserId = 2 WHERE DepartmentId = 1; -- المستخدم 2 مدير القسم 1
UPDATE Departments SET ManagerUserId = 5 WHERE DepartmentId = 2; -- المستخدم 5 مدير القسم 2
```

#### جدول Employees:
```sql
-- التأكد من وجود عمود DepartmentId
ALTER TABLE Employees ADD DepartmentId INT NULL;

-- ربط الموظفين بالأقسام (عبر جدول Users)
-- يتم الربط تلقائياً عبر EmployeeCode = UserId
```

### 2. إعداد أنواع المستخدمين

#### في جدول Users:
- `UserType = "مدير"` - مدير عام (يرى جميع البيانات)
- `UserType = "مدير القسم"` أو `"مدير قسم"` - مدير قسم (يرى بيانات قسمه فقط)
- `UserType = "مستخدم"` - مستخدم عادي (يرى جميع البيانات حالياً)

### 3. تطبيق الفلترة على نموذج جديد

```csharp
public partial class YourNewForm : Form
{
    private User? currentUser = null;

    // Constructor أساسي
    public YourNewForm()
    {
        InitializeComponent();
    }

    // Constructor يستقبل المستخدم الحالي
    public YourNewForm(User currentUser) : this()
    {
        this.currentUser = currentUser;
        
        // عرض رسالة تحذيرية
        if (EmployeeDepartmentHelper.IsDepartmentManager(currentUser))
        {
            string warningMessage = EmployeeDepartmentHelper.GetFilterWarningMessage(currentUser);
            MessageBox.Show(warningMessage, "معلومات الفلترة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }

    private void LoadData()
    {
        // تطبيق الفلترة على البيانات
        var data = EmployeeDepartmentHelper.GetFilteredEmployees(currentUser);
        dataGridView.DataSource = data;
        
        // تحديث عنوان النافذة
        string title = EmployeeDepartmentHelper.GetWindowTitle(currentUser, data.Rows.Count);
        this.Text = title.Replace("نظام إدارة الموظفين", "اسم نموذجك");
    }

    private void AddRecord()
    {
        if (!DepartmentFilterManager.CanAddRecord(currentUser))
        {
            MessageBox.Show("ليس لديك صلاحية لإضافة سجلات", "غير مخول", 
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }
        // باقي كود الإضافة...
    }
}
```

### 4. تحديث MainForm

```csharp
private void OpenYourNewForm()
{
    // تمرير المستخدم الحالي
    OpenForm(new YourNewForm(CurrentUser));
}
```

## اختبار النظام

### 1. إنشاء بيانات اختبار:

```sql
-- إنشاء أقسام
INSERT INTO Departments (DepartmentName, DepartmentCode, IsActive, CreatedDate, CreatedBy) 
VALUES ('قسم المبيعات', 'SALES', 1, GETDATE(), 1);

INSERT INTO Departments (DepartmentName, DepartmentCode, IsActive, CreatedDate, CreatedBy) 
VALUES ('قسم المحاسبة', 'ACCOUNTING', 1, GETDATE(), 1);

-- إنشاء مستخدمين
INSERT INTO Users (Username, Password, FullName, UserType, DepartmentId) 
VALUES ('admin', 'admin123', 'المدير العام', 'مدير', NULL);

INSERT INTO Users (Username, Password, FullName, UserType, DepartmentId) 
VALUES ('sales_manager', 'pass123', 'مدير المبيعات', 'مدير القسم', 1);

INSERT INTO Users (Username, Password, FullName, UserType, DepartmentId) 
VALUES ('acc_manager', 'pass123', 'مدير المحاسبة', 'مدير القسم', 2);

-- تعيين مدراء الأقسام
UPDATE Departments SET ManagerUserId = 2 WHERE DepartmentId = 1;
UPDATE Departments SET ManagerUserId = 3 WHERE DepartmentId = 2;
```

### 2. سيناريوهات الاختبار:

#### اختبار المدير العام:
1. تسجيل دخول بـ admin
2. فتح أي نموذج
3. يجب رؤية جميع البيانات
4. يجب إمكانية الإضافة والتعديل والحذف لجميع السجلات

#### اختبار مدير القسم:
1. تسجيل دخول بـ sales_manager
2. فتح نموذج الموظفين
3. يجب رؤية موظفي قسم المبيعات فقط
4. يجب عرض رسالة تحذيرية حول الفلترة
5. يجب منع الوصول لموظفي الأقسام الأخرى

#### اختبار الصلاحيات:
1. محاولة إضافة سجل لموظف من قسم آخر
2. محاولة تعديل بيانات موظف من قسم آخر
3. محاولة حذف سجل لموظف من قسم آخر
4. يجب منع جميع العمليات السابقة

## استكشاف الأخطاء

### مشكلة: مدير القسم لا يرى أي بيانات
**الأسباب المحتملة:**
1. لم يتم تعيين ManagerUserId في جدول Departments
2. لم يتم ربط الموظفين بالقسم في جدول Users
3. IsActive = 0 للمستخدمين

**الحل:**
```sql
-- التحقق من تعيين مدير القسم
SELECT * FROM Departments WHERE ManagerUserId = [UserId];

-- التحقق من ربط الموظفين
SELECT * FROM Users WHERE DepartmentId = [DepartmentId] AND IsActive = 1;

-- التحقق من ربط الموظفين بالمستخدمين
SELECT e.*, u.DepartmentId FROM Employees e 
INNER JOIN Users u ON e.EmployeeCode = u.UserId 
WHERE u.DepartmentId = [DepartmentId];
```

### مشكلة: رسائل خطأ عند تحميل البيانات
**الحل:**
1. التحقق من وجود الأعمدة المطلوبة
2. التحقق من صحة أسماء الأعمدة
3. التحقق من أنواع البيانات

## الملفات المرجعية

- `DepartmentFilterGuide.md` - دليل تفصيلي لتطبيق الفلترة
- `EmployeeDepartmentHelper.cs` - الكلاس الرئيسي للفلترة
- `DepartmentFilterManager.cs` - مدير الفلترة المركزي

## المساهمة

عند إضافة نماذج جديدة أو تحديث موجودة، يرجى:
1. اتباع النمط المحدد في الدليل
2. إضافة Constructor يستقبل المستخدم الحالي
3. تطبيق الفلترة على جميع عمليات تحميل البيانات
4. التحقق من الصلاحيات قبل الإضافة/التعديل/الحذف
5. تحديث MainForm لتمرير المستخدم الحالي
