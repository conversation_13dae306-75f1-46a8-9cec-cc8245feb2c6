using System;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class SimpleDailyTrackingForm : Form
    {
        private int workPeriodId;
        private DateTime startDate;
        private DateTime endDate;
        private string projectName;

        public SimpleDailyTrackingForm()
        {
            InitializeComponent();

        }

        public SimpleDailyTrackingForm(int workPeriodId, DateTime startDate, DateTime endDate, string projectName)
        {
            this.workPeriodId = workPeriodId;
            this.startDate = startDate;
            this.endDate = endDate;
            this.projectName = projectName;

            InitializeComponent();
            SetupForm();
            LoadEmployeesData();
            ThemeManager.ApplyThemeToForm(this);
        }

        private void SetupForm()
        {
            this.Text = "التتبع اليومي لفترة العمل";
            lblTitle.Text = $"التتبع اليومي - {projectName} ({startDate:dd/MM/yyyy} - {endDate:dd/MM/yyyy})";
        }

        private DataTable GetEmployeeFromWorkPeriodDirectly(int workPeriodId)
        {
            try
            {
                using (var connection = new System.Data.SqlClient.SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    
                    string sql = @"SELECT wp.EmployeeCode, 
                                         COALESCE(e.Name, wp.EmployeeName) as EmployeeName,
                                         COALESCE(e.Category, '') as Category,
                                         COALESCE(e.KeyCardNumber, '') as KeyCardNumber,
                                         COALESCE(e.PhoneNumber, '') as PhoneNumber
                                  FROM WorkPeriods wp
                                  LEFT JOIN Employees e ON wp.EmployeeCode = e.EmployeeCode
                                  WHERE wp.WorkPeriodId = @WorkPeriodId";

                    using (var command = new System.Data.SqlClient.SqlCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);

                        var table = new DataTable();
                        using (var adapter = new System.Data.SqlClient.SqlDataAdapter(command))
                        {
                            adapter.Fill(table);
                        }

                        System.Diagnostics.Debug.WriteLine($"الطريقة البديلة: تم العثور على {table.Rows.Count} موظف لفترة العمل {workPeriodId}");
                        return table;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الطريقة البديلة: {ex.Message}");
                return new DataTable();
            }
        }

        private void LoadEmployeesData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"تحميل بيانات الموظفين لفترة العمل: {workPeriodId}");
                
                // الحصول على موظفي فترة العمل
                var employeesData = DatabaseHelper.GetWorkPeriodEmployees(workPeriodId);

                System.Diagnostics.Debug.WriteLine($"تم جلب {employeesData?.Rows.Count ?? 0} سجل من قاعدة البيانات");

                if (employeesData == null || employeesData.Rows.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("لا توجد بيانات موظفين من الطريقة الأولى، محاولة الطريقة البديلة...");
                    
                    // محاولة الحصول على بيانات الموظف من فترة العمل مباشرة
                    employeesData = GetEmployeeFromWorkPeriodDirectly(workPeriodId);
                    
                    if (employeesData == null || employeesData.Rows.Count == 0)
                    {
                        System.Diagnostics.Debug.WriteLine("لا توجد بيانات موظفين لهذه الفترة حتى من الطريقة البديلة");
                        
                        string detailedMessage = $"لا توجد بيانات موظفين لفترة العمل رقم {workPeriodId}\n\n" +
                                               $"تفاصيل الفترة:\n" +
                                               $"- اسم المشروع: {projectName}\n" +
                                               $"- تاريخ البداية: {startDate:dd/MM/yyyy}\n" +
                                               $"- تاريخ النهاية: {endDate:dd/MM/yyyy}\n\n" +
                                               $"الأسباب المحتملة:\n" +
                                               $"1. لم يتم ربط أي موظف بهذه الفترة\n" +
                                               $"2. الموظف المرتبط غير نشط\n" +
                                               $"3. مشكلة في بنية قاعدة البيانات\n\n" +
                                               $"يرجى التحقق من إعدادات فترة العمل.";
                        
                        MessageBox.Show(detailedMessage, "لا توجد بيانات موظفين", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"نجحت الطريقة البديلة في جلب {employeesData.Rows.Count} موظف");
                    }
                }

                // إنشاء جدول للعرض
                var displayTable = new DataTable();
                displayTable.Columns.Add("كود الموظف", typeof(int));
                displayTable.Columns.Add("اسم الموظف", typeof(string));
                displayTable.Columns.Add("الفئة", typeof(string));
                displayTable.Columns.Add("رقم الكي كارت", typeof(string));
                displayTable.Columns.Add("رقم الهاتف", typeof(string));
                displayTable.Columns.Add("إجمالي الأيام", typeof(int));
                displayTable.Columns.Add("أيام الحضور", typeof(int));
                displayTable.Columns.Add("أيام الغياب", typeof(int));
                displayTable.Columns.Add("أيام الإجازة", typeof(int));
                displayTable.Columns.Add("معدل الحضور %", typeof(string));

                foreach (DataRow empRow in employeesData.Rows)
                {
                    int employeeCode = Convert.ToInt32(empRow["EmployeeCode"]);
                    string employeeName = empRow["EmployeeName"].ToString();
                    string category = empRow["Category"]?.ToString() ?? "";
                    string KeyCardNumber = empRow["KeyCardNumber"]?.ToString() ?? "";
                    string phoneNumber = empRow["PhoneNumber"]?.ToString() ?? "";

                    // الحصول على ملخص التتبع اليومي
                    var (totalDays, presentDays, absentDays, vacationDays, attendanceRate) =
                        DatabaseHelper.GetDailyWorkStatusSummary(workPeriodId, employeeCode);

                    var newRow = displayTable.NewRow();
                    newRow["كود الموظف"] = employeeCode;
                    newRow["اسم الموظف"] = employeeName;
                    newRow["الفئة"] = category;
                    newRow["رقم الكي كارت"] = KeyCardNumber;
                    newRow["رقم الهاتف"] = phoneNumber;
                    newRow["إجمالي الأيام"] = totalDays;
                    newRow["أيام الحضور"] = presentDays;
                    newRow["أيام الغياب"] = absentDays;
                    newRow["أيام الإجازة"] = vacationDays;
                    newRow["معدل الحضور %"] = $"{attendanceRate:F1}%";

                    displayTable.Rows.Add(newRow);
                }

                dataGridViewEmployees.DataSource = displayTable;
                
                // تحديث عنوان المجموعة ليشمل عدد الموظفين
                groupBoxEmployees.Text = $"قائمة الموظفين والتتبع اليومي (إجمالي: {displayTable.Rows.Count} موظف)";

                // تنسيق الأعمدة
                foreach (DataGridViewColumn column in dataGridViewEmployees.Columns)
                {
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    column.HeaderCell.Style.Font = new Font("Cairo", 9F, FontStyle.Bold);
                    
                    // تعديل عرض الأعمدة حسب المحتوى
                    switch (column.HeaderText)
                    {
                        case "كود الموظف":
                            column.Width = 80;
                            break;
                        case "اسم الموظف":
                            column.Width = 150;
                            column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                            break;
                        case "الفئة":
                            column.Width = 100;
                            break;
                        case "رقم الكي كارت":
                            column.Width = 100;
                            break;
                        case "رقم الهاتف":
                            column.Width = 120;
                            break;
                        case "إجمالي الأيام":
                        case "أيام الحضور":
                        case "أيام الغياب":
                        case "أيام الإجازة":
                            column.Width = 90;
                            break;
                        case "معدل الحضور %":
                            column.Width = 100;
                            break;
                    }
                }

                // تلوين الصفوف بناءً على معدل الحضور
                foreach (DataGridViewRow row in dataGridViewEmployees.Rows)
                {
                    if (row.Cells["معدل الحضور %"].Value != null)
                    {
                        string attendanceRateStr = row.Cells["معدل الحضور %"].Value.ToString();
                        if (double.TryParse(attendanceRateStr.Replace("%", ""), out double rate))
                        {
                            if (rate >= 90)
                            {
                                row.DefaultCellStyle.BackColor = Color.LightGreen;
                            }
                            else if (rate >= 70)
                            {
                                row.DefaultCellStyle.BackColor = Color.LightYellow;
                            }
                            else if (rate >= 50)
                            {
                                row.DefaultCellStyle.BackColor = Color.LightCoral;
                            }
                            else
                            {
                                row.DefaultCellStyle.BackColor = Color.MistyRose;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEditStatus_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewEmployees.SelectedRows.Count == 0)
                {
                    MessageBox.Show("الرجاء اختيار موظف لتعديل حالته", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedRow = dataGridViewEmployees.SelectedRows[0];
                int employeeCode = Convert.ToInt32(selectedRow.Cells["كود الموظف"].Value);
                string employeeName = selectedRow.Cells["اسم الموظف"].Value.ToString();

                // فتح نموذج تعديل الحالة اليومية
                var editForm = new SimpleEmployeeStatusFormV2(workPeriodId, employeeCode, employeeName, startDate, endDate);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    // تحديث البيانات
                    LoadEmployeesData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تعديل الحالة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void BtnViewDetails_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewEmployees.SelectedRows.Count == 0)
                {
                    MessageBox.Show("الرجاء اختيار موظف لعرض تفاصيل حضوره اليومي", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedRow = dataGridViewEmployees.SelectedRows[0];
                int employeeCode = Convert.ToInt32(selectedRow.Cells["كود الموظف"].Value);
                string employeeName = selectedRow.Cells["اسم الموظف"].Value.ToString();

                // فتح نموذج عرض التفاصيل اليومية
                System.Diagnostics.Debug.WriteLine($"فتح تفاصيل الموظف: WorkPeriodId={workPeriodId}, EmployeeCode={employeeCode}, Name={employeeName}");
                
                // فتح نموذج عرض التفاصيل اليومية
                var detailsForm = new DailyDetailsForm(workPeriodId, employeeCode, employeeName, startDate, endDate);
                detailsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تفاصيل الحضور: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnPrintReport_Click(object sender, EventArgs e)
        {
            try
            {
                string html = GenerateHTMLReport();
                string tempFile = System.IO.Path.Combine(System.IO.Path.GetTempPath(), $"daily_tracking_report_{DateTime.Now:yyyyMMdd_HHmmss}.html");
                System.IO.File.WriteAllText(tempFile, html, System.Text.Encoding.UTF8);
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempFile,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GenerateHTMLReport()
        {
            // جلب اسم المؤسسة من الإعدادات
            string companyName = "اسم المؤسسة";
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT TOP 1 CompanyName FROM Settings";
                    using (var command = new SqlCommand(sql, connection))
                    {
                        var result = command.ExecuteScalar();
                        if (result != null && result != DBNull.Value)
                            companyName = result.ToString();
                    }
                }
            }
            catch
            {
                // تركنا catch فارغ للتجاهل كما في الكود الأصلي
            }


            // بناء جدول التقرير من dataGridViewEmployees
            var html = new System.Text.StringBuilder();
            html.Append(@"<!DOCTYPE html>");
            html.Append("<html dir='rtl' lang='ar'><head><meta charset='UTF-8'><title>تقرير التتبع اليومي</title>");
            html.Append("<style>");
            html.Append(@"@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');
                body { font-family: 'Cairo', sans-serif; margin: 40px; direction: rtl; background-color: #f5f5f5; color: #333; }
                .header { text-align: center; margin-bottom: 30px; padding: 20px; background: #fff; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
                .org-name { font-size: 20px; font-weight: bold; color: #45678a; margin-bottom: 10px; }
                .report-title { font-size: 17px; color: #666; margin-bottom: 20px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 13px; }
                th, td { padding: 8px; text-align: center; border: 1px solid #ddd; font-size: 13px; }
                th { background-color: #45678a; color: white; font-weight: bold; font-size: 13px; }
                tr:nth-child(even) { background-color: #f8f9fa; }
                tr:hover { background-color: #e9ecef; }
                .present { color: #388e3c; font-weight: bold; background: #e8f5e9; }
                .absent { color: #d32f2f; font-weight: bold; }
                .vacation { color: #00897b; font-weight: bold; background: #e3f6fd; }
                .footer { margin-top: 30px; text-align: center; color: #666; font-size: 14px; }
                @media print { body { margin: 0; background: white; } .header { box-shadow: none; border: 1px solid #ddd; } }");
            html.Append("</style></head><body>");
            html.Append($"<div class='header'><div class='org-name'>{companyName}</div><div class='report-title'>تقرير التتبع اليومي الشامل لفترة العمل</div></div>");
            html.Append($"<div style='margin-bottom:10px;font-size:15px;'><b>مكان العمل:</b> {projectName}</div>");
            html.Append($"<div style='margin-bottom:20px;font-size:15px;'><b>الفترة:</b> {startDate:dd/MM/yyyy} - {endDate:dd/MM/yyyy}</div>");
            html.Append("<table><thead><tr>");
            foreach (DataGridViewColumn col in dataGridViewEmployees.Columns)
                html.Append($"<th>{col.HeaderText}</th>");
            html.Append("</tr></thead><tbody>");
            foreach (DataGridViewRow row in dataGridViewEmployees.Rows)
            {
                if (row.IsNewRow) continue;
                html.Append("<tr>");
                for (int i = 0; i < dataGridViewEmployees.Columns.Count; i++)
                {
                    string val = row.Cells[i].Value?.ToString() ?? "";
                    string cssClass = "";
                    if (dataGridViewEmployees.Columns[i].HeaderText.Contains("حضور")) cssClass = "present";
                    else if (dataGridViewEmployees.Columns[i].HeaderText.Contains("إجازة")) cssClass = "vacation";
                    else if (dataGridViewEmployees.Columns[i].HeaderText.Contains("غياب")) cssClass = "absent";
                    html.Append($"<td class='{cssClass}'>{val}</td>");
                }
                html.Append("</tr>");
            }
            html.Append("</tbody></table>");

            // إضافة التفاصيل اليومية لكل موظف
            html.Append("<div style='margin-top:30px;font-size:16px;font-weight:bold;color:#45678a;'>التفاصيل اليومية لكل موظف</div>");
            
            foreach (DataGridViewRow row in dataGridViewEmployees.Rows)
            {
                if (row.IsNewRow) continue;
                
                int employeeCode = Convert.ToInt32(row.Cells["كود الموظف"].Value);
                string employeeName = row.Cells["اسم الموظف"].Value.ToString();
                
                // الحصول على التفاصيل اليومية للموظف
                var detailsTable = DatabaseHelper.GetEmployeeDailyWorkDetails(workPeriodId, employeeCode);
                
                if (detailsTable.Rows.Count > 0)
                {
                    html.Append($"<div style='margin-top:25px;padding:15px;background:#fff;border:1px solid #ddd;border-radius:5px;'>");
                    html.Append($"<div style='font-size:14px;font-weight:bold;color:#333;margin-bottom:10px;'>الموظف: {employeeName} (كود: {employeeCode})</div>");
                    html.Append("<table style='font-size:12px;'><thead><tr>");
                    html.Append("<th>التاريخ</th><th>الحالة</th><th>الملاحظات</th>");
                    html.Append("</tr></thead><tbody>");
                    
                    foreach (DataRow detailRow in detailsTable.Rows)
                    {
                        string workDate = detailRow["WorkDate"].ToString();
                        string status = detailRow["Status"].ToString();
                        string notes = detailRow["Notes"]?.ToString() ?? "";
                        
                        string cssClass = "";
                        if (status == "حضور") cssClass = "present";
                        else if (status == "غياب") cssClass = "absent";
                        else if (status == "إجازة") cssClass = "vacation";
                        
                        html.Append($"<tr>");
                        html.Append($"<td>{workDate}</td>");
                        html.Append($"<td class='{cssClass}'>{status}</td>");
                        html.Append($"<td>{notes}</td>");
                        html.Append("</tr>");
                    }
                    html.Append("</tbody></table>");
                    html.Append("</div>");
                }
            }
            
            html.Append($"<div class='footer'>تم إنشاء هذا التقرير في {DateTime.Now:dd/MM/yyyy HH:mm} | جميع الحقوق محفوظة &copy; {companyName}</div>");
            html.Append("</body></html>");
            return html.ToString();
        }
    }
}
