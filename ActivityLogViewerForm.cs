using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class ActivityLogViewerForm : Form
    {
        private User currentUser;
        private DataTable? currentData;

        public ActivityLogViewerForm(User user)
        {
            InitializeComponent();
            this.currentUser = user;
            SetupInitialValues();
            string theme = "default";
            try
            {
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT TOP 1 Theme FROM Settings";
                    using (var command = new SqlCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            theme = reader["Theme"]?.ToString() ?? "default";
                        }
                    }
                }
            }
            catch
            {
                theme = "default";
            }

            ThemeManager.ApplyThemeToForm(this);
        }

        private void SetupInitialValues()
        {
            // تعيين القيم الافتراضية
            dtpFrom.Value = DateTime.Now.AddDays(-7);
            dtpTo.Value = DateTime.Now;
            cmbActionType.SelectedIndex = 0; // الكل
            cmbPriority.SelectedIndex = 0; // الكل
            cmbStatus.SelectedIndex = 0; // الكل

            // إعداد أحداث البحث
            txtSearch.TextChanged += (s, e) => FilterData();
            cmbActionType.SelectedIndexChanged += (s, e) => FilterData();
            cmbPriority.SelectedIndexChanged += (s, e) => FilterData();
            cmbStatus.SelectedIndexChanged += (s, e) => FilterData();
            dtpFrom.ValueChanged += (s, e) => FilterData();
            dtpTo.ValueChanged += (s, e) => FilterData();

            // إعداد DataGridView
            SetupDataGridView();
        }

        private void SetupDataGridView()
        {
            dgvLogs.AutoGenerateColumns = false;
            dgvLogs.AllowUserToAddRows = false;
            dgvLogs.AllowUserToDeleteRows = false;
            dgvLogs.ReadOnly = true;
            dgvLogs.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvLogs.MultiSelect = true;
            dgvLogs.RowHeadersVisible = false;
            dgvLogs.BackgroundColor = Color.White;
            dgvLogs.BorderStyle = BorderStyle.None;
            dgvLogs.GridColor = Color.FromArgb(224, 224, 224);

            // إضافة الأعمدة
            dgvLogs.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "LogId",
                HeaderText = "معرف السجل",
                Name = "LogId",
                Width = 80,
                Visible = false
            });

            dgvLogs.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "ActionDate",
                HeaderText = "التاريخ والوقت",
                Name = "ActionDate",
                Width = 140,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy-MM-dd HH:mm:ss" }
            });

            dgvLogs.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "FullName",
                HeaderText = "المستخدم",
                Name = "FullName",
                Width = 120
            });

            dgvLogs.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Action",
                HeaderText = "العملية",
                Name = "Action",
                Width = 200
            });

            dgvLogs.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "TableName",
                HeaderText = "الجدول",
                Name = "TableName",
                Width = 100
            });

            dgvLogs.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "ActionType",
                HeaderText = "نوع العملية",
                Name = "ActionType",
                Width = 80
            });

            dgvLogs.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Priority",
                HeaderText = "الأولوية",
                Name = "Priority",
                Width = 70
            });

            dgvLogs.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "Status",
                HeaderText = "الحالة",
                Name = "Status",
                Width = 60
            });

            dgvLogs.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "IpAddress",
                HeaderText = "عنوان IP",
                Name = "IpAddress",
                Width = 100
            });

            dgvLogs.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "ComputerName",
                HeaderText = "اسم الجهاز",
                Name = "ComputerName",
                Width = 120
            });

            // إعداد تلوين الصفوف
            dgvLogs.CellFormatting += DgvLogs_CellFormatting;
        }

        private void DgvLogs_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (dgvLogs.Columns[e.ColumnIndex].Name == "Priority")
            {
                switch (e.Value?.ToString())
                {
                    case "حرج":
                        e.CellStyle.BackColor = Color.FromArgb(255, 235, 238);
                        e.CellStyle.ForeColor = Color.Red;
                        break;
                    case "مهم":
                        e.CellStyle.BackColor = Color.FromArgb(255, 248, 225);
                        e.CellStyle.ForeColor = Color.Orange;
                        break;
                    case "عادي":
                        e.CellStyle.BackColor = Color.FromArgb(232, 245, 233);
                        e.CellStyle.ForeColor = Color.Green;
                        break;
                }
            }
            else if (dgvLogs.Columns[e.ColumnIndex].Name == "Status")
            {
                switch (e.Value?.ToString())
                {
                    case "فشل":
                        e.CellStyle.ForeColor = Color.Red;
                        break;
                    case "نجح":
                        e.CellStyle.ForeColor = Color.Green;
                        break;
                    case "تحذير":
                        e.CellStyle.ForeColor = Color.Orange;
                        break;
                }
            }
        }

        private async void ActivityLogViewerForm_Load(object sender, EventArgs e)
        {
            await LoadDataAsync();
        }

        private async System.Threading.Tasks.Task LoadDataAsync()
        {
            try
            {
                var logs = await ActivityLogService.GetAllActivityAsync(
                    dtpFrom.Value.Date,
                    dtpTo.Value.Date.AddDays(1).AddSeconds(-1),
                    null,
                    null
                );

                currentData = ConvertToDataTable(logs);
                FilterData();
                UpdateStatusLabels();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private DataTable ConvertToDataTable(System.Collections.Generic.List<ActivityLog> logs)
        {
            var table = new DataTable();
            table.Columns.Add("LogId", typeof(int));
            table.Columns.Add("ActionDate", typeof(DateTime));
            table.Columns.Add("FullName", typeof(string));
            table.Columns.Add("Action", typeof(string));
            table.Columns.Add("TableName", typeof(string));
            table.Columns.Add("ActionType", typeof(string));
            table.Columns.Add("Priority", typeof(string));
            table.Columns.Add("Status", typeof(string));
            table.Columns.Add("IpAddress", typeof(string));
            table.Columns.Add("ComputerName", typeof(string));

            foreach (var log in logs)
            {
                table.Rows.Add(
                    log.LogId,
                    log.ActionDate,
                    log.FullName,
                    log.Action,
                    log.TableName,
                    log.ActionType,
                    log.Priority,
                    log.Status,
                    log.IpAddress,
                    log.ComputerName
                );
            }

            return table;
        }

        private void FilterData()
        {
            if (currentData == null) return;

            string filter = "1=1";

            // فلترة حسب نوع العملية
            if (cmbActionType.SelectedItem?.ToString() != "الكل")
            {
                filter += $" AND ActionType = '{cmbActionType.SelectedItem}'";
            }

            // فلترة حسب الأولوية
            if (cmbPriority.SelectedItem?.ToString() != "الكل")
            {
                filter += $" AND Priority = '{cmbPriority.SelectedItem}'";
            }

            // فلترة حسب الحالة
            if (cmbStatus.SelectedItem?.ToString() != "الكل")
            {
                filter += $" AND Status = '{cmbStatus.SelectedItem}'";
            }

            // فلترة حسب النص
            if (!string.IsNullOrEmpty(txtSearch.Text))
            {
                filter += $" AND (Action LIKE '%{txtSearch.Text}%' OR FullName LIKE '%{txtSearch.Text}%' OR TableName LIKE '%{txtSearch.Text}%')";
            }

            // فلترة حسب التاريخ
            filter += $" AND ActionDate >= #{dtpFrom.Value:yyyy-MM-dd}# AND ActionDate <= #{dtpTo.Value.AddDays(1):yyyy-MM-dd}#";

            try
            {
                var filteredRows = currentData.Select(filter);
                var filteredTable = currentData.Clone();
                foreach (var row in filteredRows)
                {
                    filteredTable.ImportRow(row);
                }

                dgvLogs.DataSource = filteredTable;
                UpdateStatusLabels();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الفلترة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void UpdateStatusLabels()
        {
            var totalRecords = currentData?.Rows.Count ?? 0;
            var filteredRecords = dgvLogs.Rows.Count;

            lblTotalRecords.Text = $"إجمالي السجلات: {totalRecords:N0}";
            lblFilteredRecords.Text = $"السجلات المفلترة: {filteredRecords:N0}";
        }

        private async void BtnRefresh_Click(object sender, EventArgs e)
        {
            await LoadDataAsync();
        }

        private void BtnClear_Click(object sender, EventArgs e)
        {
            dtpFrom.Value = DateTime.Now.AddDays(-7);
            dtpTo.Value = DateTime.Now;
            cmbActionType.SelectedIndex = 0;
            cmbPriority.SelectedIndex = 0;
            cmbStatus.SelectedIndex = 0;
            txtSearch.Clear();
            FilterData();
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            if (dgvLogs.Rows.Count == 0)
            {
                MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            using (var dialog = new SaveFileDialog())
            {
                dialog.Filter = "CSV Files (*.csv)|*.csv";
                dialog.Title = "تصدير سجلات الأنشطة";
                dialog.FileName = $"ActivityLogs_{DateTime.Now:yyyyMMdd_HHmmss}.csv";

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        ExportToCsv(dialog.FileName);
                        MessageBox.Show("تم تصدير البيانات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        
                        // تسجيل عملية التصدير
                        _ = ActivityLogHelper.LogExportOperationAsync(
                            "سجلات الأنشطة",
                            "ActivityLogs",
                            dgvLogs.Rows.Count,
                            dialog.FileName
                        );
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void ExportToCsv(string fileName)
        {
            var csv = new StringBuilder();
            
            // إضافة العناوين
            var headers = dgvLogs.Columns.Cast<DataGridViewColumn>()
                .Where(c => c.Visible)
                .Select(c => c.HeaderText);
            csv.AppendLine(string.Join(",", headers));

            // إضافة البيانات
            foreach (DataGridViewRow row in dgvLogs.Rows)
            {
                var values = dgvLogs.Columns.Cast<DataGridViewColumn>()
                    .Where(c => c.Visible)
                    .Select(c => row.Cells[c.Index].Value?.ToString() ?? "");
                csv.AppendLine(string.Join(",", values));
            }

            File.WriteAllText(fileName, csv.ToString(), Encoding.UTF8);
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void DgvLogs_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                try
                {
                    var logId = Convert.ToInt32(dgvLogs.Rows[e.RowIndex].Cells["LogId"].Value);
                    var selectedLog = ActivityLogService.GetActivityByIdAsync(logId).Result;
                    
                    if (selectedLog != null)
                    {
                        var detailsForm = new ActivityLogDetailsForm(selectedLog);
                        detailsForm.ShowDialog();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح التفاصيل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        // دالة حذف السجل المحدد
        private async void BtnDeleteSelected_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvLogs.SelectedRows.Count == 0)
                {
                    MessageBox.Show("الرجاء تحديد سجل للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف {dgvLogs.SelectedRows.Count} سجل محدد؟\n\nتحذير: هذه العملية لا يمكن التراجع عنها!",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var logIds = new List<int>();
                    foreach (DataGridViewRow row in dgvLogs.SelectedRows)
                    {
                        if (row.Cells["LogId"].Value != null)
                        {
                            logIds.Add(Convert.ToInt32(row.Cells["LogId"].Value));
                        }
                    }

                    if (logIds.Count > 0)
                    {
                        await DeleteActivityLogs(logIds);
                        MessageBox.Show($"تم حذف {logIds.Count} سجل بنجاح", "نجح الحذف", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        BtnRefresh_Click(sender, e); // تحديث البيانات
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف السجلات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // دالة حذف جميع السجلات
        private async void BtnDeleteAll_Click(object sender, EventArgs e)
        {
            try
            {
                var totalRecords = dgvLogs.Rows.Count;
                if (totalRecords == 0)
                {
                    MessageBox.Show("لا توجد سجلات للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف جميع السجلات ({totalRecords} سجل)؟\n\n⚠️ تحذير: هذه العملية ستحذف جميع سجلات الأنشطة ولا يمكن التراجع عنها!\n\nهذا سيؤثر على:\n- تتبع أنشطة المستخدمين\n- سجلات التدقيق\n- التقارير التاريخية",
                    "تأكيد حذف جميع السجلات",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    // تأكيد إضافي للحذف الكامل
                    var finalConfirm = MessageBox.Show(
                        "هذا هو التأكيد الأخير!\n\nسيتم حذف جميع سجلات الأنشطة نهائياً.\nهل أنت متأكد 100%؟",
                        "التأكيد النهائي",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Stop);

                    if (finalConfirm == DialogResult.Yes)
                    {
                        await DeleteAllActivityLogs();
                        MessageBox.Show("تم حذف جميع السجلات بنجاح", "نجح الحذف", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        BtnRefresh_Click(sender, e); // تحديث البيانات
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف جميع السجلات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // دالة مساعدة لحذف سجلات محددة
        private async Task DeleteActivityLogs(List<int> logIds)
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                await connection.OpenAsync();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        foreach (var logId in logIds)
                        {
                            string sql = "DELETE FROM ActivityLogs WHERE LogId = @LogId";
                            using (var command = new SqlCommand(sql, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@LogId", logId);
                                await command.ExecuteNonQueryAsync();
                            }
                        }
                        
                        await transaction.CommitAsync();
                    }
                    catch
                    {
                        await transaction.RollbackAsync();
                        throw;
                    }
                }
            }
        }

        // دالة مساعدة لحذف جميع السجلات
        private async Task DeleteAllActivityLogs()
        {
            using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
            {
                await connection.OpenAsync();
                string sql = "DELETE FROM ActivityLogs";
                using (var command = new SqlCommand(sql, connection))
                {
                    await command.ExecuteNonQueryAsync();
                }
            }
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            if (keyData == Keys.F5)
            {
                BtnRefresh_Click(null, null);
                return true;
            }
            else if (keyData == Keys.Escape)
            {
                this.Close();
                return true;
            }
            return base.ProcessCmdKey(ref msg, keyData);
        }
    }
}