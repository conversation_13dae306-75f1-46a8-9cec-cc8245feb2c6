namespace EmployeeManagementSystem
{
    partial class SimpleEmployeeStatusFormV2
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            lblTitle = new Label();
            listBoxDays = new ListBox();
            lblSelectedDay = new Label();
            lblStatus = new Label();
            cmbStatus = new ComboBox();
            lblNotes = new Label();
            txtNotes = new TextBox();
            btnUpdate = new Button();
            btnSave = new Button();
            btnClose = new Button();
            groupBoxDays = new GroupBox();
            groupBoxEdit = new GroupBox();
            chkMultiSelect = new CheckBox();
            btnUpdateSelected = new Button();
            groupBoxDays.SuspendLayout();
            groupBoxEdit.SuspendLayout();
            SuspendLayout();
            // 
            // lblTitle
            // 
            lblTitle.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lblTitle.ForeColor = Color.DarkBlue;
            lblTitle.Location = new Point(12, 9);
            lblTitle.Name = "lblTitle";
            lblTitle.Size = new Size(578, 40);
            lblTitle.TabIndex = 0;
            lblTitle.Text = "تعديل الحالة اليومية للموظف";
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // listBoxDays
            // 
            listBoxDays.Font = new Font("Cairo", 9F, FontStyle.Regular, GraphicsUnit.Point, 0);
            listBoxDays.FormattingEnabled = true;
            listBoxDays.ItemHeight = 23;
            listBoxDays.Location = new Point(6, 25);
            listBoxDays.Name = "listBoxDays";
            listBoxDays.Size = new Size(280, 280);
            listBoxDays.TabIndex = 0;
            listBoxDays.SelectedIndexChanged += ListBoxDays_SelectedIndexChanged;
            // 
            // lblSelectedDay
            // 
            lblSelectedDay.Font = new Font("Cairo", 10F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lblSelectedDay.ForeColor = Color.DarkGreen;
            lblSelectedDay.Location = new Point(8, 25);
            lblSelectedDay.Name = "lblSelectedDay";
            lblSelectedDay.Size = new Size(250, 30);
            lblSelectedDay.TabIndex = 0;
            lblSelectedDay.Text = "اختر يوم من القائمة";
            lblSelectedDay.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblStatus
            // 
            lblStatus.AutoSize = true;
            lblStatus.Location = new Point(210, 70);
            lblStatus.Name = "lblStatus";
            lblStatus.Size = new Size(52, 26);
            lblStatus.TabIndex = 1;
            lblStatus.Text = "الحالة:";
            // 
            // cmbStatus
            // 
            cmbStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbStatus.FormattingEnabled = true;
            cmbStatus.Location = new Point(8, 67);
            cmbStatus.Name = "cmbStatus";
            cmbStatus.Size = new Size(196, 32);
            cmbStatus.TabIndex = 2;
            // 
            // lblNotes
            // 
            lblNotes.AutoSize = true;
            lblNotes.Location = new Point(176, 115);
            lblNotes.Name = "lblNotes";
            lblNotes.Size = new Size(79, 26);
            lblNotes.TabIndex = 3;
            lblNotes.Text = "الملاحظات:";
            // 
            // txtNotes
            // 
            txtNotes.Location = new Point(6, 143);
            txtNotes.Multiline = true;
            txtNotes.Name = "txtNotes";
            txtNotes.ScrollBars = ScrollBars.Vertical;
            txtNotes.Size = new Size(250, 60);
            txtNotes.TabIndex = 4;
            // 
            // btnUpdate
            // 
            btnUpdate.BackColor = Color.Blue;
            btnUpdate.FlatStyle = FlatStyle.Flat;
            btnUpdate.Font = new Font("Cairo", 9F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnUpdate.ForeColor = Color.White;
            btnUpdate.Image = Properties.Resources.update_32px;
            btnUpdate.ImageAlign = ContentAlignment.MiddleRight;
            btnUpdate.Location = new Point(55, 209);
            btnUpdate.Name = "btnUpdate";
            btnUpdate.Size = new Size(201, 41);
            btnUpdate.TabIndex = 5;
            btnUpdate.Text = "تحديث اليوم";
            btnUpdate.TextAlign = ContentAlignment.MiddleLeft;
            btnUpdate.UseVisualStyleBackColor = false;
            btnUpdate.Click += BtnUpdate_Click;
            // 
            // btnSave
            // 
            btnSave.BackColor = Color.Green;
            btnSave.FlatStyle = FlatStyle.Flat;
            btnSave.Font = new Font("Cairo", 10F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnSave.ForeColor = Color.White;
            btnSave.Image = Properties.Resources.ok_32px;
            btnSave.ImageAlign = ContentAlignment.MiddleRight;
            btnSave.Location = new Point(12, 442);
            btnSave.Name = "btnSave";
            btnSave.Size = new Size(114, 46);
            btnSave.TabIndex = 3;
            btnSave.Text = "حفظ الكل";
            btnSave.TextAlign = ContentAlignment.MiddleLeft;
            btnSave.UseVisualStyleBackColor = false;
            btnSave.Click += BtnSave_Click;
            // 
            // btnClose
            // 
            btnClose.BackColor = Color.Red;
            btnClose.FlatStyle = FlatStyle.Flat;
            btnClose.Font = new Font("Cairo", 10F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnClose.ForeColor = Color.White;
            btnClose.Image = Properties.Resources.delete_32px;
            btnClose.ImageAlign = ContentAlignment.MiddleRight;
            btnClose.Location = new Point(497, 442);
            btnClose.Name = "btnClose";
            btnClose.Size = new Size(93, 46);
            btnClose.TabIndex = 4;
            btnClose.Text = "إغلاق";
            btnClose.TextAlign = ContentAlignment.MiddleLeft;
            btnClose.UseVisualStyleBackColor = false;
            btnClose.Click += btnClose_Click;
            // 
            // groupBoxDays
            // 
            groupBoxDays.Controls.Add(listBoxDays);
            groupBoxDays.Font = new Font("Cairo", 10F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxDays.Location = new Point(12, 60);
            groupBoxDays.Name = "groupBoxDays";
            groupBoxDays.Size = new Size(300, 320);
            groupBoxDays.TabIndex = 1;
            groupBoxDays.TabStop = false;
            groupBoxDays.Text = "قائمة الأيام";
            // 
            // groupBoxEdit
            // 
            groupBoxEdit.Controls.Add(lblSelectedDay);
            groupBoxEdit.Controls.Add(btnUpdate);
            groupBoxEdit.Controls.Add(lblStatus);
            groupBoxEdit.Controls.Add(txtNotes);
            groupBoxEdit.Controls.Add(cmbStatus);
            groupBoxEdit.Controls.Add(lblNotes);
            groupBoxEdit.Controls.Add(chkMultiSelect);
            groupBoxEdit.Controls.Add(btnUpdateSelected);
            groupBoxEdit.Font = new Font("Cairo", 10F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxEdit.Location = new Point(320, 60);
            groupBoxEdit.Name = "groupBoxEdit";
            groupBoxEdit.Size = new Size(270, 320);
            groupBoxEdit.TabIndex = 2;
            groupBoxEdit.TabStop = false;
            groupBoxEdit.Text = "تعديل اليوم المحدد";
            // 
            // chkMultiSelect
            // 
            chkMultiSelect.AutoSize = true;
            chkMultiSelect.Location = new Point(8, 107);
            chkMultiSelect.Name = "chkMultiSelect";
            chkMultiSelect.RightToLeft = RightToLeft.No;
            chkMultiSelect.Size = new Size(109, 30);
            chkMultiSelect.TabIndex = 6;
            chkMultiSelect.Text = "تحديد متعدد";
            chkMultiSelect.UseVisualStyleBackColor = true;
            chkMultiSelect.CheckedChanged += ChkMultiSelect_CheckedChanged;
            // 
            // btnUpdateSelected
            // 
            btnUpdateSelected.BackColor = Color.Navy;
            btnUpdateSelected.FlatStyle = FlatStyle.Flat;
            btnUpdateSelected.Font = new Font("Cairo", 9F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnUpdateSelected.ForeColor = Color.White;
            btnUpdateSelected.Image = Properties.Resources.update_32px;
            btnUpdateSelected.ImageAlign = ContentAlignment.MiddleRight;
            btnUpdateSelected.Location = new Point(55, 257);
            btnUpdateSelected.Name = "btnUpdateSelected";
            btnUpdateSelected.Size = new Size(200, 43);
            btnUpdateSelected.TabIndex = 7;
            btnUpdateSelected.Text = "تحديث الحالة للأيام المحددة";
            btnUpdateSelected.TextAlign = ContentAlignment.MiddleLeft;
            btnUpdateSelected.UseVisualStyleBackColor = false;
            btnUpdateSelected.Click += BtnUpdateSelected_Click;
            // 
            // SimpleEmployeeStatusFormV2
            // 
            AutoScaleDimensions = new SizeF(6F, 20F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(604, 497);
            Controls.Add(groupBoxEdit);
            Controls.Add(groupBoxDays);
            Controls.Add(btnClose);
            Controls.Add(btnSave);
            Controls.Add(lblTitle);
            Font = new Font("Cairo", 8.25F, FontStyle.Regular, GraphicsUnit.Point, 0);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            Name = "SimpleEmployeeStatusFormV2";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterParent;
            Text = "تعديل الحالة اليومية";
            groupBoxDays.ResumeLayout(false);
            groupBoxEdit.ResumeLayout(false);
            groupBoxEdit.PerformLayout();
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label lblTitle;
        private System.Windows.Forms.ListBox listBoxDays;
        private System.Windows.Forms.Label lblSelectedDay;
        private System.Windows.Forms.ComboBox cmbStatus;
        private System.Windows.Forms.TextBox txtNotes;
        private System.Windows.Forms.Button btnUpdate;
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.Button btnClose;
        private System.Windows.Forms.CheckBox chkMultiSelect;
        private System.Windows.Forms.Button btnUpdateSelected;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.Label lblNotes;
        private System.Windows.Forms.GroupBox groupBoxEdit;
        private System.Windows.Forms.GroupBox groupBoxDays;
    }
}
