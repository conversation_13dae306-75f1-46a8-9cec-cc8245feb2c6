using System;
using System.Windows.Forms;
using System.Data;
using System.Data.SqlClient;
using System.Linq;

namespace EmployeeManagementSystem
{
    public partial class DepartmentForm : Form
    {
        private int? selectedDepartmentId = null;
        public int? AdminUserId { get; set; }

        public DepartmentForm()
        {
            InitializeComponent();
            LoadDepartments();
        }

        private void LoadDepartments()
        {
            var table = DatabaseHelper.GetAllDepartments();

            // تغيير أسماء الأعمدة للعرض بالعربية
            foreach (DataColumn column in table.Columns)
            {
                switch (column.ColumnName)
                {
                    case "DepartmentId":
                        column.ColumnName = "رقم القسم";
                        break;
                    case "DepartmentName":
                        column.ColumnName = "اسم القسم";
                        break;
                    case "DepartmentCode":
                        column.ColumnName = "رمز القسم";
                        break;
                    case "Description":
                        column.ColumnName = "الوصف";
                        break;
                    case "ManagerUserId":
                        column.ColumnName = "مدير القسم";
                        break;
                    case "CreatedDate":
                        column.ColumnName = "تاريخ الإنشاء";
                        break;
                    case "CreatedBy":
                        column.ColumnName = "أنشئ بواسطة";
                        break;
                    case "IsActive":
                        column.ColumnName = "نشط";
                        break;
                }
            }

            // إضافة عمود لعرض اسم مدير القسم بدلاً من الرقم
            AddManagerNameColumn(table);

            dataGridView1.DataSource = table;
            dataGridView1.Refresh();
        }

        private void AddManagerNameColumn(DataTable table)
        {
            // إضافة عمود جديد لاسم مدير القسم
            table.Columns.Add("اسم مدير القسم", typeof(string));

            foreach (DataRow row in table.Rows)
            {
                if (!row.IsNull("مدير القسم"))
                {
                    int managerId = Convert.ToInt32(row["مدير القسم"]);
                    try
                    {
                        var manager = DatabaseHelper.GetUserById(managerId);
                        row["اسم مدير القسم"] = manager.FullName;
                    }
                    catch
                    {
                        row["اسم مدير القسم"] = "غير معروف";
                    }
                }
                else
                {
                    row["اسم مدير القسم"] = "غير معين";
                }
            }
        }

        private string GenerateDepartmentCode()
        {
            try
            {
                // الحصول على أعلى رقم قسم موجود
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT MAX(DepartmentId) FROM Departments";
                    using (var command = new SqlCommand(sql, connection))
                    {
                        var result = command.ExecuteScalar();
                        int nextId = (result == DBNull.Value || result == null) ? 1 : Convert.ToInt32(result) + 1;
                        return $"DEPT{nextId:D3}"; // مثل: DEPT001, DEPT002, DEPT003
                    }
                }
            }
            catch
            {
                // في حالة الخطأ، استخدم الوقت الحالي
                return $"DEPT{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtDepartmentName.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم القسم", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // إنشاء رمز القسم تلقائياً إذا كان فارغاً
                string departmentCode = string.IsNullOrWhiteSpace(txtDepartmentCode.Text)
                    ? GenerateDepartmentCode()
                    : txtDepartmentCode.Text;

                var department = new Department
                {
                    DepartmentName = txtDepartmentName.Text,
                    DepartmentCode = departmentCode,
                    Description = txtDescription.Text,
                    CreatedDate = DateTime.Now,
                    CreatedBy = AdminUserId.HasValue ? AdminUserId.Value : 1, // استخدام معرف المستخدم الحالي
                    IsActive = chkIsActive.Checked
                };

                DatabaseHelper.AddDepartment(department);
                MessageBox.Show($"تمت إضافة القسم بنجاح\nرمز القسم: {departmentCode}", "نجاح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                LoadDepartments();
                ToastHelper.ShowAddToast();
                ClearFields();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnUpdate_Click(object sender, EventArgs e)
        {
            if (!selectedDepartmentId.HasValue)
            {
                MessageBox.Show("الرجاء اختيار قسم للتعديل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                if (string.IsNullOrWhiteSpace(txtDepartmentName.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم القسم", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // إنشاء رمز القسم تلقائياً إذا كان فارغاً
                string departmentCode = string.IsNullOrWhiteSpace(txtDepartmentCode.Text)
                    ? GenerateDepartmentCode()
                    : txtDepartmentCode.Text;

                var department = new Department
                {
                    DepartmentId = selectedDepartmentId.Value,
                    DepartmentName = txtDepartmentName.Text,
                    DepartmentCode = departmentCode,
                    Description = txtDescription.Text,
                    IsActive = chkIsActive.Checked
                };

                DatabaseHelper.UpdateDepartment(department);
                MessageBox.Show("تم تعديل القسم بنجاح", "نجاح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                LoadDepartments();
                ToastHelper.ShowEditToast();
                ClearFields();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (!selectedDepartmentId.HasValue)
            {
                MessageBox.Show("الرجاء اختيار قسم للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (MessageBox.Show("هل أنت متأكد من حذف هذا القسم؟", "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                try
                {
                    DatabaseHelper.DeleteDepartment(selectedDepartmentId.Value);
                    MessageBox.Show("تم حذف القسم بنجاح", "نجاح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    LoadDepartments();
                    ToastHelper.ShowDeleteToast();
                    ClearFields();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearFields();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentRow != null)
            {
                try
                {
                    selectedDepartmentId = Convert.ToInt32(dataGridView1.CurrentRow.Cells["رقم القسم"].Value);
                    if (selectedDepartmentId.HasValue)
                    {
                        var department = DatabaseHelper.GetDepartmentById(selectedDepartmentId.Value);
                        txtDepartmentName.Text = department.DepartmentName;
                        txtDepartmentCode.Text = department.DepartmentCode;
                        txtDescription.Text = department.Description;
                        chkIsActive.Checked = department.IsActive;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحديد الصف: {ex.Message}");
                }
            }
        }

        private void ClearFields()
        {
            selectedDepartmentId = null;
            txtDepartmentName.Clear();
            txtDepartmentCode.Clear();
            txtDescription.Clear();
            chkIsActive.Checked = true;
        }

        // دالة لإنشاء رمز قسم جديد تلقائياً
        private void btnGenerateCode_Click(object sender, EventArgs e)
        {
            txtDepartmentCode.Text = GenerateDepartmentCode();
        }

        // تحسين تجربة المستخدم - إنشاء رمز تلقائي عند كتابة اسم القسم
        private void txtDepartmentName_TextChanged(object sender, EventArgs e)
        {
            // إنشاء رمز تلقائي فقط إذا كان حقل الرمز فارغ وليس في وضع التعديل
            if (string.IsNullOrWhiteSpace(txtDepartmentCode.Text) &&
                !string.IsNullOrWhiteSpace(txtDepartmentName.Text) &&
                !selectedDepartmentId.HasValue)
            {
                // إنشاء رمز مبسط من اسم القسم
                string simplifiedCode = CreateSimplifiedCode(txtDepartmentName.Text);
                txtDepartmentCode.Text = simplifiedCode;
            }
        }

        private string CreateSimplifiedCode(string departmentName)
        {
            try
            {
                // أخذ أول 3 أحرف من اسم القسم وإضافة رقم
                string prefix = "";
                string[] words = departmentName.Trim().Split(' ');

                if (words.Length >= 2)
                {
                    // إذا كان هناك كلمتان أو أكثر، خذ أول حرف من كل كلمة
                    foreach (string word in words.Take(3))
                    {
                        if (!string.IsNullOrWhiteSpace(word))
                        {
                            prefix += word[0];
                        }
                    }
                }
                else
                {
                    // إذا كان هناك كلمة واحدة، خذ أول 3 أحرف
                    prefix = departmentName.Length >= 3 ? departmentName.Substring(0, 3) : departmentName;
                }

                // إضافة رقم تسلسلي
                using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                {
                    connection.Open();
                    string sql = "SELECT MAX(DepartmentId) FROM Departments";
                    using (var command = new SqlCommand(sql, connection))
                    {
                        var result = command.ExecuteScalar();
                        int nextId = (result == DBNull.Value || result == null) ? 1 : Convert.ToInt32(result) + 1;
                        return $"{prefix.ToUpper()}{nextId:D2}"; // مثل: ADM01, ACC02
                    }
                }
            }
            catch
            {
                return GenerateDepartmentCode();
            }
        }

        private void DepartmentForm_Load(object sender, EventArgs e)
        {
            LoadDepartments();

            // ربط الأحداث
            txtDepartmentName.TextChanged += txtDepartmentName_TextChanged;
        }
    }
}