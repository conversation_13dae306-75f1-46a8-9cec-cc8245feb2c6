using System;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    /// <summary>
    /// مساعد لتسهيل إضافة تسجيل الأنشطة للنماذج الموجودة
    /// </summary>
    public static class ActivityLogHelper
    {
        /// <summary>
        /// تسجيل عملية مع قياس الوقت
        /// </summary>
        public static async Task<T> LogTimedOperationAsync<T>(string action, string tableName, Func<Task<T>> operation, 
            int? recordId = null, object? oldValues = null, string actionType = "عادي", string priority = "عادي", string notes = null)
        {
            return await ActivityLogService.LogTimedActivityAsync(action, tableName, operation, recordId, oldValues, actionType, priority, notes);
        }

        /// <summary>
        /// تسجيل عملية إضافة مع التعامل مع الأخطاء
        /// </summary>
        public static async Task<bool> LogAddOperationAsync<T>(string itemName, string tableName, int? recordId, T newData, string priority = "عادي")
        {
            try
            {
                await ActivityLogService.LogActivityAsync(
                    action: $"إضافة {itemName}",
                    tableName: tableName,
                    recordId: recordId,
                    oldValues: null,
                    newValues: newData,
                    actionType: "إضافة",
                    priority: priority,
                    notes: $"تم إضافة {itemName} بنجاح"
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"تسجيل عملية إضافة {itemName}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل عملية تحديث مع التعامل مع الأخطاء
        /// </summary>
        public static async Task<bool> LogUpdateOperationAsync<T>(string itemName, string tableName, int recordId, T oldData, T newData, string priority = "عادي")
        {
            try
            {
                await ActivityLogService.LogActivityAsync(
                    action: $"تحديث {itemName}",
                    tableName: tableName,
                    recordId: recordId,
                    oldValues: oldData,
                    newValues: newData,
                    actionType: "تحديث",
                    priority: priority,
                    notes: $"تم تحديث {itemName} بنجاح"
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"تسجيل عملية تحديث {itemName}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل عملية حذف مع التعامل مع الأخطاء
        /// </summary>
        public static async Task<bool> LogDeleteOperationAsync<T>(string itemName, string tableName, int recordId, T deletedData, string priority = "مهم")
        {
            try
            {
                await ActivityLogService.LogActivityAsync(
                    action: $"حذف {itemName}",
                    tableName: tableName,
                    recordId: recordId,
                    oldValues: deletedData,
                    newValues: null,
                    actionType: "حذف",
                    priority: priority,
                    notes: $"تم حذف {itemName} نهائياً"
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"تسجيل عملية حذف {itemName}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل عملية استعلام مع التعامل مع الأخطاء
        /// </summary>
        public static async Task<bool> LogQueryOperationAsync(string queryDescription, string tableName, string notes = null, string priority = "عادي")
        {
            try
            {
                await ActivityLogService.LogActivityAsync(
                    action: $"استعلام: {queryDescription}",
                    tableName: tableName,
                    recordId: null,
                    oldValues: null,
                    newValues: null,
                    actionType: "استعلام",
                    priority: priority,
                    notes: notes ?? $"تم تنفيذ استعلام: {queryDescription}"
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"تسجيل عملية الاستعلام: {queryDescription}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل عملية فاشلة مع التعامل مع الأخطاء
        /// </summary>
        public static async Task<bool> LogFailedOperationAsync(string operationName, string tableName, string errorMessage, int? recordId = null, string priority = "حرج")
        {
            try
            {
                await ActivityLogService.LogFailedActivityAsync(
                    action: operationName,
                    tableName: tableName,
                    errorMessage: errorMessage,
                    recordId: recordId,
                    priority: priority
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"تسجيل العملية الفاشلة: {operationName}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل فتح نموذج أو تقرير
        /// </summary>
        public static async Task<bool> LogFormAccessAsync(string formName, string notes = null)
        {
            try
            {
                await ActivityLogService.LogActivityAsync(
                    action: $"فتح نموذج: {formName}",
                    tableName: "System",
                    recordId: null,
                    oldValues: null,
                    newValues: null,
                    actionType: "استعلام",
                    priority: "عادي",
                    notes: notes ?? $"تم فتح نموذج {formName}"
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"تسجيل فتح النموذج: {formName}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل تصدير البيانات
        /// </summary>
        public static async Task<bool> LogExportOperationAsync(string exportType, string tableName, int recordCount, string filePath = null)
        {
            try
            {
                var notes = $"تم تصدير {recordCount} سجل";
                if (!string.IsNullOrEmpty(filePath))
                    notes += $" إلى الملف: {filePath}";

                await ActivityLogService.LogActivityAsync(
                    action: $"تصدير {exportType}",
                    tableName: tableName,
                    recordId: null,
                    oldValues: null,
                    newValues: new { RecordCount = recordCount, FilePath = filePath },
                    actionType: "استعلام",
                    priority: "عادي",
                    notes: notes
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"تسجيل عملية التصدير: {exportType}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل طباعة التقارير
        /// </summary>
        public static async Task<bool> LogPrintOperationAsync(string reportName, string tableName, int recordCount = 0)
        {
            try
            {
                await ActivityLogService.LogActivityAsync(
                    action: $"طباعة تقرير: {reportName}",
                    tableName: tableName,
                    recordId: null,
                    oldValues: null,
                    newValues: new { ReportName = reportName, RecordCount = recordCount },
                    actionType: "استعلام",
                    priority: "عادي",
                    notes: $"تم طباعة تقرير {reportName}"
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"تسجيل عملية الطباعة: {reportName}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل عملية نسخ احتياطي
        /// </summary>
        public static async Task<bool> LogBackupOperationAsync(string backupType, string filePath, bool success = true, string errorMessage = null)
        {
            try
            {
                if (success)
                {
                    await ActivityLogService.LogActivityAsync(
                        action: $"نسخ احتياطي: {backupType}",
                        tableName: "System",
                        recordId: null,
                        oldValues: null,
                        newValues: new { BackupType = backupType, FilePath = filePath },
                        actionType: "استعلام",
                        priority: "مهم",
                        notes: $"تم إنشاء نسخة احتياطية بنجاح: {filePath}"
                    );
                }
                else
                {
                    await ActivityLogService.LogFailedActivityAsync(
                        action: $"نسخ احتياطي: {backupType}",
                        tableName: "System",
                        errorMessage: errorMessage ?? "خطأ غير محدد",
                        recordId: null,
                        priority: "حرج"
                    );
                }
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"تسجيل عملية النسخ الاحتياطي: {backupType}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل تغيير الإعدادات
        /// </summary>
        public static async Task<bool> LogSettingsChangeAsync(string settingName, object oldValue, object newValue)
        {
            try
            {
                await ActivityLogService.LogActivityAsync(
                    action: $"تغيير إعداد: {settingName}",
                    tableName: "Settings",
                    recordId: null,
                    oldValues: new { SettingName = settingName, Value = oldValue },
                    newValues: new { SettingName = settingName, Value = newValue },
                    actionType: "تحديث",
                    priority: "مهم",
                    notes: $"تم تغيير إعداد {settingName}"
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"تسجيل تغيير الإعداد: {settingName}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل أخطاء داخلية في النظام
        /// </summary>
        private static async Task LogErrorAsync(string operation, string errorMessage)
        {
            try
            {
                System.IO.File.AppendAllText("hrms_errors.log", 
                    $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] خطأ في {operation}: {errorMessage}\n");
            }
            catch
            {
                // لا نفعل شيئاً لتجنب التكرار اللانهائي
            }
        }

        /// <summary>
        /// تسجيل بداية جلسة المستخدم
        /// </summary>
        public static async Task<bool> LogSessionStartAsync(string sessionInfo = null)
        {
            try
            {
                await ActivityLogService.LogActivityAsync(
                    action: "بداية جلسة العمل",
                    tableName: "System",
                    recordId: null,
                    oldValues: null,
                    newValues: new { SessionInfo = sessionInfo, StartTime = DateTime.Now },
                    actionType: "تسجيل دخول",
                    priority: "عادي",
                    notes: sessionInfo ?? "بداية جلسة عمل جديدة"
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync("تسجيل بداية الجلسة", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل نهاية جلسة المستخدم
        /// </summary>
        public static async Task<bool> LogSessionEndAsync(string sessionInfo = null)
        {
            try
            {
                await ActivityLogService.LogActivityAsync(
                    action: "نهاية جلسة العمل",
                    tableName: "System",
                    recordId: null,
                    oldValues: null,
                    newValues: new { SessionInfo = sessionInfo, EndTime = DateTime.Now },
                    actionType: "تسجيل خروج",
                    priority: "عادي",
                    notes: sessionInfo ?? "انتهاء جلسة العمل"
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync("تسجيل نهاية الجلسة", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل محاولة وصول غير مصرح بها
        /// </summary>
        public static async Task<bool> LogUnauthorizedAccessAsync(string resourceName, string reason = null)
        {
            try
            {
                await ActivityLogService.LogActivityAsync(
                    action: $"محاولة وصول غير مصرح بها: {resourceName}",
                    tableName: "Security",
                    recordId: null,
                    oldValues: null,
                    newValues: new { Resource = resourceName, Reason = reason },
                    actionType: "خطأ",
                    priority: "حرج",
                    notes: reason ?? "محاولة وصول غير مصرح بها"
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"تسجيل محاولة الوصول غير المصرح بها: {resourceName}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// إنشاء سجل مخصص للأنشطة المعقدة
        /// </summary>
        public static async Task<bool> LogCustomActivityAsync(
            string action, 
            string tableName, 
            int? recordId = null, 
            object oldValues = null, 
            object newValues = null,
            string actionType = "استعلام",
            string priority = "عادي",
            string notes = null)
        {
            try
            {
                await ActivityLogService.LogActivityAsync(
                    action: action,
                    tableName: tableName,
                    recordId: recordId,
                    oldValues: oldValues,
                    newValues: newValues,
                    actionType: actionType,
                    priority: priority,
                    notes: notes
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"تسجيل النشاط المخصص: {action}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل عملية خطيرة مع تأكيد إضافي
        /// </summary>
        public static async Task<bool> LogCriticalOperationAsync<T>(string action, string tableName, int? recordId, 
            T oldData, T newData, string reason = null)
        {
            try
            {
                await ActivityLogService.LogActivityAsync(
                    action: action,
                    tableName: tableName,
                    recordId: recordId,
                    oldValues: oldData,
                    newValues: newData,
                    actionType: "حرج",
                    priority: "حرج",
                    notes: reason ?? "عملية حرجة"
                );
                return true;
            }
            catch (Exception ex)
            {
                await ActivityLogService.LogFailedActivityAsync(
                    action: action,
                    tableName: tableName,
                    errorMessage: ex.Message,
                    recordId: recordId,
                    priority: "حرج"
                );
                return false;
            }
        }

        /// <summary>
        /// تسجيل عملية مع تحديد وقت التنفيذ
        /// </summary>
        public static async Task<bool> LogOperationWithTimingAsync<T>(
            string operation,
            string tableName,
            Func<Task<T>> operationFunc,
            int? recordId = null,
            string priority = "عادي")
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            Exception operationException = null;
            T result = default(T);

            try
            {
                result = await operationFunc();
                stopwatch.Stop();

                await ActivityLogService.LogActivityAsync(
                    action: operation,
                    tableName: tableName,
                    recordId: recordId,
                    oldValues: null,
                    newValues: result,
                    actionType: "استعلام",
                    priority: priority,
                    notes: $"تم تنفيذ {operation} في {stopwatch.ElapsedMilliseconds} ميلي ثانية"
                );

                return true;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                operationException = ex;

                await LogFailedOperationAsync(
                    operation,
                    tableName,
                    $"فشل في {stopwatch.ElapsedMilliseconds} ميلي ثانية: {ex.Message}",
                    recordId,
                    "حرج"
                );

                throw; // إعادة رفع الاستثناء
            }
        }

        /// <summary>
        /// تسجيل تجميعي للعمليات المتعددة
        /// </summary>
        public static async Task<bool> LogBatchOperationAsync(
            string batchName,
            string tableName,
            int processedCount,
            int successCount,
            int failureCount,
            TimeSpan duration)
        {
            try
            {
                await ActivityLogService.LogActivityAsync(
                    action: $"عملية تجميعية: {batchName}",
                    tableName: tableName,
                    recordId: null,
                    oldValues: null,
                    newValues: new 
                    { 
                        BatchName = batchName,
                        ProcessedCount = processedCount,
                        SuccessCount = successCount,
                        FailureCount = failureCount,
                        Duration = duration.TotalSeconds
                    },
                    actionType: "استعلام",
                    priority: failureCount > 0 ? "مهم" : "عادي",
                    notes: $"تم معالجة {processedCount} عنصر، نجح {successCount}، فشل {failureCount}"
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"تسجيل العملية التجميعية: {batchName}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل عملية البحث
        /// </summary>
        public static async Task<bool> LogSearchAsync(string searchType, string tableName, string searchCriteria, int resultCount = 0, string notes = null)
        {
            try
            {
                await ActivityLogService.LogActivityAsync(
                    action: $"بحث: {searchType}",
                    tableName: tableName,
                    recordId: null,
                    oldValues: null,
                    newValues: new { SearchCriteria = searchCriteria, ResultCount = resultCount },
                    actionType: "استعلام",
                    priority: "عادي",
                    notes: notes ?? $"تم البحث عن {searchType} وإرجاع {resultCount} نتيجة"
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"تسجيل عملية البحث: {searchType}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل عملية التصدير المحسنة
        /// </summary>
        public static async Task<bool> LogExportAsync(string exportType, string tableName, int recordCount, string filePath = null, string format = null)
        {
            try
            {
                var notes = $"تم تصدير {recordCount} سجل";
                if (!string.IsNullOrEmpty(format))
                    notes += $" بصيغة {format}";
                if (!string.IsNullOrEmpty(filePath))
                    notes += $" إلى الملف: {filePath}";

                await ActivityLogService.LogActivityAsync(
                    action: $"تصدير {exportType}",
                    tableName: tableName,
                    recordId: null,
                    oldValues: null,
                    newValues: new { RecordCount = recordCount, FilePath = filePath, Format = format },
                    actionType: "تصدير",
                    priority: "عادي",
                    notes: notes
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"تسجيل عملية التصدير: {exportType}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// تسجيل عملية إنشاء التقارير
        /// </summary>
        public static async Task<bool> LogReportGenerationAsync(string reportName, string tableName, int recordCount = 0, string reportType = null, string parameters = null)
        {
            try
            {
                var notes = $"تم إنشاء تقرير {reportName}";
                if (recordCount > 0)
                    notes += $" يحتوي على {recordCount} سجل";
                if (!string.IsNullOrEmpty(reportType))
                    notes += $" من نوع {reportType}";

                await ActivityLogService.LogActivityAsync(
                    action: $"إنشاء تقرير: {reportName}",
                    tableName: tableName,
                    recordId: null,
                    oldValues: null,
                    newValues: new {
                        ReportName = reportName,
                        RecordCount = recordCount,
                        ReportType = reportType,
                        Parameters = parameters
                    },
                    actionType: "تقرير",
                    priority: "عادي",
                    notes: notes
                );
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"تسجيل عملية إنشاء التقرير: {reportName}", ex.Message);
                return false;
            }
        }
    }
}