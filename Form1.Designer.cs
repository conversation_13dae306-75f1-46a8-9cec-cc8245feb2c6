﻿using System;
using System.Windows.Forms;
using System.Drawing;

namespace EmployeeManagementSystem
{
    partial class Form1
    {
        private System.ComponentModel.IContainer components = null;
        private CheckBox chkSelectAll;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            txtName = new TextBox();
            txtMotherName = new TextBox();
            txtProvince = new TextBox();
            chkSelectAll = new CheckBox();
            txtNationality = new TextBox();
            txtIdentityNumber = new TextBox();
            txtAdminOrder = new TextBox();
            txtStatisticalNumber = new TextBox();
            txtKeyCard = new TextBox();
            txtPhone = new TextBox();
            txtCategory = new TextBox();
            txtBadgeNumber = new TextBox();
            dtpDateOfBirth = new DateTimePicker();
            dtpStartDate = new DateTimePicker();
            dtpBadgeExpiry = new DateTimePicker();
            btnSave = new Button();
            btnDelete = new Button();
            btnDocuments = new Button();
            dgvEmployees = new DataGridView();
            dataGridViewTextBoxColumn1 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn13 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn2 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn3 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn16 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn17 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn6 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn5 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn7 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn4 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn19 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn18 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn9 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn8 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn10 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn11 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn12 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn14 = new DataGridViewTextBoxColumn();
            dataGridViewTextBoxColumn15 = new DataGridViewTextBoxColumn();
            btnPrintSelected = new Button();
            btnPrintAll = new Button();
            btnEdit = new Button();
            btnClear = new Button();
            btnAddPhoto = new Button();
            lblName = new Label();
            lblMotherName = new Label();
            lblProvince = new Label();
            lblNationality = new Label();
            lblIdentityNumber = new Label();
            lblAdminOrder = new Label();
            lblStatisticalNumber = new Label();
            lblKeyCard = new Label();
            lblPhone = new Label();
            lblCategory = new Label();
            lblBadgeNumber = new Label();
            lblDateOfBirth = new Label();
            lblStartDate = new Label();
            lblBadgeExpiry = new Label();
            txtSearch = new TextBox();
            previewBox = new PictureBox();
            button1 = new Button();
            btn_Scan = new Button();
            listOfScanner = new ComboBox();
            button2 = new Button();
            btnViewPhoto = new Button();
            btnOpenCamera = new Button();
            cmbMaritalStatus = new ComboBox();
            txtWifeName = new TextBox();
            txtElectoralNumber = new TextBox();
            cmbEducationLevel = new ComboBox();
            label2 = new Label();
            label3 = new Label();
            label4 = new Label();
            label5 = new Label();
            panel1 = new Panel();
            lbl_NoDocuments = new Label();
            toolTip1 = new ToolTip(components);
            btnSearch = new Button();
            btnRefresh = new Button();
            ((System.ComponentModel.ISupportInitialize)dgvEmployees).BeginInit();
            ((System.ComponentModel.ISupportInitialize)previewBox).BeginInit();
            panel1.SuspendLayout();
            SuspendLayout();
            // 
            // txtName
            // 
            txtName.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtName.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtName.Location = new Point(801, 60);
            txtName.Margin = new Padding(3, 2, 3, 2);
            txtName.Name = "txtName";
            txtName.Size = new Size(241, 37);
            txtName.TabIndex = 1;
            // 
            // txtMotherName
            // 
            txtMotherName.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtMotherName.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtMotherName.Location = new Point(801, 102);
            txtMotherName.Margin = new Padding(3, 2, 3, 2);
            txtMotherName.Name = "txtMotherName";
            txtMotherName.Size = new Size(241, 37);
            txtMotherName.TabIndex = 2;
            // 
            // txtProvince
            // 
            txtProvince.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtProvince.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtProvince.Location = new Point(801, 229);
            txtProvince.Margin = new Padding(3, 2, 3, 2);
            txtProvince.Name = "txtProvince";
            txtProvince.Size = new Size(241, 37);
            txtProvince.TabIndex = 5;
            // 
            // chkSelectAll
            // 
            chkSelectAll.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            chkSelectAll.Font = new Font("Cairo", 12F, FontStyle.Bold);
            chkSelectAll.Image = Properties.Resources.checked_checkbox_32px;
            chkSelectAll.ImageAlign = ContentAlignment.MiddleRight;
            chkSelectAll.Location = new Point(487, 458);
            chkSelectAll.Name = "chkSelectAll";
            chkSelectAll.RightToLeft = RightToLeft.No;
            chkSelectAll.Size = new Size(98, 34);
            chkSelectAll.TabIndex = 36;
            chkSelectAll.Text = "تحديد ";
            toolTip1.SetToolTip(chkSelectAll, "تحديد الكل");
            chkSelectAll.UseVisualStyleBackColor = true;
            chkSelectAll.CheckedChanged += ChkSelectAll_CheckedChanged;
            // 
            // txtNationality
            // 
            txtNationality.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtNationality.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtNationality.Location = new Point(801, 271);
            txtNationality.Margin = new Padding(3, 2, 3, 2);
            txtNationality.Name = "txtNationality";
            txtNationality.Size = new Size(241, 37);
            txtNationality.TabIndex = 6;
            // 
            // txtIdentityNumber
            // 
            txtIdentityNumber.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtIdentityNumber.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtIdentityNumber.Location = new Point(801, 313);
            txtIdentityNumber.Margin = new Padding(3, 2, 3, 2);
            txtIdentityNumber.Name = "txtIdentityNumber";
            txtIdentityNumber.Size = new Size(241, 37);
            txtIdentityNumber.TabIndex = 7;
            // 
            // txtAdminOrder
            // 
            txtAdminOrder.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtAdminOrder.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtAdminOrder.Location = new Point(392, 102);
            txtAdminOrder.Margin = new Padding(3, 2, 3, 2);
            txtAdminOrder.Name = "txtAdminOrder";
            txtAdminOrder.Size = new Size(241, 37);
            txtAdminOrder.TabIndex = 11;
            // 
            // txtStatisticalNumber
            // 
            txtStatisticalNumber.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtStatisticalNumber.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtStatisticalNumber.Location = new Point(392, 187);
            txtStatisticalNumber.Margin = new Padding(3, 2, 3, 2);
            txtStatisticalNumber.Name = "txtStatisticalNumber";
            txtStatisticalNumber.Size = new Size(241, 37);
            txtStatisticalNumber.TabIndex = 13;
            // 
            // txtKeyCard
            // 
            txtKeyCard.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtKeyCard.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtKeyCard.Location = new Point(392, 229);
            txtKeyCard.Margin = new Padding(3, 2, 3, 2);
            txtKeyCard.Name = "txtKeyCard";
            txtKeyCard.Size = new Size(241, 37);
            txtKeyCard.TabIndex = 14;
            // 
            // txtPhone
            // 
            txtPhone.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtPhone.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtPhone.Location = new Point(392, 271);
            txtPhone.Margin = new Padding(3, 2, 3, 2);
            txtPhone.Name = "txtPhone";
            txtPhone.Size = new Size(241, 37);
            txtPhone.TabIndex = 15;
            // 
            // txtCategory
            // 
            txtCategory.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtCategory.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtCategory.Location = new Point(801, 18);
            txtCategory.Margin = new Padding(3, 2, 3, 2);
            txtCategory.Name = "txtCategory";
            txtCategory.Size = new Size(241, 37);
            txtCategory.TabIndex = 0;
            // 
            // txtBadgeNumber
            // 
            txtBadgeNumber.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtBadgeNumber.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtBadgeNumber.Location = new Point(392, 313);
            txtBadgeNumber.Margin = new Padding(3, 2, 3, 2);
            txtBadgeNumber.Name = "txtBadgeNumber";
            txtBadgeNumber.Size = new Size(241, 37);
            txtBadgeNumber.TabIndex = 17;
            // 
            // dtpDateOfBirth
            // 
            dtpDateOfBirth.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            dtpDateOfBirth.Font = new Font("Cairo", 12F, FontStyle.Bold);
            dtpDateOfBirth.Format = DateTimePickerFormat.Short;
            dtpDateOfBirth.Location = new Point(801, 355);
            dtpDateOfBirth.Margin = new Padding(3, 2, 3, 2);
            dtpDateOfBirth.Name = "dtpDateOfBirth";
            dtpDateOfBirth.Size = new Size(241, 37);
            dtpDateOfBirth.TabIndex = 8;
            // 
            // dtpStartDate
            // 
            dtpStartDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            dtpStartDate.Font = new Font("Cairo", 12F, FontStyle.Bold);
            dtpStartDate.Format = DateTimePickerFormat.Short;
            dtpStartDate.Location = new Point(392, 144);
            dtpStartDate.Margin = new Padding(3, 2, 3, 2);
            dtpStartDate.Name = "dtpStartDate";
            dtpStartDate.Size = new Size(241, 37);
            dtpStartDate.TabIndex = 12;
            // 
            // dtpBadgeExpiry
            // 
            dtpBadgeExpiry.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            dtpBadgeExpiry.Font = new Font("Cairo", 12F, FontStyle.Bold);
            dtpBadgeExpiry.Format = DateTimePickerFormat.Short;
            dtpBadgeExpiry.Location = new Point(392, 355);
            dtpBadgeExpiry.Margin = new Padding(3, 2, 3, 2);
            dtpBadgeExpiry.Name = "dtpBadgeExpiry";
            dtpBadgeExpiry.Size = new Size(241, 37);
            dtpBadgeExpiry.TabIndex = 18;
            // 
            // btnSave
            // 
            btnSave.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnSave.BackColor = Color.FromArgb(45, 66, 91);
            btnSave.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnSave.ForeColor = Color.White;
            btnSave.Image = Properties.Resources.ok_32px;
            btnSave.ImageAlign = ContentAlignment.MiddleLeft;
            btnSave.Location = new Point(1091, 404);
            btnSave.Margin = new Padding(3, 2, 3, 2);
            btnSave.Name = "btnSave";
            btnSave.Size = new Size(94, 41);
            btnSave.TabIndex = 22;
            btnSave.Text = "حفظ";
            btnSave.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(btnSave, "حفظ");
            btnSave.UseVisualStyleBackColor = false;
            btnSave.Click += btnSave_Click;
            // 
            // btnDelete
            // 
            btnDelete.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnDelete.BackColor = Color.FromArgb(45, 66, 91);
            btnDelete.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnDelete.ForeColor = Color.White;
            btnDelete.Image = Properties.Resources.remove_32px;
            btnDelete.ImageAlign = ContentAlignment.MiddleLeft;
            btnDelete.Location = new Point(898, 403);
            btnDelete.Margin = new Padding(3, 2, 3, 2);
            btnDelete.Name = "btnDelete";
            btnDelete.Size = new Size(84, 42);
            btnDelete.TabIndex = 24;
            btnDelete.Text = "حذف";
            btnDelete.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(btnDelete, "حذف");
            btnDelete.UseVisualStyleBackColor = false;
            btnDelete.Click += btnDelete_Click;
            // 
            // btnDocuments
            // 
            btnDocuments.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            btnDocuments.BackColor = Color.FromArgb(45, 66, 91);
            btnDocuments.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnDocuments.ForeColor = Color.White;
            btnDocuments.Image = Properties.Resources.documents_32px;
            btnDocuments.ImageAlign = ContentAlignment.MiddleLeft;
            btnDocuments.Location = new Point(5, 403);
            btnDocuments.Margin = new Padding(3, 2, 3, 2);
            btnDocuments.Name = "btnDocuments";
            btnDocuments.Size = new Size(97, 42);
            btnDocuments.TabIndex = 21;
            btnDocuments.Text = "إرفاق  ";
            btnDocuments.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(btnDocuments, "اضافة مرفقات");
            btnDocuments.UseVisualStyleBackColor = false;
            btnDocuments.Click += btnDocuments_Click;
            // 
            // dgvEmployees
            // 
            dgvEmployees.AllowUserToAddRows = false;
            dgvEmployees.AllowUserToDeleteRows = false;
            dgvEmployees.Columns.AddRange(new DataGridViewColumn[] { dataGridViewTextBoxColumn1, dataGridViewTextBoxColumn13, dataGridViewTextBoxColumn2, dataGridViewTextBoxColumn3, dataGridViewTextBoxColumn16, dataGridViewTextBoxColumn17, dataGridViewTextBoxColumn6, dataGridViewTextBoxColumn5, dataGridViewTextBoxColumn7, dataGridViewTextBoxColumn4, dataGridViewTextBoxColumn19, dataGridViewTextBoxColumn18, dataGridViewTextBoxColumn9, dataGridViewTextBoxColumn8, dataGridViewTextBoxColumn10, dataGridViewTextBoxColumn11, dataGridViewTextBoxColumn12, dataGridViewTextBoxColumn14, dataGridViewTextBoxColumn15 });
            dgvEmployees.Dock = DockStyle.Fill;
            dgvEmployees.Location = new Point(0, 0);
            dgvEmployees.Margin = new Padding(3, 2, 3, 2);
            dgvEmployees.MultiSelect = false;
            dgvEmployees.Name = "dgvEmployees";
            dgvEmployees.ReadOnly = true;
            dgvEmployees.RightToLeft = RightToLeft.Yes;
            dgvEmployees.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvEmployees.Size = new Size(1183, 331);
            dgvEmployees.TabIndex = 40;
            dgvEmployees.CellClick += dgvEmployees_CellClick;
            // 
            // dataGridViewTextBoxColumn1
            // 
            dataGridViewTextBoxColumn1.HeaderText = "كود الموظف";
            dataGridViewTextBoxColumn1.Name = "dataGridViewTextBoxColumn1";
            dataGridViewTextBoxColumn1.ReadOnly = true;
            dataGridViewTextBoxColumn1.Width = 60;
            // 
            // dataGridViewTextBoxColumn13
            // 
            dataGridViewTextBoxColumn13.HeaderText = "الصنف";
            dataGridViewTextBoxColumn13.Name = "dataGridViewTextBoxColumn13";
            dataGridViewTextBoxColumn13.ReadOnly = true;
            dataGridViewTextBoxColumn13.Width = 60;
            // 
            // dataGridViewTextBoxColumn2
            // 
            dataGridViewTextBoxColumn2.HeaderText = "الاسم";
            dataGridViewTextBoxColumn2.Name = "dataGridViewTextBoxColumn2";
            dataGridViewTextBoxColumn2.ReadOnly = true;
            dataGridViewTextBoxColumn2.Width = 60;
            // 
            // dataGridViewTextBoxColumn3
            // 
            dataGridViewTextBoxColumn3.HeaderText = "اسم الأم";
            dataGridViewTextBoxColumn3.Name = "dataGridViewTextBoxColumn3";
            dataGridViewTextBoxColumn3.ReadOnly = true;
            dataGridViewTextBoxColumn3.Width = 61;
            // 
            // dataGridViewTextBoxColumn16
            // 
            dataGridViewTextBoxColumn16.HeaderText = "الحالة الاجتماعية";
            dataGridViewTextBoxColumn16.Name = "dataGridViewTextBoxColumn16";
            dataGridViewTextBoxColumn16.ReadOnly = true;
            dataGridViewTextBoxColumn16.Resizable = DataGridViewTriState.True;
            dataGridViewTextBoxColumn16.Width = 60;
            // 
            // dataGridViewTextBoxColumn17
            // 
            dataGridViewTextBoxColumn17.HeaderText = "اسم الزوج/ـة";
            dataGridViewTextBoxColumn17.Name = "dataGridViewTextBoxColumn17";
            dataGridViewTextBoxColumn17.ReadOnly = true;
            dataGridViewTextBoxColumn17.Width = 60;
            // 
            // dataGridViewTextBoxColumn6
            // 
            dataGridViewTextBoxColumn6.HeaderText = "الجنسية";
            dataGridViewTextBoxColumn6.Name = "dataGridViewTextBoxColumn6";
            dataGridViewTextBoxColumn6.ReadOnly = true;
            dataGridViewTextBoxColumn6.Width = 60;
            // 
            // dataGridViewTextBoxColumn5
            // 
            dataGridViewTextBoxColumn5.HeaderText = "المحافظة";
            dataGridViewTextBoxColumn5.Name = "dataGridViewTextBoxColumn5";
            dataGridViewTextBoxColumn5.ReadOnly = true;
            dataGridViewTextBoxColumn5.Width = 60;
            // 
            // dataGridViewTextBoxColumn7
            // 
            dataGridViewTextBoxColumn7.HeaderText = "رقم الهوية";
            dataGridViewTextBoxColumn7.Name = "dataGridViewTextBoxColumn7";
            dataGridViewTextBoxColumn7.ReadOnly = true;
            dataGridViewTextBoxColumn7.Width = 60;
            // 
            // dataGridViewTextBoxColumn4
            // 
            dataGridViewTextBoxColumn4.HeaderText = "تاريخ الميلاد";
            dataGridViewTextBoxColumn4.Name = "dataGridViewTextBoxColumn4";
            dataGridViewTextBoxColumn4.ReadOnly = true;
            dataGridViewTextBoxColumn4.Width = 61;
            // 
            // dataGridViewTextBoxColumn19
            // 
            dataGridViewTextBoxColumn19.HeaderText = "المستوى التعليمي";
            dataGridViewTextBoxColumn19.Name = "dataGridViewTextBoxColumn19";
            dataGridViewTextBoxColumn19.ReadOnly = true;
            dataGridViewTextBoxColumn19.Width = 60;
            // 
            // dataGridViewTextBoxColumn18
            // 
            dataGridViewTextBoxColumn18.HeaderText = "الرقم الانتخابي";
            dataGridViewTextBoxColumn18.Name = "dataGridViewTextBoxColumn18";
            dataGridViewTextBoxColumn18.ReadOnly = true;
            dataGridViewTextBoxColumn18.Width = 60;
            // 
            // dataGridViewTextBoxColumn9
            // 
            dataGridViewTextBoxColumn9.HeaderText = "الأمر الإداري";
            dataGridViewTextBoxColumn9.Name = "dataGridViewTextBoxColumn9";
            dataGridViewTextBoxColumn9.ReadOnly = true;
            dataGridViewTextBoxColumn9.Width = 60;
            // 
            // dataGridViewTextBoxColumn8
            // 
            dataGridViewTextBoxColumn8.HeaderText = "تاريخ الامر الاداري";
            dataGridViewTextBoxColumn8.Name = "dataGridViewTextBoxColumn8";
            dataGridViewTextBoxColumn8.ReadOnly = true;
            dataGridViewTextBoxColumn8.Width = 60;
            // 
            // dataGridViewTextBoxColumn10
            // 
            dataGridViewTextBoxColumn10.HeaderText = "الرقم الإحصائي";
            dataGridViewTextBoxColumn10.Name = "dataGridViewTextBoxColumn10";
            dataGridViewTextBoxColumn10.ReadOnly = true;
            dataGridViewTextBoxColumn10.Width = 60;
            // 
            // dataGridViewTextBoxColumn11
            // 
            dataGridViewTextBoxColumn11.HeaderText = "رقم الكي كارد";
            dataGridViewTextBoxColumn11.Name = "dataGridViewTextBoxColumn11";
            dataGridViewTextBoxColumn11.ReadOnly = true;
            dataGridViewTextBoxColumn11.Width = 61;
            // 
            // dataGridViewTextBoxColumn12
            // 
            dataGridViewTextBoxColumn12.HeaderText = "رقم الهاتف";
            dataGridViewTextBoxColumn12.Name = "dataGridViewTextBoxColumn12";
            dataGridViewTextBoxColumn12.ReadOnly = true;
            dataGridViewTextBoxColumn12.Width = 60;
            // 
            // dataGridViewTextBoxColumn14
            // 
            dataGridViewTextBoxColumn14.HeaderText = "رقم الباج";
            dataGridViewTextBoxColumn14.Name = "dataGridViewTextBoxColumn14";
            dataGridViewTextBoxColumn14.ReadOnly = true;
            dataGridViewTextBoxColumn14.Width = 60;
            // 
            // dataGridViewTextBoxColumn15
            // 
            dataGridViewTextBoxColumn15.HeaderText = "تاريخ انتهاء الباج";
            dataGridViewTextBoxColumn15.Name = "dataGridViewTextBoxColumn15";
            dataGridViewTextBoxColumn15.ReadOnly = true;
            dataGridViewTextBoxColumn15.Width = 60;
            // 
            // btnPrintSelected
            // 
            btnPrintSelected.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnPrintSelected.BackColor = Color.FromArgb(45, 66, 91);
            btnPrintSelected.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnPrintSelected.ForeColor = Color.White;
            btnPrintSelected.Image = Properties.Resources.print_32px;
            btnPrintSelected.ImageAlign = ContentAlignment.MiddleLeft;
            btnPrintSelected.Location = new Point(534, 404);
            btnPrintSelected.Margin = new Padding(3, 2, 3, 2);
            btnPrintSelected.Name = "btnPrintSelected";
            btnPrintSelected.Size = new Size(105, 41);
            btnPrintSelected.TabIndex = 27;
            btnPrintSelected.Text = "طباعة";
            btnPrintSelected.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(btnPrintSelected, "طباعة الموظف");
            btnPrintSelected.UseVisualStyleBackColor = false;
            btnPrintSelected.Click += btnPrintSelected_Click;
            // 
            // btnPrintAll
            // 
            btnPrintAll.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnPrintAll.BackColor = Color.FromArgb(45, 66, 91);
            btnPrintAll.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnPrintAll.ForeColor = Color.White;
            btnPrintAll.Image = Properties.Resources.print_32px;
            btnPrintAll.ImageAlign = ContentAlignment.MiddleLeft;
            btnPrintAll.Location = new Point(435, 404);
            btnPrintAll.Margin = new Padding(3, 2, 3, 2);
            btnPrintAll.Name = "btnPrintAll";
            btnPrintAll.Size = new Size(93, 41);
            btnPrintAll.TabIndex = 28;
            btnPrintAll.Text = " الكل";
            btnPrintAll.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(btnPrintAll, "طباعة كل الموظفين");
            btnPrintAll.UseVisualStyleBackColor = false;
            btnPrintAll.Click += btnPrintAll_Click;
            // 
            // btnEdit
            // 
            btnEdit.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnEdit.BackColor = Color.FromArgb(45, 66, 91);
            btnEdit.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnEdit.ForeColor = Color.White;
            btnEdit.Image = Properties.Resources.edit_profile_32px1;
            btnEdit.ImageAlign = ContentAlignment.MiddleLeft;
            btnEdit.Location = new Point(988, 403);
            btnEdit.Margin = new Padding(3, 2, 3, 2);
            btnEdit.Name = "btnEdit";
            btnEdit.Size = new Size(97, 42);
            btnEdit.TabIndex = 23;
            btnEdit.Text = "تعديل";
            btnEdit.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(btnEdit, "تعديل");
            btnEdit.UseVisualStyleBackColor = false;
            btnEdit.Click += btnEdit_Click;
            // 
            // btnClear
            // 
            btnClear.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnClear.BackColor = Color.FromArgb(192, 0, 0);
            btnClear.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnClear.ForeColor = Color.White;
            btnClear.Image = Properties.Resources.clear_formatting_32px;
            btnClear.ImageAlign = ContentAlignment.MiddleLeft;
            btnClear.Location = new Point(797, 403);
            btnClear.Name = "btnClear";
            btnClear.Size = new Size(95, 42);
            btnClear.TabIndex = 25;
            btnClear.Text = "إفراغ ";
            btnClear.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(btnClear, "إفراغ الحقول");
            btnClear.UseVisualStyleBackColor = false;
            btnClear.Click += btnClear_Click;
            // 
            // btnAddPhoto
            // 
            btnAddPhoto.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnAddPhoto.BackColor = Color.FromArgb(45, 66, 91);
            btnAddPhoto.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnAddPhoto.ForeColor = Color.White;
            btnAddPhoto.Image = Properties.Resources.image_file_add_32px;
            btnAddPhoto.ImageAlign = ContentAlignment.MiddleLeft;
            btnAddPhoto.Location = new Point(303, 404);
            btnAddPhoto.Margin = new Padding(3, 2, 3, 2);
            btnAddPhoto.Name = "btnAddPhoto";
            btnAddPhoto.Size = new Size(97, 41);
            btnAddPhoto.TabIndex = 19;
            btnAddPhoto.Text = "إضافة ";
            btnAddPhoto.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(btnAddPhoto, "إضافة  ملف");
            btnAddPhoto.UseVisualStyleBackColor = false;
            btnAddPhoto.Click += btnAddPhoto_Click;
            // 
            // lblName
            // 
            lblName.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblName.AutoSize = true;
            lblName.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblName.Location = new Point(1127, 65);
            lblName.Name = "lblName";
            lblName.Size = new Size(53, 30);
            lblName.TabIndex = 18;
            lblName.Text = "الاسم";
            // 
            // lblMotherName
            // 
            lblMotherName.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblMotherName.AutoSize = true;
            lblMotherName.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblMotherName.Location = new Point(1108, 105);
            lblMotherName.Name = "lblMotherName";
            lblMotherName.Size = new Size(72, 30);
            lblMotherName.TabIndex = 19;
            lblMotherName.Text = "اسم الأم";
            // 
            // lblProvince
            // 
            lblProvince.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblProvince.AutoSize = true;
            lblProvince.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblProvince.Location = new Point(1096, 231);
            lblProvince.Name = "lblProvince";
            lblProvince.Size = new Size(84, 30);
            lblProvince.TabIndex = 20;
            lblProvince.Text = "المحافظة";
            // 
            // lblNationality
            // 
            lblNationality.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblNationality.AutoSize = true;
            lblNationality.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblNationality.Location = new Point(1108, 274);
            lblNationality.Name = "lblNationality";
            lblNationality.Size = new Size(72, 30);
            lblNationality.TabIndex = 21;
            lblNationality.Text = "الجنسية";
            // 
            // lblIdentityNumber
            // 
            lblIdentityNumber.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblIdentityNumber.AutoSize = true;
            lblIdentityNumber.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblIdentityNumber.Location = new Point(1087, 316);
            lblIdentityNumber.Name = "lblIdentityNumber";
            lblIdentityNumber.Size = new Size(93, 30);
            lblIdentityNumber.TabIndex = 22;
            lblIdentityNumber.Text = "رقم الهوية";
            // 
            // lblAdminOrder
            // 
            lblAdminOrder.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblAdminOrder.AutoSize = true;
            lblAdminOrder.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblAdminOrder.Location = new Point(665, 105);
            lblAdminOrder.Name = "lblAdminOrder";
            lblAdminOrder.Size = new Size(128, 30);
            lblAdminOrder.TabIndex = 23;
            lblAdminOrder.Text = "رقم الامر الاداري";
            // 
            // lblStatisticalNumber
            // 
            lblStatisticalNumber.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblStatisticalNumber.AutoSize = true;
            lblStatisticalNumber.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblStatisticalNumber.Location = new Point(674, 190);
            lblStatisticalNumber.Name = "lblStatisticalNumber";
            lblStatisticalNumber.Size = new Size(119, 30);
            lblStatisticalNumber.TabIndex = 24;
            lblStatisticalNumber.Text = "الرقم الإحصائي";
            // 
            // lblKeyCard
            // 
            lblKeyCard.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblKeyCard.AutoSize = true;
            lblKeyCard.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblKeyCard.Location = new Point(677, 232);
            lblKeyCard.Name = "lblKeyCard";
            lblKeyCard.Size = new Size(116, 30);
            lblKeyCard.TabIndex = 25;
            lblKeyCard.Text = "رقم الكي كارت";
            // 
            // lblPhone
            // 
            lblPhone.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblPhone.AutoSize = true;
            lblPhone.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblPhone.Location = new Point(699, 274);
            lblPhone.Name = "lblPhone";
            lblPhone.Size = new Size(94, 30);
            lblPhone.TabIndex = 26;
            lblPhone.Text = "رقم الهاتف";
            // 
            // lblCategory
            // 
            lblCategory.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblCategory.AutoSize = true;
            lblCategory.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblCategory.Location = new Point(1119, 21);
            lblCategory.Name = "lblCategory";
            lblCategory.Size = new Size(61, 30);
            lblCategory.TabIndex = 27;
            lblCategory.Text = "الصنف";
            // 
            // lblBadgeNumber
            // 
            lblBadgeNumber.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblBadgeNumber.AutoSize = true;
            lblBadgeNumber.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblBadgeNumber.Location = new Point(719, 316);
            lblBadgeNumber.Name = "lblBadgeNumber";
            lblBadgeNumber.Size = new Size(74, 30);
            lblBadgeNumber.TabIndex = 28;
            lblBadgeNumber.Text = "رقم الباج";
            // 
            // lblDateOfBirth
            // 
            lblDateOfBirth.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblDateOfBirth.AutoSize = true;
            lblDateOfBirth.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblDateOfBirth.Location = new Point(1084, 355);
            lblDateOfBirth.Name = "lblDateOfBirth";
            lblDateOfBirth.Size = new Size(96, 30);
            lblDateOfBirth.TabIndex = 29;
            lblDateOfBirth.Text = "تاريخ الميلاد";
            // 
            // lblStartDate
            // 
            lblStartDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblStartDate.AutoSize = true;
            lblStartDate.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblStartDate.Location = new Point(659, 149);
            lblStartDate.Name = "lblStartDate";
            lblStartDate.Size = new Size(134, 30);
            lblStartDate.TabIndex = 30;
            lblStartDate.Text = "تاريخ الامر الاداري";
            // 
            // lblBadgeExpiry
            // 
            lblBadgeExpiry.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblBadgeExpiry.AutoSize = true;
            lblBadgeExpiry.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblBadgeExpiry.Location = new Point(665, 360);
            lblBadgeExpiry.Name = "lblBadgeExpiry";
            lblBadgeExpiry.Size = new Size(128, 30);
            lblBadgeExpiry.TabIndex = 31;
            lblBadgeExpiry.Text = "تاريخ انتهاء الباج";
            // 
            // txtSearch
            // 
            txtSearch.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtSearch.Font = new Font("Cairo", 14F);
            txtSearch.Location = new Point(597, 453);
            txtSearch.Name = "txtSearch";
            txtSearch.PlaceholderText = "ابحث عن موظف...";
            txtSearch.RightToLeft = RightToLeft.Yes;
            txtSearch.Size = new Size(386, 42);
            txtSearch.TabIndex = 32;
            toolTip1.SetToolTip(txtSearch, "البحث عن موظف");
            txtSearch.KeyDown += txtSearch_KeyDown;
            // 
            // previewBox
            // 
            previewBox.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            previewBox.BackColor = Color.White;
            previewBox.BorderStyle = BorderStyle.FixedSingle;
            previewBox.Image = Properties.Resources.picture;
            previewBox.Location = new Point(7, 17);
            previewBox.Name = "previewBox";
            previewBox.Size = new Size(380, 375);
            previewBox.SizeMode = PictureBoxSizeMode.StretchImage;
            previewBox.TabIndex = 1;
            previewBox.TabStop = false;
            // 
            // button1
            // 
            button1.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            button1.BackColor = Color.FromArgb(45, 66, 91);
            button1.Font = new Font("Cairo", 12F, FontStyle.Bold);
            button1.ForeColor = Color.White;
            button1.Image = Properties.Resources.microsoft_excel_2019_32px;
            button1.ImageAlign = ContentAlignment.MiddleLeft;
            button1.Location = new Point(645, 404);
            button1.Margin = new Padding(3, 2, 3, 2);
            button1.Name = "button1";
            button1.Size = new Size(105, 41);
            button1.TabIndex = 26;
            button1.Text = "تصدير";
            button1.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(button1, "تصدير");
            button1.UseVisualStyleBackColor = false;
            button1.Click += button1_Click;
            // 
            // btn_Scan
            // 
            btn_Scan.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btn_Scan.BackColor = Color.FromArgb(45, 66, 91);
            btn_Scan.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btn_Scan.ForeColor = Color.White;
            btn_Scan.Image = Properties.Resources.scanner_32px;
            btn_Scan.ImageAlign = ContentAlignment.MiddleLeft;
            btn_Scan.Location = new Point(321, 455);
            btn_Scan.Margin = new Padding(3, 2, 3, 2);
            btn_Scan.Name = "btn_Scan";
            btn_Scan.Size = new Size(110, 41);
            btn_Scan.TabIndex = 29;
            btn_Scan.Text = "الماسح";
            btn_Scan.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(btn_Scan, "تشغيل الماسح الضوئي");
            btn_Scan.UseVisualStyleBackColor = false;
            btn_Scan.Click += btn_Scan_Click;
            // 
            // listOfScanner
            // 
            listOfScanner.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            listOfScanner.DropDownStyle = ComboBoxStyle.DropDownList;
            listOfScanner.Font = new Font("Cairo", 12F, FontStyle.Bold);
            listOfScanner.Location = new Point(7, 457);
            listOfScanner.Name = "listOfScanner";
            listOfScanner.RightToLeft = RightToLeft.No;
            listOfScanner.Size = new Size(248, 38);
            listOfScanner.TabIndex = 31;
            toolTip1.SetToolTip(listOfScanner, "اختيار جهاز الماسح الضوئي");
            // 
            // button2
            // 
            button2.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            button2.BackColor = Color.FromArgb(45, 66, 91);
            button2.FlatAppearance.BorderSize = 0;
            button2.Font = new Font("Cairo", 12F, FontStyle.Bold);
            button2.ForeColor = Color.White;
            button2.Image = Properties.Resources.available_updates_32px;
            button2.Location = new Point(261, 455);
            button2.Margin = new Padding(3, 2, 3, 2);
            button2.Name = "button2";
            button2.Size = new Size(54, 41);
            button2.TabIndex = 30;
            button2.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(button2, "تحديث اجهزة الماسح الضوئي");
            button2.UseVisualStyleBackColor = false;
            button2.Click += button2_Click;
            // 
            // btnViewPhoto
            // 
            btnViewPhoto.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnViewPhoto.BackColor = Color.FromArgb(45, 66, 91);
            btnViewPhoto.FlatAppearance.BorderSize = 0;
            btnViewPhoto.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnViewPhoto.ForeColor = Color.White;
            btnViewPhoto.Image = Properties.Resources.picture_32px;
            btnViewPhoto.ImageAlign = ContentAlignment.MiddleLeft;
            btnViewPhoto.Location = new Point(110, 404);
            btnViewPhoto.Margin = new Padding(3, 2, 3, 2);
            btnViewPhoto.Name = "btnViewPhoto";
            btnViewPhoto.Size = new Size(93, 41);
            btnViewPhoto.TabIndex = 35;
            btnViewPhoto.Text = "عرض ";
            btnViewPhoto.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(btnViewPhoto, "عرض  الملف");
            btnViewPhoto.UseVisualStyleBackColor = false;
            btnViewPhoto.Click += btnViewPhoto_Click;
            // 
            // btnOpenCamera
            // 
            btnOpenCamera.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnOpenCamera.BackColor = Color.FromArgb(45, 66, 91);
            btnOpenCamera.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnOpenCamera.ForeColor = Color.White;
            btnOpenCamera.Image = Properties.Resources.camera_32px;
            btnOpenCamera.ImageAlign = ContentAlignment.MiddleLeft;
            btnOpenCamera.Location = new Point(211, 404);
            btnOpenCamera.Margin = new Padding(3, 2, 3, 2);
            btnOpenCamera.Name = "btnOpenCamera";
            btnOpenCamera.Size = new Size(84, 41);
            btnOpenCamera.TabIndex = 20;
            btnOpenCamera.Text = "فتح";
            btnOpenCamera.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(btnOpenCamera, "فتح الكامرة");
            btnOpenCamera.UseVisualStyleBackColor = false;
            btnOpenCamera.Click += btnOpenCamera_Click;
            // 
            // cmbMaritalStatus
            // 
            cmbMaritalStatus.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbMaritalStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbMaritalStatus.Font = new Font("Cairo", 12F, FontStyle.Bold);
            cmbMaritalStatus.Items.AddRange(new object[] { "اعزب", "متزوج/ـة", "مطلق/ـة", "أرمل/ـة" });
            cmbMaritalStatus.Location = new Point(801, 144);
            cmbMaritalStatus.Name = "cmbMaritalStatus";
            cmbMaritalStatus.Size = new Size(241, 38);
            cmbMaritalStatus.TabIndex = 3;
            cmbMaritalStatus.SelectedIndexChanged += cmbMaritalStatus_SelectedIndexChanged;
            // 
            // txtWifeName
            // 
            txtWifeName.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtWifeName.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtWifeName.Location = new Point(802, 187);
            txtWifeName.Name = "txtWifeName";
            txtWifeName.Size = new Size(240, 37);
            txtWifeName.TabIndex = 4;
            // 
            // txtElectoralNumber
            // 
            txtElectoralNumber.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtElectoralNumber.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtElectoralNumber.Location = new Point(392, 60);
            txtElectoralNumber.Name = "txtElectoralNumber";
            txtElectoralNumber.Size = new Size(241, 37);
            txtElectoralNumber.TabIndex = 10;
            // 
            // cmbEducationLevel
            // 
            cmbEducationLevel.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbEducationLevel.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbEducationLevel.Font = new Font("Cairo", 12F, FontStyle.Bold);
            cmbEducationLevel.Items.AddRange(new object[] { "امي", "يقرأ ويكتب", "ابتدائية", "متوسطة", "اعدادية", "دبلوم", "بكالوريوس", "ماجستير", "دكتوراه" });
            cmbEducationLevel.Location = new Point(392, 17);
            cmbEducationLevel.Name = "cmbEducationLevel";
            cmbEducationLevel.Size = new Size(242, 38);
            cmbEducationLevel.TabIndex = 9;
            // 
            // label2
            // 
            label2.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label2.AutoSize = true;
            label2.Font = new Font("Cairo", 12F, FontStyle.Bold);
            label2.Location = new Point(670, 63);
            label2.Name = "label2";
            label2.Size = new Size(123, 30);
            label2.TabIndex = 29;
            label2.Text = " الرقم الانتخابي";
            // 
            // label3
            // 
            label3.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label3.AutoSize = true;
            label3.Font = new Font("Cairo", 12F, FontStyle.Bold);
            label3.Location = new Point(640, 21);
            label3.Name = "label3";
            label3.Size = new Size(153, 30);
            label3.TabIndex = 29;
            label3.Text = "المستوى التعليمي";
            // 
            // label4
            // 
            label4.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label4.AutoSize = true;
            label4.Font = new Font("Cairo", 12F, FontStyle.Bold);
            label4.Location = new Point(1047, 149);
            label4.Name = "label4";
            label4.Size = new Size(133, 30);
            label4.TabIndex = 29;
            label4.Text = "الحالة الاجتماعية";
            // 
            // label5
            // 
            label5.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label5.AutoSize = true;
            label5.Font = new Font("Cairo", 12F, FontStyle.Bold);
            label5.Location = new Point(1081, 190);
            label5.Name = "label5";
            label5.Size = new Size(104, 30);
            label5.TabIndex = 29;
            label5.Text = "اسم الزوج/ـة";
            // 
            // panel1
            // 
            panel1.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            panel1.Controls.Add(dgvEmployees);
            panel1.Location = new Point(2, 501);
            panel1.Name = "panel1";
            panel1.Size = new Size(1183, 331);
            panel1.TabIndex = 34;
            // 
            // lbl_NoDocuments
            // 
            lbl_NoDocuments.Anchor = AnchorStyles.None;
            lbl_NoDocuments.AutoSize = true;
            lbl_NoDocuments.Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lbl_NoDocuments.Location = new Point(573, 629);
            lbl_NoDocuments.Name = "lbl_NoDocuments";
            lbl_NoDocuments.Size = new Size(54, 30);
            lbl_NoDocuments.TabIndex = 34;
            lbl_NoDocuments.Text = "label6";
            lbl_NoDocuments.Visible = false;
            // 
            // btnSearch
            // 
            btnSearch.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnSearch.Font = new Font("Cairo", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnSearch.Image = Properties.Resources.search_32px;
            btnSearch.ImageAlign = ContentAlignment.MiddleLeft;
            btnSearch.Location = new Point(982, 449);
            btnSearch.Name = "btnSearch";
            btnSearch.Size = new Size(101, 49);
            btnSearch.TabIndex = 33;
            btnSearch.Text = "بحث";
            btnSearch.TextAlign = ContentAlignment.MiddleRight;
            btnSearch.UseVisualStyleBackColor = true;
            btnSearch.Click += btnSearch_Click;
            // 
            // btnRefresh
            // 
            btnRefresh.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnRefresh.BackColor = Color.FromArgb(45, 66, 91);
            btnRefresh.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnRefresh.ForeColor = Color.White;
            btnRefresh.Image = Properties.Resources.update_32px;
            btnRefresh.ImageAlign = ContentAlignment.MiddleLeft;
            btnRefresh.Location = new Point(1091, 448);
            btnRefresh.Margin = new Padding(3, 2, 3, 2);
            btnRefresh.Name = "btnRefresh";
            btnRefresh.Size = new Size(94, 49);
            btnRefresh.TabIndex = 34;
            btnRefresh.Text = "تحديث";
            btnRefresh.TextAlign = ContentAlignment.MiddleRight;
            btnRefresh.UseVisualStyleBackColor = false;
            btnRefresh.Click += btnRefresh_Click;
            // 
            // Form1
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1200, 700);
            Controls.Add(btnSearch);
            Controls.Add(lbl_NoDocuments);
            Controls.Add(chkSelectAll);
            Controls.Add(panel1);
            Controls.Add(btnViewPhoto);
            Controls.Add(button2);
            Controls.Add(listOfScanner);
            Controls.Add(txtName);
            Controls.Add(txtMotherName);
            Controls.Add(txtProvince);
            Controls.Add(txtNationality);
            Controls.Add(txtIdentityNumber);
            Controls.Add(txtAdminOrder);
            Controls.Add(txtStatisticalNumber);
            Controls.Add(txtKeyCard);
            Controls.Add(txtPhone);
            Controls.Add(txtCategory);
            Controls.Add(txtBadgeNumber);
            Controls.Add(dtpDateOfBirth);
            Controls.Add(dtpStartDate);
            Controls.Add(dtpBadgeExpiry);
            Controls.Add(btnRefresh);
            Controls.Add(btnSave);
            Controls.Add(btnDelete);
            Controls.Add(btnDocuments);
            Controls.Add(lblName);
            Controls.Add(lblMotherName);
            Controls.Add(lblProvince);
            Controls.Add(lblNationality);
            Controls.Add(lblIdentityNumber);
            Controls.Add(lblAdminOrder);
            Controls.Add(lblStatisticalNumber);
            Controls.Add(lblKeyCard);
            Controls.Add(lblPhone);
            Controls.Add(lblCategory);
            Controls.Add(lblBadgeNumber);
            Controls.Add(label5);
            Controls.Add(label4);
            Controls.Add(label3);
            Controls.Add(label2);
            Controls.Add(lblDateOfBirth);
            Controls.Add(lblStartDate);
            Controls.Add(lblBadgeExpiry);
            Controls.Add(btn_Scan);
            Controls.Add(button1);
            Controls.Add(btnPrintSelected);
            Controls.Add(btnOpenCamera);
            Controls.Add(btnPrintAll);
            Controls.Add(btnEdit);
            Controls.Add(btnClear);
            Controls.Add(btnAddPhoto);
            Controls.Add(txtSearch);
            Controls.Add(previewBox);
            Controls.Add(cmbMaritalStatus);
            Controls.Add(txtWifeName);
            Controls.Add(txtElectoralNumber);
            Controls.Add(cmbEducationLevel);
            Margin = new Padding(3, 2, 3, 2);
            Name = "Form1";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "نظام ادارة الموظفين";
            Load += Form1_Load;
            ((System.ComponentModel.ISupportInitialize)dgvEmployees).EndInit();
            ((System.ComponentModel.ISupportInitialize)previewBox).EndInit();
            panel1.ResumeLayout(false);
            ResumeLayout(false);
            PerformLayout();
        }

        private System.Windows.Forms.ComboBox cmbMaritalStatus;
        private System.Windows.Forms.ComboBox cmbEducationLevel;
        private System.Windows.Forms.TextBox txtWifeName;
        private System.Windows.Forms.TextBox txtElectoralNumber;
        private System.Windows.Forms.TextBox txtName;
        private System.Windows.Forms.TextBox txtMotherName;
        private System.Windows.Forms.TextBox txtProvince;
        private System.Windows.Forms.TextBox txtNationality;
        private System.Windows.Forms.TextBox txtIdentityNumber;
        private System.Windows.Forms.TextBox txtAdminOrder;
        private System.Windows.Forms.TextBox txtStatisticalNumber;
        private System.Windows.Forms.TextBox txtKeyCard;
        private System.Windows.Forms.TextBox txtPhone;
        private System.Windows.Forms.TextBox txtCategory;
        private System.Windows.Forms.TextBox txtBadgeNumber;
        private System.Windows.Forms.TextBox txtSearch;
        
        private System.Windows.Forms.DateTimePicker dtpDateOfBirth;
        private System.Windows.Forms.DateTimePicker dtpStartDate;
        private System.Windows.Forms.DateTimePicker dtpBadgeExpiry;
        
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.Button btnDelete;
        private System.Windows.Forms.Button btnDocuments;
        private System.Windows.Forms.Button btnEdit;
        private System.Windows.Forms.Button btnClear;
        private System.Windows.Forms.Button btnAddPhoto;
        
        private System.Windows.Forms.DataGridView dgvEmployees;
        private System.Windows.Forms.PictureBox previewBox;

        private System.Windows.Forms.Button btnPrintSelected;
        private System.Windows.Forms.Button btnPrintAll;

        private System.Windows.Forms.Label lblName;
        private System.Windows.Forms.Label lblMotherName;
        private System.Windows.Forms.Label lblProvince;
        private System.Windows.Forms.Label lblNationality;
        private System.Windows.Forms.Label lblIdentityNumber;
        private System.Windows.Forms.Label lblAdminOrder;
        private System.Windows.Forms.Label lblStatisticalNumber;
        private System.Windows.Forms.Label lblKeyCard;
        private System.Windows.Forms.Label lblPhone;
        private System.Windows.Forms.Label lblCategory;
        private System.Windows.Forms.Label lblBadgeNumber;
        private System.Windows.Forms.Label lblDateOfBirth;
        private System.Windows.Forms.Label lblStartDate;
        private System.Windows.Forms.Label lblBadgeExpiry;
        private Button button1;
        private Button btn_Scan;
        private ComboBox listOfScanner;
        private Button button2;
        private Button btnViewPhoto;
        private Button btnOpenCamera;
        private Label label2;
        private Label label3;
        private Label label4;
        private Label label5;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn1;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn13;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn2;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn3;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn16;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn17;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn6;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn5;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn7;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn4;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn19;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn18;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn9;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn8;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn10;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn11;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn12;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn14;
        private DataGridViewTextBoxColumn dataGridViewTextBoxColumn15;
        private Panel panel1;
        private ToolTip toolTip1;
        private Label lbl_NoDocuments;
        private Button btnSearch;
        private Button btnRefresh;
    }
}
