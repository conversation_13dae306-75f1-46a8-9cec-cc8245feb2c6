using System;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.VisualBasic;

namespace EmployeeManagementSystem
{
    public partial class VacationRequestsForm : Form
    {
        private int? selectedRequestId = null;
        private User? currentUser = null; // إضافة المستخدم الحالي

        public VacationRequestsForm()
        {
            InitializeComponent();
            LoadVacationRequests();

            // تسجيل فتح النموذج
            _ = Task.Run(async () => await ActivityLogHelper.LogFormAccessAsync("فتح نموذج طلبات الإجازات"));

            // تسجيل إغلاق النموذج عند الإغلاق
            this.FormClosing += async (sender, e) =>
            {
                await ActivityLogHelper.LogFormAccessAsync("إغلاق نموذج طلبات الإجازات");
            };
        }

        // Constructor جديد يستقبل المستخدم الحالي
        public VacationRequestsForm(User currentUser) : this()
        {
            this.currentUser = currentUser;

            // تم إزالة الرسالة المزعجة - الفلترة تعمل تلقائياً

                // تحديث عنوان النافذة
                this.Text = "🔒 طلبات الإجازات - مدير القسم";
            }
            else if (EmployeeDepartmentHelper.IsGeneralManager(currentUser))
            {
                this.Text = "🌐 طلبات الإجازات - مدير عام";
            }

            // تطبيق الثيم
            ThemeManager.ApplyThemeToForm(this);
        }

        private void LoadVacationRequests()
        {
            try
            {
                // جلب جميع الإجازات التي في حالة "في الانتظار" مع تطبيق الفلترة
                var table = DatabaseHelper.GetPendingVacationRequests();

                // تطبيق فلترة إضافية لمدير القسم
                if (EmployeeDepartmentHelper.IsDepartmentManager(currentUser))
                {
                    var accessibleCodes = EmployeeDepartmentHelper.GetAccessibleEmployeeCodes(currentUser);
                    var filteredTable = table.Clone();

                    foreach (DataRow row in table.Rows)
                    {
                        if (int.TryParse(row["EmployeeCode"]?.ToString(), out int empCode) &&
                            accessibleCodes.Contains(empCode))
                        {
                            filteredTable.ImportRow(row);
                        }
                    }
                    table = filteredTable;
                }

                // تعيين عناوين الأعمدة بالعربية
                foreach (DataColumn column in table.Columns)
                {
                    switch (column.ColumnName)
                    {
                        case "VacationId":
                            column.ColumnName = "رقم الطلب";
                            break;
                        case "EmployeeName":
                            column.ColumnName = "اسم الموظف";
                            break;
                        case "VacationType":
                            column.ColumnName = "نوع الإجازة";
                            break;
                        case "StartDate":
                            column.ColumnName = "تاريخ البداية";
                            break;
                        case "EndDate":
                            column.ColumnName = "تاريخ النهاية";
                            break;
                        case "DaysCount":
                            column.ColumnName = "عدد الأيام";
                            break;
                        case "Reason":
                            column.ColumnName = "السبب";
                            break;
                        case "Status":
                            column.ColumnName = "الحالة";
                            break;
                    }
                }

                // تنظيف البيانات
                foreach (DataRow row in table.Rows)
                {
                    // تحويل التواريخ
                    if (DateTime.TryParse(row["تاريخ البداية"].ToString(), out DateTime startDate))
                    {
                        row["تاريخ البداية"] = startDate;
                    }

                    if (DateTime.TryParse(row["تاريخ النهاية"].ToString(), out DateTime endDate))
                    {
                        row["تاريخ النهاية"] = endDate;
                    }

                    // تنظيف الحالة
                    if (row["الحالة"] == DBNull.Value || string.IsNullOrEmpty(row["الحالة"].ToString()))
                    {
                        row["الحالة"] = "في الانتظار";
                    }
                }

                dataGridViewRequests.DataSource = table;
                dataGridViewRequests.Refresh();

                // تعيين رؤوس الأعمدة
                SetArabicColumnHeaders();

                // تحديث عدد الطلبات
                UpdateRequestsCount();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل الطلبات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetArabicColumnHeaders()
        {
            try
            {
                if (dataGridViewRequests.Columns.Count == 0)
                    return; // لا توجد أعمدة، نخرج مباشرة

                foreach (DataGridViewColumn column in dataGridViewRequests.Columns)
                {
                    if (column == null) continue; // تحقق إضافي احتياطي

                    string columnName = column.DataPropertyName ?? column.Name;

                    switch (columnName)
                    {
                        case "VacationId":
                            column.HeaderText = "رقم الطلب";
                            column.Width = 80;
                            break;
                        case "EmployeeName":
                            column.HeaderText = "اسم الموظف";
                            column.Width = 150;
                            break;
                        case "VacationType":
                            column.HeaderText = "نوع الإجازة";
                            column.Width = 120;
                            break;
                        case "StartDate":
                            column.HeaderText = "تاريخ البداية";
                            column.Width = 100;
                            break;
                        case "EndDate":
                            column.HeaderText = "تاريخ النهاية";
                            column.Width = 100;
                            break;
                        case "DaysCount":
                            column.HeaderText = "عدد الأيام";
                            column.Width = 80;
                            break;
                        case "Reason":
                            column.HeaderText = "السبب";
                            column.Width = 200;
                            break;
                        case "Status":
                            column.HeaderText = "الحالة";
                            column.Width = 100;
                            break;
                        default:
                            // عمود غير معروف، يمكن تجاهله أو تعيين اسم افتراضي
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء إعداد الأعمدة: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        private void UpdateRequestsCount()
        {
            int count = dataGridViewRequests.Rows.Count;
            lblRequestsCount.Text = $"عدد الطلبات المعلقة: {count}";
        }

        private void dataGridViewRequests_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (dataGridViewRequests.CurrentRow != null)
            {
                try
                {
                    selectedRequestId = Convert.ToInt32(dataGridViewRequests.CurrentRow.Cells["رقم الطلب"].Value);
                    
                    if (selectedRequestId.HasValue)
                    {
                        var vacation = DatabaseHelper.GetVacationById(selectedRequestId.Value);
                        
                        // عرض تفاصيل الطلب في الحقول
                        lblEmployeeName.Text = $"الموظف: {vacation.EmployeeName}";
                        lblVacationType.Text = $"نوع الإجازة: {vacation.VacationType}";
                        lblStartDate.Text = $"من: {vacation.StartDate:yyyy/MM/dd}";
                        lblEndDate.Text = $"إلى: {vacation.EndDate:yyyy/MM/dd}";
                        lblDaysCount.Text = $"عدد الأيام: {vacation.DaysCount}";
                        lblReason.Text = $"السبب: {vacation.Reason}";
                        
                        // تفعيل أزرار الموافقة والرفض والطباعة
                        btnApprove.Enabled = true;
                        btnReject.Enabled = true;
                        btnPrintRequest.Enabled = true;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحديد الصف: {ex.Message}");
                }
            }
        }

        // دالة الموافقة على الطلب
        private async void btnApprove_Click(object sender, EventArgs e)
        {
            try
            {
                if (!selectedRequestId.HasValue)
                {
                    MessageBox.Show("الرجاء اختيار طلب للموافقة عليه", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var vacation = DatabaseHelper.GetVacationById(selectedRequestId.Value);
                
                if (vacation.Status == "معتمد")
                {
                    MessageBox.Show("هذا الطلب معتمد مسبقاً", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = MessageBox.Show($"هل أنت متأكد من الموافقة على إجازة الموظف: {vacation.EmployeeName}؟", 
                    "تأكيد الموافقة", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    string currentUser = Environment.UserName;

                    await Task.Run(() => DatabaseHelper.ApproveVacation(selectedRequestId.Value, currentUser));

                    // تسجيل عملية الموافقة
                    var description = $"الموافقة على إجازة للموظف: {vacation.EmployeeName}";
                    await ActivityLogHelper.LogUpdateOperationAsync(
                        description,
                        "Vacations",
                        selectedRequestId.Value,
                        vacation,
                        vacation,
                        "مهم"
                    );

                    MessageBox.Show("تم اعتماد الإجازة بنجاح", "نجاح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    LoadVacationRequests();
                    ClearSelection();
                    ToastHelper.ShowEditToast();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الموافقة على الإجازة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // دالة رفض الطلب
        private async void btnReject_Click(object sender, EventArgs e)
        {
            try
            {
                if (!selectedRequestId.HasValue)
                {
                    MessageBox.Show("الرجاء اختيار طلب للرفض", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var vacation = DatabaseHelper.GetVacationById(selectedRequestId.Value);
                
                if (vacation.Status == "مرفوض")
                {
                    MessageBox.Show("هذا الطلب مرفوض مسبقاً", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // طلب سبب الرفض
                string rejectionReason = Microsoft.VisualBasic.Interaction.InputBox(
                    "الرجاء إدخال سبب رفض الإجازة:",
                    "سبب الرفض",
                    "");

                if (string.IsNullOrWhiteSpace(rejectionReason))
                {
                    MessageBox.Show("يجب إدخال سبب الرفض", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show($"هل أنت متأكد من رفض إجازة الموظف: {vacation.EmployeeName}؟", 
                    "تأكيد الرفض", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    string currentUser = Environment.UserName;

                    await Task.Run(() => DatabaseHelper.RejectVacation(selectedRequestId.Value, rejectionReason, currentUser));

                    // تسجيل عملية الرفض
                    var description = $"رفض إجازة للموظف: {vacation.EmployeeName} - السبب: {rejectionReason}";
                    await ActivityLogHelper.LogUpdateOperationAsync(
                        description,
                        "Vacations",
                        selectedRequestId.Value,
                        vacation,
                        vacation,
                        "مهم"
                    );

                    MessageBox.Show("تم رفض الإجازة بنجاح", "نجاح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    LoadVacationRequests();
                    ClearSelection();
                    ToastHelper.ShowEditToast();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء رفض الإجازة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearSelection()
        {
            selectedRequestId = null;
            lblEmployeeName.Text = "الموظف: -";
            lblVacationType.Text = "نوع الإجازة: -";
            lblStartDate.Text = "من: -";
            lblEndDate.Text = "إلى: -";
            lblDaysCount.Text = "عدد الأيام: -";
            lblReason.Text = "السبب: -";
            
            btnApprove.Enabled = false;
            btnReject.Enabled = false;
            btnPrintRequest.Enabled = false;
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadVacationRequests();
            ClearSelection();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        // دالة طباعة تفاصيل الطلب
        private async void btnPrintRequest_Click(object sender, EventArgs e)
        {
            try
            {
                if (!selectedRequestId.HasValue)
                {
                    MessageBox.Show("الرجاء اختيار طلب للطباعة", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var vacation = DatabaseHelper.GetVacationById(selectedRequestId.Value);

                // قراءة اسم المؤسسة من الإعدادات
                string companyName = "المؤسسة";
                try
                {
                    using (var connection = new SqlConnection(ConStringHelper.GetConnectionString()))
                    {
                        connection.Open();
                        string sql = "SELECT TOP 1 CompanyName FROM Settings";
                        using (var command = new SqlCommand(sql, connection))
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                companyName = reader["CompanyName"]?.ToString() ?? "المؤسسة";
                            }
                        }
                    }
                }
                catch
                {
                    companyName = "المؤسسة";
                }

                // جلب كود الموظف
                int employeeCode = 0;
                try
                {
                    employeeCode = DatabaseHelper.GetEmployeeCodeByName(vacation.EmployeeName);
                }
                catch
                {
                    employeeCode = 0;
                }

                // إنشاء HTML للطباعة الجميل والاحترافي - مطابق لتطبيق الويب
                string html = $@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>طلب إجازة - {vacation.EmployeeName}</title>
    <link href=""https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap"" rel=""stylesheet"">
    <style>
        body {{ 
            font-family: 'Cairo', Arial, sans-serif;
            margin: 0;
            padding: 10mm;
            direction: rtl;
            background-color: #ffffff;
            color: #333;
            line-height: 1.5;
            font-size: 11px;
        }}
        
        .document-container {{
            width: 100%;
            max-width: 190mm;
            margin: 0 auto;
            background: white;
            border: 2px solid #2c3e50;
            border-radius: 8px;
            overflow: hidden;
            page-break-inside: avoid;
        }}
        
        .header {{
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            text-align: center;
            padding: 8mm 10mm;
            position: relative;
        }}
        
        .header::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=""http://www.w3.org/2000/svg"" viewBox=""0 0 100 100""><defs><pattern id=""grain"" width=""100"" height=""100"" patternUnits=""userSpaceOnUse""><circle cx=""50"" cy=""50"" r=""1"" fill=""rgba(255,255,255,0.1)""/></pattern></defs><rect width=""100"" height=""100"" fill=""url(%23grain)""/></svg>');
            opacity: 0.3;
        }}
        
        .header-content {{
            position: relative;
            z-index: 1;
        }}
        
        .main-title {{
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 3mm 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .sub-title {{
            font-size: 13px;
            margin: 0 0 3mm 0;
            opacity: 0.9;
        }}
        
        .system-name {{
            font-size: 11px;
            margin: 0 0 3mm 0;
            opacity: 0.8;
        }}
        
        .print-date {{
            font-size: 10px;
            margin: 0;
            opacity: 0.7;
        }}
        
        .content {{
            padding: 8mm 10mm;
        }}
        
        .section {{
            margin-bottom: 8mm;
            background: #f8f9fa;
            border-radius: 6px;
            padding: 8mm;
            border-right: 3px solid #3498db;
        }}
        
        .section-title {{
            color: #2c3e50;
            font-size: 13px;
            font-weight: bold;
            margin: 0 0 5mm 0;
            padding-bottom: 2mm;
            border-bottom: 1px solid #3498db;
        }}
        
        .info-row {{
            display: flex;
            margin-bottom: 4mm;
            align-items: flex-start;
        }}
        
        .info-label {{
            font-weight: bold;
            color: #2c3e50;
            min-width: 35mm;
            margin-left: 5mm;
            font-size: 10px;
        }}
        
        .info-value {{
            color: #34495e;
            flex: 1;
            word-wrap: break-word;
            font-size: 10px;
        }}
        
        .status-badge {{
            display: inline-block;
            padding: 2mm 5mm;
            border-radius: 15px;
            font-weight: bold;
            font-size: 9px;
        }}
        
        .status-pending {{
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }}
        
        .status-approved {{
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }}
        
        .status-rejected {{
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }}
        
        .signatures-section {{
            margin-top: 8mm;
            padding: 8mm;
            background: #ecf0f1;
            border-radius: 6px;
        }}
        
        .signatures-title {{
            font-weight: bold;
            text-align: center;
            margin-bottom: 6mm;
            font-size: 12px;
            color: #2c3e50;
        }}
        
        .signatures-grid {{
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8mm;
        }}
        
        .signature-box {{
            text-align: center;
            padding: 8mm 5mm;
            border: 1px dashed #bdc3c7;
            border-radius: 6px;
            background: white;
        }}
        
        .signature-label {{
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 12mm;
            font-size: 9px;
        }}
        
        .signature-line {{
            border-top: 1px solid #34495e;
            margin-top: 12mm;
            padding-top: 2mm;
            font-size: 8px;
            color: #7f8c8d;
        }}
        
        .important-note {{
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 6mm;
            margin: 6mm 0;
            color: #0c5460;
            font-size: 9px;
        }}
        
        .footer {{
            background: #34495e;
            color: white;
            text-align: center;
            padding: 8mm;
            font-size: 9px;
        }}
        
        .request-number {{
            background: #e74c3c;
            color: white;
            padding: 3mm 6mm;
            border-radius: 15px;
            font-weight: bold;
            display: inline-block;
            margin-top: 4mm;
            font-size: 8px;
        }}
        
        @media print {{
            @@page {{
                size: A4;
                margin: 0;
            }}
            
            body {{
                margin: 0;
                padding: 8mm;
                background: white;
                width: 210mm;
                height: 297mm;
                page-break-after: always;
                font-size: 10px;
                line-height: 1.3;
            }}
            
            .document-container {{
                border: none;
                box-shadow: none;
                width: 100%;
                height: auto;
                max-height: 281mm;
                margin: 0;
                box-sizing: border-box;
                border-radius: 0;
            }}
            
            .header {{
                padding: 5mm;
            }}
            
            .main-title {{
                font-size: 16px;
                margin: 0 0 2mm 0;
            }}
            
            .sub-title {{
                font-size: 11px;
                margin: 0 0 2mm 0;
            }}
            
            .system-name {{
                font-size: 10px;
                margin: 0 0 2mm 0;
            }}
            
            .print-date {{
                font-size: 9px;
            }}
            
            .content {{
                padding: 5mm;
            }}
            
            .section {{
                padding: 5mm;
                margin-bottom: 5mm;
                page-break-inside: avoid;
            }}
            
            .section-title {{
                font-size: 11px;
                margin: 0 0 3mm 0;
                padding-bottom: 1mm;
            }}
            
            .info-row {{
                margin-bottom: 2mm;
            }}
            
            .info-label {{
                font-size: 9px;
                min-width: 30mm;
                margin-left: 3mm;
            }}
            
            .info-value {{
                font-size: 9px;
            }}
            
            .signatures-section {{
                margin-top: 5mm;
                padding: 5mm;
                page-break-inside: avoid;
            }}
            
            .signatures-title {{
                font-size: 10px;
                margin-bottom: 5mm;
            }}
            
            .signatures-grid {{
                gap: 5mm;
            }}
            
            .signature-box {{
                padding: 5mm 3mm;
            }}
            
            .signature-label {{
                font-size: 8px;
                margin-bottom: 8mm;
            }}
            
            .signature-line {{
                margin-top: 8mm;
                font-size: 7px;
            }}
            
            .footer {{
                padding: 5mm;
                font-size: 8px;
            }}
            
            .important-note {{
                font-size: 8px;
                padding: 4mm;
                margin: 4mm 0;
                page-break-inside: avoid;
            }}
            
            .status-badge {{
                font-size: 8px;
                padding: 1mm 3mm;
            }}
            
            .request-number {{
                font-size: 7px;
                padding: 2mm 4mm;
                margin-top: 3mm;
            }}
            
            .no-print {{
                display: none;
            }}
            
            /* ضبط الألوان للطباعة بالأبيض والأسود */
            .status-pending,
            .status-approved,
            .status-rejected {{
                background: #f8f9fa !important;
                color: #000 !important;
                border: 1px solid #000 !important;
            }}
            
            .important-note {{
                background: #f8f9fa !important;
                border: 1px solid #000 !important;
                color: #000 !important;
            }}
            
            .section {{
                background: transparent !important;
                border-right: 2px solid #000 !important;
            }}
            
            .section-title {{
                color: #000 !important;
                border-bottom: 1px solid #000 !important;
            }}
        }}
    </style>
</head>
<body>
    <div class=""document-container"">
        <div class=""header"">
            <div class=""header-content"">
                <div class=""main-title"">طلب إجازة</div>
                <div class=""sub-title"">نموذج طلب إجازة رسمي</div>
                <div class=""system-name"">{companyName}</div>
                <div class=""print-date"">تاريخ الطباعة: {DateTime.Now:dd/MM/yyyy HH:mm}</div>
            </div>
        </div>

        <div class=""content"">
            <div class=""section"">
                <div class=""section-title"">معلومات الموظف</div>
                <div class=""info-row"">
                    <div class=""info-label"">اسم الموظف:</div>
                    <div class=""info-value"">{vacation.EmployeeName}</div>
                </div>
                <div class=""info-row"">
                    <div class=""info-label"">كود الموظف:</div>
                    <div class=""info-value"">{(employeeCode > 0 ? employeeCode.ToString() : "غير محدد")}</div>
                </div>
            </div>

            <div class=""section"">
                <div class=""section-title"">تفاصيل الإجازة</div>
                <div class=""info-row"">
                    <div class=""info-label"">نوع الإجازة:</div>
                    <div class=""info-value"">{vacation.VacationType}</div>
                </div>
                <div class=""info-row"">
                    <div class=""info-label"">عدد الأيام:</div>
                    <div class=""info-value"">{vacation.DaysCount} يوم</div>
                </div>
                <div class=""info-row"">
                    <div class=""info-label"">تاريخ البداية:</div>
                    <div class=""info-value"">{vacation.StartDate:dd/MM/yyyy}</div>
                </div>
                <div class=""info-row"">
                    <div class=""info-label"">تاريخ النهاية:</div>
                    <div class=""info-value"">{vacation.EndDate:dd/MM/yyyy}</div>
                </div>
                <div class=""info-row"">
                    <div class=""info-label"">سبب الإجازة:</div>
                    <div class=""info-value"">{vacation.Reason}</div>
                </div>
            </div>

            <div class=""section"">
                <div class=""section-title"">معلومات الطلب</div>
                <div class=""info-row"">
                    <div class=""info-label"">تاريخ تقديم الطلب:</div>
                    <div class=""info-value"">{DateTime.Now:dd/MM/yyyy HH:mm}</div>
                </div>
                <div class=""info-row"">
                    <div class=""info-label"">حالة الطلب:</div>
                    <div class=""info-value"">
                        <span class=""status-badge {(vacation.Status == "في الانتظار" ? "status-pending" : vacation.Status == "معتمد" ? "status-approved" : "status-rejected")}"">{vacation.Status}</span>
                    </div>
                </div>
                <div class=""info-row"">
                    <div class=""info-label"">ملاحظات الإدارة:</div>
                    <div class=""info-value"">{(string.IsNullOrEmpty(vacation.RejectionReason) ? "قيد المراجعة" : vacation.RejectionReason)}</div>
                </div>
            </div>

            <div class=""important-note"">
                <strong>ملاحظة مهمة:</strong> هذا المستند تم إنشاؤه آلياً من نظام إدارة الموارد البشرية. للتحقق من صحة البيانات، يرجى مراجعة النظام الإلكتروني.
            </div>

            <div class=""signatures-section"">
                <div class=""signatures-title"">التوقيعات</div>
                <div class=""signatures-grid"">
                    <div class=""signature-box"">
                        <div class=""signature-label"">توقيع الموظف</div>
                        <div class=""signature-line"">التوقيع</div>
                    </div>
                    <div class=""signature-box"">
                        <div class=""signature-label"">توقيع المدير المباشر</div>
                        <div class=""signature-line"">التوقيع</div>
                    </div>
                    <div class=""signature-box"">
                        <div class=""signature-label"">توقيع الموارد البشرية</div>
                        <div class=""signature-line"">التوقيع</div>
                    </div>
                </div>
            </div>
        </div>

        <div class=""footer"">
            <div>{companyName} - تم إنشاء هذا المستند في {DateTime.Now:dd/MM/yyyy HH:mm}</div>
            <div class=""request-number"">رقم الطلب: VR-{vacation.VacationId}</div>
        </div>
    </div>
</body>
</html>";

                // إنشاء ملف HTML مؤقت وعرضه في المتصفح
                var tempFile = Path.GetTempFileName() + ".html";
                File.WriteAllText(tempFile, html);
                
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempFile,
                    UseShellExecute = true
                });

                // تسجيل عملية الطباعة
                await ActivityLogHelper.LogReportGenerationAsync(
                    $"طباعة طلب إجازة رقم {vacation.VacationId}",
                    "Vacations",
                    1,
                    "HTML",
                    $"الموظف: {vacation.EmployeeName}"
                );

                MessageBox.Show("تم فتح التقرير في المتصفح للطباعة", "نجاح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء طباعة الطلب: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}