-- ===================================================================
-- إعداد قاعدة البيانات لنظام فلترة الأقسام
-- ===================================================================

-- 1. التأكد من وجود الأعمدة المطلوبة في جدول Users
-- ===================================================================

-- إضافة عمود DepartmentId إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'DepartmentId')
BEGIN
    ALTER TABLE Users ADD DepartmentId INT NULL;
    PRINT 'تم إضافة عمود DepartmentId إلى جدول Users';
END
ELSE
BEGIN
    PRINT 'عمود DepartmentId موجود بالفعل في جدول Users';
END

-- إضافة عمود IsActive إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'IsActive')
BEGIN
    ALTER TABLE Users ADD IsActive BIT DEFAULT 1;
    PRINT 'تم إضافة عمود IsActive إلى جدول Users';
    
    -- تعيين قيمة افتراضية للسجلات الموجودة
    UPDATE Users SET IsActive = 1 WHERE IsActive IS NULL;
END
ELSE
BEGIN
    PRINT 'عمود IsActive موجود بالفعل في جدول Users';
END

-- 2. التأكد من وجود الأعمدة المطلوبة في جدول Departments
-- ===================================================================

-- إضافة عمود ManagerUserId إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Departments' AND COLUMN_NAME = 'ManagerUserId')
BEGIN
    ALTER TABLE Departments ADD ManagerUserId INT NULL;
    PRINT 'تم إضافة عمود ManagerUserId إلى جدول Departments';
END
ELSE
BEGIN
    PRINT 'عمود ManagerUserId موجود بالفعل في جدول Departments';
END

-- إضافة عمود IsActive إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Departments' AND COLUMN_NAME = 'IsActive')
BEGIN
    ALTER TABLE Departments ADD IsActive BIT DEFAULT 1;
    PRINT 'تم إضافة عمود IsActive إلى جدول Departments';
    
    -- تعيين قيمة افتراضية للسجلات الموجودة
    UPDATE Departments SET IsActive = 1 WHERE IsActive IS NULL;
END
ELSE
BEGIN
    PRINT 'عمود IsActive موجود بالفعل في جدول Departments';
END

-- إضافة عمود CreatedDate إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Departments' AND COLUMN_NAME = 'CreatedDate')
BEGIN
    ALTER TABLE Departments ADD CreatedDate DATETIME DEFAULT GETDATE();
    PRINT 'تم إضافة عمود CreatedDate إلى جدول Departments';
    
    -- تعيين قيمة افتراضية للسجلات الموجودة
    UPDATE Departments SET CreatedDate = GETDATE() WHERE CreatedDate IS NULL;
END
ELSE
BEGIN
    PRINT 'عمود CreatedDate موجود بالفعل في جدول Departments';
END

-- إضافة عمود CreatedBy إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Departments' AND COLUMN_NAME = 'CreatedBy')
BEGIN
    ALTER TABLE Departments ADD CreatedBy INT DEFAULT 1;
    PRINT 'تم إضافة عمود CreatedBy إلى جدول Departments';
    
    -- تعيين قيمة افتراضية للسجلات الموجودة
    UPDATE Departments SET CreatedBy = 1 WHERE CreatedBy IS NULL;
END
ELSE
BEGIN
    PRINT 'عمود CreatedBy موجود بالفعل في جدول Departments';
END

-- 3. التأكد من وجود الأعمدة المطلوبة في جدول Employees
-- ===================================================================

-- إضافة عمود DepartmentId إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = 'DepartmentId')
BEGIN
    ALTER TABLE Employees ADD DepartmentId INT NULL;
    PRINT 'تم إضافة عمود DepartmentId إلى جدول Employees';
END
ELSE
BEGIN
    PRINT 'عمود DepartmentId موجود بالفعل في جدول Employees';
END

-- 4. إنشاء فهارس لتحسين الأداء
-- ===================================================================

-- فهرس على DepartmentId في جدول Users
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_DepartmentId')
BEGIN
    CREATE INDEX IX_Users_DepartmentId ON Users(DepartmentId);
    PRINT 'تم إنشاء فهرس IX_Users_DepartmentId';
END

-- فهرس على ManagerUserId في جدول Departments
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Departments_ManagerUserId')
BEGIN
    CREATE INDEX IX_Departments_ManagerUserId ON Departments(ManagerUserId);
    PRINT 'تم إنشاء فهرس IX_Departments_ManagerUserId';
END

-- فهرس على DepartmentId في جدول Employees
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Employees_DepartmentId')
BEGIN
    CREATE INDEX IX_Employees_DepartmentId ON Employees(DepartmentId);
    PRINT 'تم إنشاء فهرس IX_Employees_DepartmentId';
END

-- 5. إنشاء قيود المفاتيح الخارجية
-- ===================================================================

-- قيد المفتاح الخارجي بين Users و Departments
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Users_Departments')
BEGIN
    ALTER TABLE Users 
    ADD CONSTRAINT FK_Users_Departments 
    FOREIGN KEY (DepartmentId) REFERENCES Departments(DepartmentId);
    PRINT 'تم إنشاء قيد المفتاح الخارجي FK_Users_Departments';
END

-- قيد المفتاح الخارجي بين Departments و Users (للمدير)
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Departments_Users_Manager')
BEGIN
    ALTER TABLE Departments 
    ADD CONSTRAINT FK_Departments_Users_Manager 
    FOREIGN KEY (ManagerUserId) REFERENCES Users(UserId);
    PRINT 'تم إنشاء قيد المفتاح الخارجي FK_Departments_Users_Manager';
END

-- 6. إنشاء بيانات اختبار (اختياري)
-- ===================================================================

PRINT '===================================================================';
PRINT 'إعداد بيانات الاختبار (اختياري)';
PRINT '===================================================================';

-- إنشاء أقسام تجريبية إذا لم تكن موجودة
IF NOT EXISTS (SELECT * FROM Departments WHERE DepartmentCode = 'SALES')
BEGIN
    INSERT INTO Departments (DepartmentName, DepartmentCode, Description, IsActive, CreatedDate, CreatedBy)
    VALUES ('قسم المبيعات', 'SALES', 'قسم المبيعات والتسويق', 1, GETDATE(), 1);
    PRINT 'تم إنشاء قسم المبيعات';
END

IF NOT EXISTS (SELECT * FROM Departments WHERE DepartmentCode = 'ACCOUNTING')
BEGIN
    INSERT INTO Departments (DepartmentName, DepartmentCode, Description, IsActive, CreatedDate, CreatedBy)
    VALUES ('قسم المحاسبة', 'ACCOUNTING', 'قسم المحاسبة والمالية', 1, GETDATE(), 1);
    PRINT 'تم إنشاء قسم المحاسبة';
END

IF NOT EXISTS (SELECT * FROM Departments WHERE DepartmentCode = 'HR')
BEGIN
    INSERT INTO Departments (DepartmentName, DepartmentCode, Description, IsActive, CreatedDate, CreatedBy)
    VALUES ('قسم الموارد البشرية', 'HR', 'قسم الموارد البشرية', 1, GETDATE(), 1);
    PRINT 'تم إنشاء قسم الموارد البشرية';
END

-- إنشاء مستخدمين تجريبيين إذا لم يكونوا موجودين
IF NOT EXISTS (SELECT * FROM Users WHERE Username = 'admin')
BEGIN
    INSERT INTO Users (Username, Password, FullName, UserType, DepartmentId, IsActive)
    VALUES ('admin', 'admin123', 'المدير العام', 'مدير', NULL, 1);
    PRINT 'تم إنشاء المدير العام';
END

-- الحصول على معرفات الأقسام
DECLARE @SalesDeptId INT = (SELECT DepartmentId FROM Departments WHERE DepartmentCode = 'SALES');
DECLARE @AccountingDeptId INT = (SELECT DepartmentId FROM Departments WHERE DepartmentCode = 'ACCOUNTING');
DECLARE @HRDeptId INT = (SELECT DepartmentId FROM Departments WHERE DepartmentCode = 'HR');

IF NOT EXISTS (SELECT * FROM Users WHERE Username = 'sales_manager')
BEGIN
    INSERT INTO Users (Username, Password, FullName, UserType, DepartmentId, IsActive)
    VALUES ('sales_manager', 'pass123', 'مدير المبيعات', 'مدير القسم', @SalesDeptId, 1);
    PRINT 'تم إنشاء مدير المبيعات';
END

IF NOT EXISTS (SELECT * FROM Users WHERE Username = 'acc_manager')
BEGIN
    INSERT INTO Users (Username, Password, FullName, UserType, DepartmentId, IsActive)
    VALUES ('acc_manager', 'pass123', 'مدير المحاسبة', 'مدير القسم', @AccountingDeptId, 1);
    PRINT 'تم إنشاء مدير المحاسبة';
END

IF NOT EXISTS (SELECT * FROM Users WHERE Username = 'hr_manager')
BEGIN
    INSERT INTO Users (Username, Password, FullName, UserType, DepartmentId, IsActive)
    VALUES ('hr_manager', 'pass123', 'مدير الموارد البشرية', 'مدير القسم', @HRDeptId, 1);
    PRINT 'تم إنشاء مدير الموارد البشرية';
END

-- تعيين مدراء الأقسام
DECLARE @SalesManagerId INT = (SELECT UserId FROM Users WHERE Username = 'sales_manager');
DECLARE @AccManagerId INT = (SELECT UserId FROM Users WHERE Username = 'acc_manager');
DECLARE @HRManagerId INT = (SELECT UserId FROM Users WHERE Username = 'hr_manager');

UPDATE Departments SET ManagerUserId = @SalesManagerId WHERE DepartmentCode = 'SALES';
UPDATE Departments SET ManagerUserId = @AccManagerId WHERE DepartmentCode = 'ACCOUNTING';
UPDATE Departments SET ManagerUserId = @HRManagerId WHERE DepartmentCode = 'HR';

PRINT 'تم تعيين مدراء الأقسام';

-- 7. عرض ملخص الإعداد
-- ===================================================================

PRINT '===================================================================';
PRINT 'ملخص الإعداد';
PRINT '===================================================================';

SELECT 
    'الأقسام' as النوع,
    COUNT(*) as العدد
FROM Departments
WHERE IsActive = 1

UNION ALL

SELECT 
    'المستخدمين' as النوع,
    COUNT(*) as العدد
FROM Users
WHERE IsActive = 1

UNION ALL

SELECT 
    'مدراء الأقسام' as النوع,
    COUNT(*) as العدد
FROM Departments
WHERE ManagerUserId IS NOT NULL AND IsActive = 1;

-- عرض تفاصيل الأقسام ومدرائها
SELECT 
    d.DepartmentName as 'اسم القسم',
    d.DepartmentCode as 'كود القسم',
    u.FullName as 'مدير القسم',
    u.Username as 'اسم المستخدم'
FROM Departments d
LEFT JOIN Users u ON d.ManagerUserId = u.UserId
WHERE d.IsActive = 1
ORDER BY d.DepartmentName;

PRINT '===================================================================';
PRINT 'تم الانتهاء من إعداد نظام فلترة الأقسام بنجاح!';
PRINT '===================================================================';
