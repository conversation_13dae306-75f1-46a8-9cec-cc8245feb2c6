namespace EmployeeManagementSystem
{
    partial class ActivityLogDetailsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            mainPanel = new TableLayoutPanel();
            lblUserTitle = new Label();
            lblUser = new Label();
            lblActionTitle = new Label();
            lblAction = new Label();
            lblTableTitle = new Label();
            lblTable = new Label();
            lblRecordIdTitle = new Label();
            lblRecordId = new Label();
            lblDateTitle = new Label();
            lblDate = new Label();
            lblStatusTitle = new Label();
            lblStatus = new Label();
            lblPriorityTitle = new Label();
            lblPriority = new Label();
            lblIpAddressTitle = new Label();
            lblIpAddress = new Label();
            lblComputerNameTitle = new Label();
            lblComputerName = new Label();
            lblOldValuesTitle = new Label();
            rtbOldValues = new RichTextBox();
            lblNewValuesTitle = new Label();
            rtbNewValues = new RichTextBox();
            lblNotesTitle = new Label();
            rtbNotes = new RichTextBox();
            lblErrorMessageTitle = new Label();
            rtbErrorMessage = new RichTextBox();
            btnClose = new Button();
            mainPanel.SuspendLayout();
            SuspendLayout();
            // 
            // mainPanel
            // 
            mainPanel.ColumnCount = 2;
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25F));
            mainPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 75F));
            mainPanel.Controls.Add(lblUserTitle, 0, 0);
            mainPanel.Controls.Add(lblUser, 1, 0);
            mainPanel.Controls.Add(lblActionTitle, 0, 1);
            mainPanel.Controls.Add(lblAction, 1, 1);
            mainPanel.Controls.Add(lblTableTitle, 0, 2);
            mainPanel.Controls.Add(lblTable, 1, 2);
            mainPanel.Controls.Add(lblRecordIdTitle, 0, 3);
            mainPanel.Controls.Add(lblRecordId, 1, 3);
            mainPanel.Controls.Add(lblDateTitle, 0, 4);
            mainPanel.Controls.Add(lblDate, 1, 4);
            mainPanel.Controls.Add(lblStatusTitle, 0, 5);
            mainPanel.Controls.Add(lblStatus, 1, 5);
            mainPanel.Controls.Add(lblPriorityTitle, 0, 6);
            mainPanel.Controls.Add(lblPriority, 1, 6);
            mainPanel.Controls.Add(lblIpAddressTitle, 0, 7);
            mainPanel.Controls.Add(lblIpAddress, 1, 7);
            mainPanel.Controls.Add(lblComputerNameTitle, 0, 8);
            mainPanel.Controls.Add(lblComputerName, 1, 8);
            mainPanel.Controls.Add(lblOldValuesTitle, 0, 9);
            mainPanel.Controls.Add(rtbOldValues, 1, 9);
            mainPanel.Controls.Add(lblNewValuesTitle, 0, 10);
            mainPanel.Controls.Add(rtbNewValues, 1, 10);
            mainPanel.Controls.Add(lblNotesTitle, 0, 11);
            mainPanel.Controls.Add(rtbNotes, 1, 11);
            mainPanel.Controls.Add(lblErrorMessageTitle, 0, 12);
            mainPanel.Controls.Add(rtbErrorMessage, 1, 12);
            mainPanel.Controls.Add(btnClose, 1, 13);
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.Font = new Font("Cairo", 8.999999F);
            mainPanel.Location = new Point(12, 12);
            mainPanel.Margin = new Padding(4, 3, 4, 3);
            mainPanel.Name = "mainPanel";
            mainPanel.Padding = new Padding(12);
            mainPanel.RowCount = 14;
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 39F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 39F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 39F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 39F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 39F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 39F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 39F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 39F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 39F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 92F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 92F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 69F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 69F));
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 46F));
            mainPanel.Size = new Size(886, 729);
            mainPanel.TabIndex = 0;
            // 
            // lblUserTitle
            // 
            lblUserTitle.Anchor = AnchorStyles.Right;
            lblUserTitle.AutoSize = true;
            lblUserTitle.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblUserTitle.Location = new Point(663, 20);
            lblUserTitle.Margin = new Padding(4, 0, 4, 0);
            lblUserTitle.Name = "lblUserTitle";
            lblUserTitle.Size = new Size(68, 23);
            lblUserTitle.TabIndex = 0;
            lblUserTitle.Text = "المستخدم:";
            // 
            // lblUser
            // 
            lblUser.Anchor = AnchorStyles.Left;
            lblUser.AutoSize = true;
            lblUser.Font = new Font("Cairo", 8.999999F);
            lblUser.Location = new Point(598, 20);
            lblUser.Margin = new Padding(4, 0, 4, 0);
            lblUser.Name = "lblUser";
            lblUser.Size = new Size(57, 23);
            lblUser.TabIndex = 1;
            lblUser.Text = "غير محدد";
            // 
            // lblActionTitle
            // 
            lblActionTitle.Anchor = AnchorStyles.Right;
            lblActionTitle.AutoSize = true;
            lblActionTitle.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblActionTitle.Location = new Point(663, 59);
            lblActionTitle.Margin = new Padding(4, 0, 4, 0);
            lblActionTitle.Name = "lblActionTitle";
            lblActionTitle.Size = new Size(55, 23);
            lblActionTitle.TabIndex = 2;
            lblActionTitle.Text = "العملية:";
            // 
            // lblAction
            // 
            lblAction.Anchor = AnchorStyles.Left;
            lblAction.AutoSize = true;
            lblAction.Font = new Font("Cairo", 8.999999F);
            lblAction.Location = new Point(598, 59);
            lblAction.Margin = new Padding(4, 0, 4, 0);
            lblAction.Name = "lblAction";
            lblAction.Size = new Size(57, 23);
            lblAction.TabIndex = 3;
            lblAction.Text = "غير محدد";
            // 
            // lblTableTitle
            // 
            lblTableTitle.Anchor = AnchorStyles.Right;
            lblTableTitle.AutoSize = true;
            lblTableTitle.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblTableTitle.Location = new Point(663, 98);
            lblTableTitle.Margin = new Padding(4, 0, 4, 0);
            lblTableTitle.Name = "lblTableTitle";
            lblTableTitle.Size = new Size(50, 23);
            lblTableTitle.TabIndex = 4;
            lblTableTitle.Text = "الجدول:";
            // 
            // lblTable
            // 
            lblTable.Anchor = AnchorStyles.Left;
            lblTable.AutoSize = true;
            lblTable.Font = new Font("Cairo", 8.999999F);
            lblTable.Location = new Point(598, 98);
            lblTable.Margin = new Padding(4, 0, 4, 0);
            lblTable.Name = "lblTable";
            lblTable.Size = new Size(57, 23);
            lblTable.TabIndex = 5;
            lblTable.Text = "غير محدد";
            // 
            // lblRecordIdTitle
            // 
            lblRecordIdTitle.Anchor = AnchorStyles.Right;
            lblRecordIdTitle.AutoSize = true;
            lblRecordIdTitle.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblRecordIdTitle.Location = new Point(663, 137);
            lblRecordIdTitle.Margin = new Padding(4, 0, 4, 0);
            lblRecordIdTitle.Name = "lblRecordIdTitle";
            lblRecordIdTitle.Size = new Size(84, 23);
            lblRecordIdTitle.TabIndex = 6;
            lblRecordIdTitle.Text = "معرف السجل:";
            // 
            // lblRecordId
            // 
            lblRecordId.Anchor = AnchorStyles.Left;
            lblRecordId.AutoSize = true;
            lblRecordId.Font = new Font("Cairo", 8.999999F);
            lblRecordId.Location = new Point(598, 137);
            lblRecordId.Margin = new Padding(4, 0, 4, 0);
            lblRecordId.Name = "lblRecordId";
            lblRecordId.Size = new Size(57, 23);
            lblRecordId.TabIndex = 7;
            lblRecordId.Text = "غير محدد";
            // 
            // lblDateTitle
            // 
            lblDateTitle.Anchor = AnchorStyles.Right;
            lblDateTitle.AutoSize = true;
            lblDateTitle.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblDateTitle.Location = new Point(663, 176);
            lblDateTitle.Margin = new Padding(4, 0, 4, 0);
            lblDateTitle.Name = "lblDateTitle";
            lblDateTitle.Size = new Size(46, 23);
            lblDateTitle.TabIndex = 8;
            lblDateTitle.Text = "التاريخ:";
            // 
            // lblDate
            // 
            lblDate.Anchor = AnchorStyles.Left;
            lblDate.AutoSize = true;
            lblDate.Font = new Font("Cairo", 8.999999F);
            lblDate.Location = new Point(598, 176);
            lblDate.Margin = new Padding(4, 0, 4, 0);
            lblDate.Name = "lblDate";
            lblDate.Size = new Size(57, 23);
            lblDate.TabIndex = 9;
            lblDate.Text = "غير محدد";
            // 
            // lblStatusTitle
            // 
            lblStatusTitle.Anchor = AnchorStyles.Right;
            lblStatusTitle.AutoSize = true;
            lblStatusTitle.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblStatusTitle.Location = new Point(663, 215);
            lblStatusTitle.Margin = new Padding(4, 0, 4, 0);
            lblStatusTitle.Name = "lblStatusTitle";
            lblStatusTitle.Size = new Size(44, 23);
            lblStatusTitle.TabIndex = 10;
            lblStatusTitle.Text = "الحالة:";
            // 
            // lblStatus
            // 
            lblStatus.Anchor = AnchorStyles.Left;
            lblStatus.AutoSize = true;
            lblStatus.Font = new Font("Cairo", 8.999999F);
            lblStatus.Location = new Point(598, 215);
            lblStatus.Margin = new Padding(4, 0, 4, 0);
            lblStatus.Name = "lblStatus";
            lblStatus.Size = new Size(57, 23);
            lblStatus.TabIndex = 11;
            lblStatus.Text = "غير محدد";
            // 
            // lblPriorityTitle
            // 
            lblPriorityTitle.Anchor = AnchorStyles.Right;
            lblPriorityTitle.AutoSize = true;
            lblPriorityTitle.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblPriorityTitle.Location = new Point(663, 254);
            lblPriorityTitle.Margin = new Padding(4, 0, 4, 0);
            lblPriorityTitle.Name = "lblPriorityTitle";
            lblPriorityTitle.Size = new Size(56, 23);
            lblPriorityTitle.TabIndex = 12;
            lblPriorityTitle.Text = "الأولوية:";
            // 
            // lblPriority
            // 
            lblPriority.Anchor = AnchorStyles.Left;
            lblPriority.AutoSize = true;
            lblPriority.Font = new Font("Cairo", 8.999999F);
            lblPriority.Location = new Point(598, 254);
            lblPriority.Margin = new Padding(4, 0, 4, 0);
            lblPriority.Name = "lblPriority";
            lblPriority.Size = new Size(57, 23);
            lblPriority.TabIndex = 13;
            lblPriority.Text = "غير محدد";
            // 
            // lblIpAddressTitle
            // 
            lblIpAddressTitle.Anchor = AnchorStyles.Right;
            lblIpAddressTitle.AutoSize = true;
            lblIpAddressTitle.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblIpAddressTitle.Location = new Point(663, 293);
            lblIpAddressTitle.Margin = new Padding(4, 0, 4, 0);
            lblIpAddressTitle.Name = "lblIpAddressTitle";
            lblIpAddressTitle.Size = new Size(58, 23);
            lblIpAddressTitle.TabIndex = 14;
            lblIpAddressTitle.Text = "عنوان IP:";
            // 
            // lblIpAddress
            // 
            lblIpAddress.Anchor = AnchorStyles.Left;
            lblIpAddress.AutoSize = true;
            lblIpAddress.Font = new Font("Cairo", 8.999999F);
            lblIpAddress.Location = new Point(598, 293);
            lblIpAddress.Margin = new Padding(4, 0, 4, 0);
            lblIpAddress.Name = "lblIpAddress";
            lblIpAddress.Size = new Size(57, 23);
            lblIpAddress.TabIndex = 15;
            lblIpAddress.Text = "غير محدد";
            // 
            // lblComputerNameTitle
            // 
            lblComputerNameTitle.Anchor = AnchorStyles.Right;
            lblComputerNameTitle.AutoSize = true;
            lblComputerNameTitle.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblComputerNameTitle.Location = new Point(663, 332);
            lblComputerNameTitle.Margin = new Padding(4, 0, 4, 0);
            lblComputerNameTitle.Name = "lblComputerNameTitle";
            lblComputerNameTitle.Size = new Size(72, 23);
            lblComputerNameTitle.TabIndex = 16;
            lblComputerNameTitle.Text = "اسم الجهاز:";
            // 
            // lblComputerName
            // 
            lblComputerName.Anchor = AnchorStyles.Left;
            lblComputerName.AutoSize = true;
            lblComputerName.Font = new Font("Cairo", 8.999999F);
            lblComputerName.Location = new Point(598, 332);
            lblComputerName.Margin = new Padding(4, 0, 4, 0);
            lblComputerName.Name = "lblComputerName";
            lblComputerName.Size = new Size(57, 23);
            lblComputerName.TabIndex = 17;
            lblComputerName.Text = "غير محدد";
            // 
            // lblOldValuesTitle
            // 
            lblOldValuesTitle.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblOldValuesTitle.AutoSize = true;
            lblOldValuesTitle.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblOldValuesTitle.Location = new Point(663, 363);
            lblOldValuesTitle.Margin = new Padding(4, 0, 4, 0);
            lblOldValuesTitle.Name = "lblOldValuesTitle";
            lblOldValuesTitle.Size = new Size(90, 23);
            lblOldValuesTitle.TabIndex = 18;
            lblOldValuesTitle.Text = "القيم القديمة:";
            // 
            // rtbOldValues
            // 
            rtbOldValues.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            rtbOldValues.BackColor = Color.FromArgb(248, 248, 248);
            rtbOldValues.Font = new Font("Cairo", 8.249999F);
            rtbOldValues.Location = new Point(16, 366);
            rtbOldValues.Margin = new Padding(4, 3, 4, 3);
            rtbOldValues.Name = "rtbOldValues";
            rtbOldValues.ReadOnly = true;
            rtbOldValues.Size = new Size(639, 86);
            rtbOldValues.TabIndex = 19;
            rtbOldValues.Text = "";
            // 
            // lblNewValuesTitle
            // 
            lblNewValuesTitle.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblNewValuesTitle.AutoSize = true;
            lblNewValuesTitle.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblNewValuesTitle.Location = new Point(663, 455);
            lblNewValuesTitle.Margin = new Padding(4, 0, 4, 0);
            lblNewValuesTitle.Name = "lblNewValuesTitle";
            lblNewValuesTitle.Size = new Size(86, 23);
            lblNewValuesTitle.TabIndex = 20;
            lblNewValuesTitle.Text = "القيم الجديدة:";
            // 
            // rtbNewValues
            // 
            rtbNewValues.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            rtbNewValues.BackColor = Color.FromArgb(248, 248, 248);
            rtbNewValues.Font = new Font("Cairo", 8.249999F);
            rtbNewValues.Location = new Point(16, 458);
            rtbNewValues.Margin = new Padding(4, 3, 4, 3);
            rtbNewValues.Name = "rtbNewValues";
            rtbNewValues.ReadOnly = true;
            rtbNewValues.Size = new Size(639, 86);
            rtbNewValues.TabIndex = 21;
            rtbNewValues.Text = "";
            // 
            // lblNotesTitle
            // 
            lblNotesTitle.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblNotesTitle.AutoSize = true;
            lblNotesTitle.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblNotesTitle.Location = new Point(663, 547);
            lblNotesTitle.Margin = new Padding(4, 0, 4, 0);
            lblNotesTitle.Name = "lblNotesTitle";
            lblNotesTitle.Size = new Size(69, 23);
            lblNotesTitle.TabIndex = 22;
            lblNotesTitle.Text = "الملاحظات:";
            // 
            // rtbNotes
            // 
            rtbNotes.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            rtbNotes.BackColor = Color.FromArgb(248, 248, 248);
            rtbNotes.Font = new Font("Cairo", 8.249999F);
            rtbNotes.Location = new Point(16, 550);
            rtbNotes.Margin = new Padding(4, 3, 4, 3);
            rtbNotes.Name = "rtbNotes";
            rtbNotes.ReadOnly = true;
            rtbNotes.Size = new Size(639, 63);
            rtbNotes.TabIndex = 23;
            rtbNotes.Text = "";
            // 
            // lblErrorMessageTitle
            // 
            lblErrorMessageTitle.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblErrorMessageTitle.AutoSize = true;
            lblErrorMessageTitle.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            lblErrorMessageTitle.Location = new Point(663, 616);
            lblErrorMessageTitle.Margin = new Padding(4, 0, 4, 0);
            lblErrorMessageTitle.Name = "lblErrorMessageTitle";
            lblErrorMessageTitle.Size = new Size(75, 23);
            lblErrorMessageTitle.TabIndex = 24;
            lblErrorMessageTitle.Text = "رسالة الخطأ:";
            // 
            // rtbErrorMessage
            // 
            rtbErrorMessage.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            rtbErrorMessage.BackColor = Color.FromArgb(255, 245, 245);
            rtbErrorMessage.Font = new Font("Cairo", 8.249999F);
            rtbErrorMessage.ForeColor = Color.Red;
            rtbErrorMessage.Location = new Point(16, 619);
            rtbErrorMessage.Margin = new Padding(4, 3, 4, 3);
            rtbErrorMessage.Name = "rtbErrorMessage";
            rtbErrorMessage.ReadOnly = true;
            rtbErrorMessage.Size = new Size(639, 63);
            rtbErrorMessage.TabIndex = 25;
            rtbErrorMessage.Text = "";
            // 
            // btnClose
            // 
            btnClose.Anchor = AnchorStyles.Right;
            btnClose.BackColor = Color.FromArgb(96, 125, 139);
            btnClose.FlatStyle = FlatStyle.Flat;
            btnClose.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnClose.ForeColor = Color.White;
            btnClose.Location = new Point(16, 690);
            btnClose.Margin = new Padding(4, 3, 4, 3);
            btnClose.Name = "btnClose";
            btnClose.Size = new Size(117, 36);
            btnClose.TabIndex = 26;
            btnClose.Text = "إغلاق";
            btnClose.UseVisualStyleBackColor = false;
            btnClose.Click += BtnClose_Click;
            // 
            // ActivityLogDetailsForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(910, 753);
            Controls.Add(mainPanel);
            Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point, 0);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Margin = new Padding(4, 3, 4, 3);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "ActivityLogDetailsForm";
            Padding = new Padding(12);
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterParent;
            Text = "تفاصيل النشاط";
            Load += ActivityLogDetailsForm_Load;
            mainPanel.ResumeLayout(false);
            mainPanel.PerformLayout();
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TableLayoutPanel mainPanel;
        private System.Windows.Forms.Label lblUserTitle;
        private System.Windows.Forms.Label lblUser;
        private System.Windows.Forms.Label lblActionTitle;
        private System.Windows.Forms.Label lblAction;
        private System.Windows.Forms.Label lblTableTitle;
        private System.Windows.Forms.Label lblTable;
        private System.Windows.Forms.Label lblRecordIdTitle;
        private System.Windows.Forms.Label lblRecordId;
        private System.Windows.Forms.Label lblDateTitle;
        private System.Windows.Forms.Label lblDate;
        private System.Windows.Forms.Label lblStatusTitle;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.Label lblPriorityTitle;
        private System.Windows.Forms.Label lblPriority;
        private System.Windows.Forms.Label lblIpAddressTitle;
        private System.Windows.Forms.Label lblIpAddress;
        private System.Windows.Forms.Label lblComputerNameTitle;
        private System.Windows.Forms.Label lblComputerName;
        private System.Windows.Forms.Label lblOldValuesTitle;
        private System.Windows.Forms.RichTextBox rtbOldValues;
        private System.Windows.Forms.Label lblNewValuesTitle;
        private System.Windows.Forms.RichTextBox rtbNewValues;
        private System.Windows.Forms.Label lblNotesTitle;
        private System.Windows.Forms.RichTextBox rtbNotes;
        private System.Windows.Forms.Label lblErrorMessageTitle;
        private System.Windows.Forms.RichTextBox rtbErrorMessage;
        private System.Windows.Forms.Button btnClose;
    }
}