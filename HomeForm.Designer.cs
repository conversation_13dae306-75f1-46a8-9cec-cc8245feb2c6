namespace EmployeeManagementSystem
{
    partial class HomeForm
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            tableLayoutPanel = new TableLayoutPanel();
            diagramPanel = new Panel();
            diagramTableLayout = new TableLayoutPanel();
            lab_el1 = new Label();
            mainPanel = new Panel();
            lblDateTime = new Label();
            lblUserGreeting = new Label();
            lblCompanyName = new Label();
            pictureBoxLogo = new PictureBox();
            lblHeaderStats = new Label();
            timer = new System.Windows.Forms.Timer(components);
            timer1 = new System.Windows.Forms.Timer(components);
            panel7 = new Panel();
            panel3 = new Panel();
            label2 = new Label();
            lblEmployeesCount = new Label();
            panel2 = new Panel();
            label1 = new Label();
            lblUsersCount = new Label();
            panel6 = new Panel();
            label5 = new Label();
            lbl_DBSize = new Label();
            panel4 = new Panel();
            label3 = new Label();
            lblVacationsCount = new Label();
            panel5 = new Panel();
            label4 = new Label();
            lblCoursesCount = new Label();
            tableLayoutPanel.SuspendLayout();
            diagramPanel.SuspendLayout();
            mainPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBoxLogo).BeginInit();
            panel7.SuspendLayout();
            panel3.SuspendLayout();
            panel2.SuspendLayout();
            panel6.SuspendLayout();
            panel4.SuspendLayout();
            panel5.SuspendLayout();
            SuspendLayout();
            // 
            // tableLayoutPanel
            // 
            tableLayoutPanel.ColumnCount = 2;
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 0.78125F));
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 99.21875F));
            tableLayoutPanel.Controls.Add(diagramPanel, 1, 0);
            tableLayoutPanel.Dock = DockStyle.Top;
            tableLayoutPanel.Location = new Point(0, 0);
            tableLayoutPanel.Name = "tableLayoutPanel";
            tableLayoutPanel.RowCount = 1;
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            tableLayoutPanel.Size = new Size(1024, 351);
            tableLayoutPanel.TabIndex = 0;
            // 
            // diagramPanel
            // 
            diagramPanel.BackColor = Color.White;
            diagramPanel.Controls.Add(diagramTableLayout);
            diagramPanel.Dock = DockStyle.Fill;
            diagramPanel.Location = new Point(3, 3);
            diagramPanel.Name = "diagramPanel";
            diagramPanel.Padding = new Padding(10);
            diagramPanel.Size = new Size(1010, 345);
            diagramPanel.TabIndex = 1;
            // 
            // diagramTableLayout
            // 
            diagramTableLayout.BackColor = Color.Transparent;
            diagramTableLayout.ColumnCount = 3;
            diagramTableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            diagramTableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            diagramTableLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20F));
            diagramTableLayout.Dock = DockStyle.Fill;
            diagramTableLayout.Location = new Point(10, 10);
            diagramTableLayout.Name = "diagramTableLayout";
            diagramTableLayout.Padding = new Padding(5);
            diagramTableLayout.RowCount = 2;
            diagramTableLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            diagramTableLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            diagramTableLayout.Size = new Size(990, 325);
            diagramTableLayout.TabIndex = 0;
            // 
            // lab_el1
            // 
            lab_el1.Dock = DockStyle.Bottom;
            lab_el1.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lab_el1.Location = new Point(0, 137);
            lab_el1.Name = "lab_el1";
            lab_el1.RightToLeft = RightToLeft.No;
            lab_el1.Size = new Size(1024, 40);
            lab_el1.TabIndex = 0;
            lab_el1.Text = "إليك بعض النصائح المهمة لمساعدتك في استخدام البرنامج بفعالية.";
            lab_el1.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // mainPanel
            // 
            mainPanel.Controls.Add(lblDateTime);
            mainPanel.Controls.Add(lblUserGreeting);
            mainPanel.Controls.Add(lblCompanyName);
            mainPanel.Controls.Add(pictureBoxLogo);
            mainPanel.Dock = DockStyle.Bottom;
            mainPanel.Location = new Point(0, 531);
            mainPanel.Name = "mainPanel";
            mainPanel.Size = new Size(1024, 237);
            mainPanel.TabIndex = 1;
            // 
            // lblDateTime
            // 
            lblDateTime.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            lblDateTime.Font = new Font("Cairo", 14F);
            lblDateTime.Location = new Point(613, 186);
            lblDateTime.Name = "lblDateTime";
            lblDateTime.Size = new Size(400, 43);
            lblDateTime.TabIndex = 3;
            lblDateTime.Text = "التاريخ والوقت";
            lblDateTime.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblUserGreeting
            // 
            lblUserGreeting.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblUserGreeting.Font = new Font("Cairo", 16F, FontStyle.Bold);
            lblUserGreeting.Location = new Point(613, 130);
            lblUserGreeting.Name = "lblUserGreeting";
            lblUserGreeting.Size = new Size(400, 50);
            lblUserGreeting.TabIndex = 2;
            lblUserGreeting.Text = "مرحباً بك";
            lblUserGreeting.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblCompanyName
            // 
            lblCompanyName.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            lblCompanyName.Font = new Font("Cairo", 20F, FontStyle.Bold);
            lblCompanyName.Location = new Point(7, 178);
            lblCompanyName.Name = "lblCompanyName";
            lblCompanyName.Size = new Size(309, 52);
            lblCompanyName.TabIndex = 1;
            lblCompanyName.Text = "اسم المؤسسة";
            lblCompanyName.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // pictureBoxLogo
            // 
            pictureBoxLogo.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            pictureBoxLogo.Image = Properties.Resources.photo;
            pictureBoxLogo.Location = new Point(66, 6);
            pictureBoxLogo.Name = "pictureBoxLogo";
            pictureBoxLogo.Size = new Size(200, 169);
            pictureBoxLogo.SizeMode = PictureBoxSizeMode.Zoom;
            pictureBoxLogo.TabIndex = 0;
            pictureBoxLogo.TabStop = false;
            // 
            // lblHeaderStats
            // 
            lblHeaderStats.Dock = DockStyle.Top;
            lblHeaderStats.Font = new Font("Cairo", 15.7499981F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lblHeaderStats.ForeColor = Color.White;
            lblHeaderStats.Location = new Point(0, 0);
            lblHeaderStats.Name = "lblHeaderStats";
            lblHeaderStats.Size = new Size(1024, 38);
            lblHeaderStats.TabIndex = 2;
            lblHeaderStats.Text = "إحصائيات النظام\r\n\r\n";
            lblHeaderStats.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // timer
            // 
            timer.Enabled = true;
            timer.Interval = 1000;
            timer.Tick += Timer_Tick;
            // 
            // timer1
            // 
            timer1.Interval = 5000;
            // 
            // panel7
            // 
            panel7.Controls.Add(lab_el1);
            panel7.Controls.Add(lblHeaderStats);
            panel7.Controls.Add(panel3);
            panel7.Controls.Add(panel2);
            panel7.Controls.Add(panel6);
            panel7.Controls.Add(panel4);
            panel7.Controls.Add(panel5);
            panel7.Dock = DockStyle.Bottom;
            panel7.Location = new Point(0, 354);
            panel7.Name = "panel7";
            panel7.Size = new Size(1024, 177);
            panel7.TabIndex = 2;
            // 
            // panel3
            // 
            panel3.Anchor = AnchorStyles.Top;
            panel3.Controls.Add(label2);
            panel3.Controls.Add(lblEmployeesCount);
            panel3.Font = new Font("Cairo", 12F, FontStyle.Bold);
            panel3.Location = new Point(836, 41);
            panel3.Name = "panel3";
            panel3.RightToLeft = RightToLeft.Yes;
            panel3.Size = new Size(191, 93);
            panel3.TabIndex = 3;
            // 
            // label2
            // 
            label2.Dock = DockStyle.Top;
            label2.Font = new Font("Cairo", 13.25F, FontStyle.Bold);
            label2.ForeColor = Color.White;
            label2.Image = Properties.Resources.user_groups_32px;
            label2.ImageAlign = ContentAlignment.MiddleRight;
            label2.Location = new Point(0, 0);
            label2.Name = "label2";
            label2.Size = new Size(191, 31);
            label2.TabIndex = 3;
            label2.Text = "عدد الموظفين";
            label2.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // lblEmployeesCount
            // 
            lblEmployeesCount.Dock = DockStyle.Bottom;
            lblEmployeesCount.Font = new Font("Cairo", 13F, FontStyle.Bold);
            lblEmployeesCount.ForeColor = Color.White;
            lblEmployeesCount.Location = new Point(0, 53);
            lblEmployeesCount.Name = "lblEmployeesCount";
            lblEmployeesCount.Size = new Size(191, 40);
            lblEmployeesCount.TabIndex = 2;
            lblEmployeesCount.Text = "0";
            lblEmployeesCount.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // panel2
            // 
            panel2.Anchor = AnchorStyles.Top;
            panel2.Controls.Add(label1);
            panel2.Controls.Add(lblUsersCount);
            panel2.Font = new Font("Cairo", 12F, FontStyle.Bold);
            panel2.Location = new Point(619, 41);
            panel2.Name = "panel2";
            panel2.RightToLeft = RightToLeft.Yes;
            panel2.Size = new Size(201, 93);
            panel2.TabIndex = 4;
            // 
            // label1
            // 
            label1.Dock = DockStyle.Top;
            label1.Font = new Font("Cairo", 13.25F, FontStyle.Bold);
            label1.ForeColor = Color.White;
            label1.Image = Properties.Resources.management_32px;
            label1.ImageAlign = ContentAlignment.MiddleRight;
            label1.Location = new Point(0, 0);
            label1.Name = "label1";
            label1.Size = new Size(201, 31);
            label1.TabIndex = 5;
            label1.Text = "عدد المستخدمين";
            label1.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // lblUsersCount
            // 
            lblUsersCount.Dock = DockStyle.Bottom;
            lblUsersCount.Font = new Font("Cairo", 13F, FontStyle.Bold);
            lblUsersCount.ForeColor = Color.White;
            lblUsersCount.Location = new Point(0, 53);
            lblUsersCount.Name = "lblUsersCount";
            lblUsersCount.Size = new Size(201, 40);
            lblUsersCount.TabIndex = 1;
            lblUsersCount.Text = "0";
            lblUsersCount.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // panel6
            // 
            panel6.Anchor = AnchorStyles.Top;
            panel6.Controls.Add(label5);
            panel6.Controls.Add(lbl_DBSize);
            panel6.Font = new Font("Cairo", 12F, FontStyle.Bold);
            panel6.Location = new Point(-2, 41);
            panel6.Name = "panel6";
            panel6.RightToLeft = RightToLeft.Yes;
            panel6.Size = new Size(191, 93);
            panel6.TabIndex = 7;
            // 
            // label5
            // 
            label5.Dock = DockStyle.Top;
            label5.Font = new Font("Cairo", 13.25F, FontStyle.Bold);
            label5.ForeColor = Color.White;
            label5.Image = Properties.Resources.database_32px;
            label5.ImageAlign = ContentAlignment.MiddleRight;
            label5.Location = new Point(0, 0);
            label5.Name = "label5";
            label5.Size = new Size(191, 31);
            label5.TabIndex = 5;
            label5.Text = "حجم البيانات";
            label5.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lbl_DBSize
            // 
            lbl_DBSize.Cursor = Cursors.Hand;
            lbl_DBSize.Dock = DockStyle.Bottom;
            lbl_DBSize.Font = new Font("Cairo", 13F, FontStyle.Bold);
            lbl_DBSize.ForeColor = Color.White;
            lbl_DBSize.Location = new Point(0, 53);
            lbl_DBSize.Name = "lbl_DBSize";
            lbl_DBSize.Size = new Size(191, 40);
            lbl_DBSize.TabIndex = 4;
            lbl_DBSize.Text = "0";
            lbl_DBSize.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // panel4
            // 
            panel4.Anchor = AnchorStyles.Top;
            panel4.Controls.Add(label3);
            panel4.Controls.Add(lblVacationsCount);
            panel4.Font = new Font("Cairo", 12F, FontStyle.Bold);
            panel4.Location = new Point(205, 41);
            panel4.Name = "panel4";
            panel4.RightToLeft = RightToLeft.Yes;
            panel4.Size = new Size(191, 93);
            panel4.TabIndex = 5;
            // 
            // label3
            // 
            label3.Dock = DockStyle.Top;
            label3.Font = new Font("Cairo", 13.25F, FontStyle.Bold);
            label3.ForeColor = Color.White;
            label3.Image = Properties.Resources.traveler_32px;
            label3.ImageAlign = ContentAlignment.MiddleRight;
            label3.Location = new Point(0, 0);
            label3.Name = "label3";
            label3.Size = new Size(191, 31);
            label3.TabIndex = 4;
            label3.Text = "عدد الإجازات";
            label3.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblVacationsCount
            // 
            lblVacationsCount.Dock = DockStyle.Bottom;
            lblVacationsCount.Font = new Font("Cairo", 13F, FontStyle.Bold);
            lblVacationsCount.ForeColor = Color.White;
            lblVacationsCount.Location = new Point(0, 53);
            lblVacationsCount.Name = "lblVacationsCount";
            lblVacationsCount.Size = new Size(191, 40);
            lblVacationsCount.TabIndex = 3;
            lblVacationsCount.Text = "0";
            lblVacationsCount.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // panel5
            // 
            panel5.Anchor = AnchorStyles.Top;
            panel5.Controls.Add(label4);
            panel5.Controls.Add(lblCoursesCount);
            panel5.Font = new Font("Cairo", 12F, FontStyle.Bold);
            panel5.Location = new Point(412, 41);
            panel5.Name = "panel5";
            panel5.RightToLeft = RightToLeft.Yes;
            panel5.Size = new Size(191, 93);
            panel5.TabIndex = 6;
            // 
            // label4
            // 
            label4.Dock = DockStyle.Top;
            label4.Font = new Font("Cairo", 13.25F, FontStyle.Bold);
            label4.ForeColor = Color.White;
            label4.Image = Properties.Resources.classroom_32px;
            label4.ImageAlign = ContentAlignment.MiddleRight;
            label4.Location = new Point(0, 0);
            label4.Name = "label4";
            label4.Size = new Size(191, 31);
            label4.TabIndex = 5;
            label4.Text = "عدد الدورات";
            label4.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblCoursesCount
            // 
            lblCoursesCount.Dock = DockStyle.Bottom;
            lblCoursesCount.Font = new Font("Cairo", 13F, FontStyle.Bold);
            lblCoursesCount.ForeColor = Color.White;
            lblCoursesCount.Location = new Point(0, 53);
            lblCoursesCount.Name = "lblCoursesCount";
            lblCoursesCount.Size = new Size(191, 40);
            lblCoursesCount.TabIndex = 1;
            lblCoursesCount.Text = "0";
            lblCoursesCount.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // HomeForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1024, 768);
            Controls.Add(panel7);
            Controls.Add(mainPanel);
            Controls.Add(tableLayoutPanel);
            Name = "HomeForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            Text = "الصفحة الرئيسية";
            Load += HomeForm_Load;
            tableLayoutPanel.ResumeLayout(false);
            diagramPanel.ResumeLayout(false);
            mainPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)pictureBoxLogo).EndInit();
            panel7.ResumeLayout(false);
            panel3.ResumeLayout(false);
            panel2.ResumeLayout(false);
            panel6.ResumeLayout(false);
            panel4.ResumeLayout(false);
            panel5.ResumeLayout(false);
            ResumeLayout(false);
        }

        private TableLayoutPanel tableLayoutPanel;
        private Panel mainPanel;
        private Label lblDateTime;
        private Label lblUserGreeting;
        private Label lblCompanyName;
        private PictureBox pictureBoxLogo;
        private System.Windows.Forms.Timer timer;
        private Panel diagramPanel;
        private TableLayoutPanel diagramTableLayout;
        private Label lab_el1;
        private System.Windows.Forms.Timer timer1;
        private Panel panel7;
        private Label lblHeaderStats;
        private Panel panel3;
        private Label label2;
        private Label lblEmployeesCount;
        private Panel panel2;
        private Label label1;
        private Label lblUsersCount;
        private Panel panel4;
        private Label label3;
        private Label lblVacationsCount;
        private Panel panel5;
        private Label label4;
        private Label lblCoursesCount;
        private Panel panel6;
        private Label label5;
        private Label lbl_DBSize;
    }
}