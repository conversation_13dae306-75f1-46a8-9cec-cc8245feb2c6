namespace EmployeeManagementSystem
{
    partial class CourseDetailsForm
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            groupBox1 = new GroupBox();
            chkIsActive = new CheckBox();
            dtUpdatedDate = new DateTimePicker();
            lblUpdatedDate = new Label();
            txtUpdatedBy = new TextBox();
            lblUpdatedBy = new Label();
            dtCreatedDate = new DateTimePicker();
            lblCreatedDate = new Label();
            txtCreatedBy = new TextBox();
            lblCreatedBy = new Label();
            chkNotificationSent = new CheckBox();
            cmbPriority = new ComboBox();
            lblPriority = new Label();
            numCost = new NumericUpDown();
            lblCost = new Label();
            chkCertificateIssued = new CheckBox();
            txtPrerequisites = new TextBox();
            lblPrerequisites = new Label();
            txtDescription = new TextBox();
            lblDescription = new Label();
            numCurrentParticipants = new NumericUpDown();
            lblCurrentParticipants = new Label();
            numMaxParticipants = new NumericUpDown();
            lblMaxParticipants = new Label();
            txtLocation = new TextBox();
            lblLocation = new Label();
            cmbStatus = new ComboBox();
            lblStatus = new Label();
            cmbCourse = new ComboBox();
            lblCourse = new Label();
            btnSendNotification = new Button();
            btnClear = new Button();
            btnUpdate = new Button();
            toolTip1 = new ToolTip(components);
            groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numCost).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numCurrentParticipants).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numMaxParticipants).BeginInit();
            SuspendLayout();
            // 
            // groupBox1
            // 
            groupBox1.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBox1.Controls.Add(chkIsActive);
            groupBox1.Controls.Add(dtUpdatedDate);
            groupBox1.Controls.Add(lblUpdatedDate);
            groupBox1.Controls.Add(txtUpdatedBy);
            groupBox1.Controls.Add(lblUpdatedBy);
            groupBox1.Controls.Add(dtCreatedDate);
            groupBox1.Controls.Add(lblCreatedDate);
            groupBox1.Controls.Add(txtCreatedBy);
            groupBox1.Controls.Add(lblCreatedBy);
            groupBox1.Controls.Add(chkNotificationSent);
            groupBox1.Controls.Add(cmbPriority);
            groupBox1.Controls.Add(lblPriority);
            groupBox1.Controls.Add(numCost);
            groupBox1.Controls.Add(lblCost);
            groupBox1.Controls.Add(chkCertificateIssued);
            groupBox1.Controls.Add(txtPrerequisites);
            groupBox1.Controls.Add(lblPrerequisites);
            groupBox1.Controls.Add(txtDescription);
            groupBox1.Controls.Add(lblDescription);
            groupBox1.Controls.Add(numCurrentParticipants);
            groupBox1.Controls.Add(lblCurrentParticipants);
            groupBox1.Controls.Add(numMaxParticipants);
            groupBox1.Controls.Add(lblMaxParticipants);
            groupBox1.Controls.Add(txtLocation);
            groupBox1.Controls.Add(lblLocation);
            groupBox1.Controls.Add(cmbStatus);
            groupBox1.Controls.Add(lblStatus);
            groupBox1.Controls.Add(cmbCourse);
            groupBox1.Controls.Add(lblCourse);
            groupBox1.Controls.Add(btnSendNotification);
            groupBox1.Controls.Add(btnClear);
            groupBox1.Controls.Add(btnUpdate);
            groupBox1.Font = new Font("Cairo", 12F, FontStyle.Bold);
            groupBox1.Location = new Point(12, 12);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(1090, 620);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            groupBox1.Text = "تفاصيل الدورة المتقدمة";
            // 
            // chkIsActive
            // 
            chkIsActive.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            chkIsActive.AutoSize = true;
            chkIsActive.Font = new Font("Cairo", 12F, FontStyle.Bold);
            chkIsActive.Location = new Point(303, 499);
            chkIsActive.Name = "chkIsActive";
            chkIsActive.RightToLeft = RightToLeft.No;
            chkIsActive.Size = new Size(65, 34);
            chkIsActive.TabIndex = 15;
            chkIsActive.Text = "نشط";
            chkIsActive.UseVisualStyleBackColor = true;
            // 
            // dtUpdatedDate
            // 
            dtUpdatedDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            dtUpdatedDate.Enabled = false;
            dtUpdatedDate.Font = new Font("Cairo", 12F);
            dtUpdatedDate.Format = DateTimePickerFormat.Short;
            dtUpdatedDate.Location = new Point(577, 459);
            dtUpdatedDate.Name = "dtUpdatedDate";
            dtUpdatedDate.Size = new Size(300, 37);
            dtUpdatedDate.TabIndex = 14;
            // 
            // lblUpdatedDate
            // 
            lblUpdatedDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblUpdatedDate.AutoSize = true;
            lblUpdatedDate.Location = new Point(883, 459);
            lblUpdatedDate.Name = "lblUpdatedDate";
            lblUpdatedDate.Size = new Size(111, 30);
            lblUpdatedDate.TabIndex = 29;
            lblUpdatedDate.Text = "تاريخ التحديث:";
            // 
            // txtUpdatedBy
            // 
            txtUpdatedBy.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtUpdatedBy.Font = new Font("Cairo", 12F);
            txtUpdatedBy.Location = new Point(68, 456);
            txtUpdatedBy.Name = "txtUpdatedBy";
            txtUpdatedBy.ReadOnly = true;
            txtUpdatedBy.Size = new Size(300, 37);
            txtUpdatedBy.TabIndex = 13;
            // 
            // lblUpdatedBy
            // 
            lblUpdatedBy.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblUpdatedBy.AutoSize = true;
            lblUpdatedBy.Location = new Point(374, 462);
            lblUpdatedBy.Name = "lblUpdatedBy";
            lblUpdatedBy.Size = new Size(124, 30);
            lblUpdatedBy.TabIndex = 27;
            lblUpdatedBy.Text = "محدث بواسطة:";
            // 
            // dtCreatedDate
            // 
            dtCreatedDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            dtCreatedDate.Enabled = false;
            dtCreatedDate.Font = new Font("Cairo", 12F);
            dtCreatedDate.Format = DateTimePickerFormat.Short;
            dtCreatedDate.Location = new Point(577, 414);
            dtCreatedDate.Name = "dtCreatedDate";
            dtCreatedDate.Size = new Size(300, 37);
            dtCreatedDate.TabIndex = 12;
            // 
            // lblCreatedDate
            // 
            lblCreatedDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblCreatedDate.AutoSize = true;
            lblCreatedDate.Location = new Point(883, 420);
            lblCreatedDate.Name = "lblCreatedDate";
            lblCreatedDate.Size = new Size(101, 30);
            lblCreatedDate.TabIndex = 25;
            lblCreatedDate.Text = "تاريخ الإنشاء:";
            // 
            // txtCreatedBy
            // 
            txtCreatedBy.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtCreatedBy.Font = new Font("Cairo", 12F);
            txtCreatedBy.Location = new Point(68, 411);
            txtCreatedBy.Name = "txtCreatedBy";
            txtCreatedBy.ReadOnly = true;
            txtCreatedBy.Size = new Size(300, 37);
            txtCreatedBy.TabIndex = 11;
            // 
            // lblCreatedBy
            // 
            lblCreatedBy.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblCreatedBy.AutoSize = true;
            lblCreatedBy.Location = new Point(374, 416);
            lblCreatedBy.Name = "lblCreatedBy";
            lblCreatedBy.Size = new Size(127, 30);
            lblCreatedBy.TabIndex = 23;
            lblCreatedBy.Text = "منشئ بواسطة:";
            // 
            // chkNotificationSent
            // 
            chkNotificationSent.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            chkNotificationSent.AutoSize = true;
            chkNotificationSent.Font = new Font("Cairo", 12F, FontStyle.Bold);
            chkNotificationSent.Location = new Point(738, 499);
            chkNotificationSent.Name = "chkNotificationSent";
            chkNotificationSent.RightToLeft = RightToLeft.No;
            chkNotificationSent.Size = new Size(147, 34);
            chkNotificationSent.TabIndex = 10;
            chkNotificationSent.Text = "تم إرسال الإشعار";
            chkNotificationSent.UseVisualStyleBackColor = true;
            // 
            // cmbPriority
            // 
            cmbPriority.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbPriority.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbPriority.Font = new Font("Cairo", 12F);
            cmbPriority.FormattingEnabled = true;
            cmbPriority.Items.AddRange(new object[] { "منخفض", "عادي", "مهم", "عاجل" });
            cmbPriority.Location = new Point(577, 368);
            cmbPriority.Name = "cmbPriority";
            cmbPriority.RightToLeft = RightToLeft.No;
            cmbPriority.Size = new Size(300, 38);
            cmbPriority.TabIndex = 9;
            // 
            // lblPriority
            // 
            lblPriority.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblPriority.AutoSize = true;
            lblPriority.Location = new Point(883, 376);
            lblPriority.Name = "lblPriority";
            lblPriority.Size = new Size(73, 30);
            lblPriority.TabIndex = 20;
            lblPriority.Text = "الأولوية:";
            // 
            // numCost
            // 
            numCost.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            numCost.DecimalPlaces = 2;
            numCost.Font = new Font("Cairo", 12F);
            numCost.Location = new Point(68, 366);
            numCost.Maximum = new decimal(new int[] { 999999, 0, 0, 0 });
            numCost.Name = "numCost";
            numCost.Size = new Size(300, 37);
            numCost.TabIndex = 8;
            // 
            // lblCost
            // 
            lblCost.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblCost.AutoSize = true;
            lblCost.Location = new Point(374, 369);
            lblCost.Name = "lblCost";
            lblCost.Size = new Size(72, 30);
            lblCost.TabIndex = 18;
            lblCost.Text = "التكلفة:";
            // 
            // chkCertificateIssued
            // 
            chkCertificateIssued.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            chkCertificateIssued.AutoSize = true;
            chkCertificateIssued.Font = new Font("Cairo", 12F, FontStyle.Bold);
            chkCertificateIssued.Location = new Point(577, 499);
            chkCertificateIssued.Name = "chkCertificateIssued";
            chkCertificateIssued.RightToLeft = RightToLeft.No;
            chkCertificateIssued.Size = new Size(155, 34);
            chkCertificateIssued.TabIndex = 7;
            chkCertificateIssued.Text = "تم إصدار الشهادة";
            chkCertificateIssued.UseVisualStyleBackColor = true;
            // 
            // txtPrerequisites
            // 
            txtPrerequisites.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtPrerequisites.Font = new Font("Cairo", 12F);
            txtPrerequisites.Location = new Point(17, 290);
            txtPrerequisites.Multiline = true;
            txtPrerequisites.Name = "txtPrerequisites";
            txtPrerequisites.RightToLeft = RightToLeft.Yes;
            txtPrerequisites.Size = new Size(860, 70);
            txtPrerequisites.TabIndex = 6;
            // 
            // lblPrerequisites
            // 
            lblPrerequisites.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblPrerequisites.AutoSize = true;
            lblPrerequisites.Location = new Point(883, 293);
            lblPrerequisites.Name = "lblPrerequisites";
            lblPrerequisites.Size = new Size(158, 30);
            lblPrerequisites.TabIndex = 15;
            lblPrerequisites.Text = "المتطلبات المسبقة:";
            // 
            // txtDescription
            // 
            txtDescription.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtDescription.Font = new Font("Cairo", 12F);
            txtDescription.Location = new Point(17, 214);
            txtDescription.Multiline = true;
            txtDescription.Name = "txtDescription";
            txtDescription.RightToLeft = RightToLeft.Yes;
            txtDescription.Size = new Size(860, 70);
            txtDescription.TabIndex = 5;
            // 
            // lblDescription
            // 
            lblDescription.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblDescription.AutoSize = true;
            lblDescription.Location = new Point(883, 217);
            lblDescription.Name = "lblDescription";
            lblDescription.Size = new Size(67, 30);
            lblDescription.TabIndex = 13;
            lblDescription.Text = "الوصف:";
            // 
            // numCurrentParticipants
            // 
            numCurrentParticipants.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            numCurrentParticipants.Font = new Font("Cairo", 12F);
            numCurrentParticipants.Location = new Point(17, 172);
            numCurrentParticipants.Maximum = new decimal(new int[] { 1000, 0, 0, 0 });
            numCurrentParticipants.Name = "numCurrentParticipants";
            numCurrentParticipants.Size = new Size(300, 37);
            numCurrentParticipants.TabIndex = 4;
            // 
            // lblCurrentParticipants
            // 
            lblCurrentParticipants.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblCurrentParticipants.AutoSize = true;
            lblCurrentParticipants.Location = new Point(323, 175);
            lblCurrentParticipants.Name = "lblCurrentParticipants";
            lblCurrentParticipants.Size = new Size(181, 30);
            lblCurrentParticipants.TabIndex = 11;
            lblCurrentParticipants.Text = "عدد المشاركين الحاليين:";
            // 
            // numMaxParticipants
            // 
            numMaxParticipants.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            numMaxParticipants.Font = new Font("Cairo", 12F);
            numMaxParticipants.Location = new Point(577, 172);
            numMaxParticipants.Maximum = new decimal(new int[] { 1000, 0, 0, 0 });
            numMaxParticipants.Name = "numMaxParticipants";
            numMaxParticipants.Size = new Size(300, 37);
            numMaxParticipants.TabIndex = 3;
            // 
            // lblMaxParticipants
            // 
            lblMaxParticipants.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblMaxParticipants.AutoSize = true;
            lblMaxParticipants.Location = new Point(883, 175);
            lblMaxParticipants.Name = "lblMaxParticipants";
            lblMaxParticipants.Size = new Size(193, 30);
            lblMaxParticipants.TabIndex = 9;
            lblMaxParticipants.Text = "العدد الأقصى للمشاركين:";
            // 
            // txtLocation
            // 
            txtLocation.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtLocation.Font = new Font("Cairo", 12F);
            txtLocation.Location = new Point(17, 130);
            txtLocation.Name = "txtLocation";
            txtLocation.RightToLeft = RightToLeft.Yes;
            txtLocation.Size = new Size(860, 37);
            txtLocation.TabIndex = 2;
            // 
            // lblLocation
            // 
            lblLocation.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblLocation.AutoSize = true;
            lblLocation.Location = new Point(883, 133);
            lblLocation.Name = "lblLocation";
            lblLocation.Size = new Size(69, 30);
            lblLocation.TabIndex = 7;
            lblLocation.Text = "الموقع:";
            // 
            // cmbStatus
            // 
            cmbStatus.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbStatus.Font = new Font("Cairo", 12F);
            cmbStatus.FormattingEnabled = true;
            cmbStatus.Items.AddRange(new object[] { "مجدولة", "جارية", "مكتملة", "ملغية", "مؤجلة" });
            cmbStatus.Location = new Point(17, 88);
            cmbStatus.Name = "cmbStatus";
            cmbStatus.RightToLeft = RightToLeft.No;
            cmbStatus.Size = new Size(860, 38);
            cmbStatus.TabIndex = 1;
            // 
            // lblStatus
            // 
            lblStatus.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblStatus.AutoSize = true;
            lblStatus.Location = new Point(883, 91);
            lblStatus.Name = "lblStatus";
            lblStatus.Size = new Size(58, 30);
            lblStatus.TabIndex = 5;
            lblStatus.Text = "الحالة:";
            // 
            // cmbCourse
            // 
            cmbCourse.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbCourse.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbCourse.Font = new Font("Cairo", 12F);
            cmbCourse.FormattingEnabled = true;
            cmbCourse.Location = new Point(17, 45);
            cmbCourse.Name = "cmbCourse";
            cmbCourse.RightToLeft = RightToLeft.No;
            cmbCourse.Size = new Size(860, 38);
            cmbCourse.TabIndex = 0;
            cmbCourse.SelectedIndexChanged += cmbCourse_SelectedIndexChanged;
            // 
            // lblCourse
            // 
            lblCourse.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblCourse.AutoSize = true;
            lblCourse.Location = new Point(883, 48);
            lblCourse.Name = "lblCourse";
            lblCourse.Size = new Size(61, 30);
            lblCourse.TabIndex = 3;
            lblCourse.Text = "الدورة:";
            // 
            // btnSendNotification
            // 
            btnSendNotification.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnSendNotification.BackColor = Color.FromArgb(52, 152, 219);
            btnSendNotification.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnSendNotification.ForeColor = Color.White;
            btnSendNotification.Image = Properties.Resources.notification1_32px;
            btnSendNotification.ImageAlign = ContentAlignment.MiddleRight;
            btnSendNotification.Location = new Point(373, 554);
            btnSendNotification.Name = "btnSendNotification";
            btnSendNotification.Size = new Size(141, 46);
            btnSendNotification.TabIndex = 18;
            btnSendNotification.Text = "إرسال إشعار";
            btnSendNotification.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnSendNotification, "إرسال إشعار لجميع المسجلين");
            btnSendNotification.UseVisualStyleBackColor = false;
            btnSendNotification.Click += btnSendNotification_Click;
            // 
            // btnClear
            // 
            btnClear.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnClear.BackColor = Color.Transparent;
            btnClear.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnClear.Image = Properties.Resources.clear_formatting_32px;
            btnClear.ImageAlign = ContentAlignment.MiddleRight;
            btnClear.Location = new Point(520, 554);
            btnClear.Name = "btnClear";
            btnClear.Size = new Size(90, 46);
            btnClear.TabIndex = 17;
            btnClear.Text = "إفراغ";
            btnClear.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnClear, "إفراغ الحقول");
            btnClear.UseVisualStyleBackColor = false;
            btnClear.Click += btnClear_Click;
            // 
            // btnUpdate
            // 
            btnUpdate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnUpdate.BackColor = Color.Transparent;
            btnUpdate.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnUpdate.Image = Properties.Resources.edit_profile_32px;
            btnUpdate.ImageAlign = ContentAlignment.MiddleRight;
            btnUpdate.Location = new Point(616, 554);
            btnUpdate.Name = "btnUpdate";
            btnUpdate.Size = new Size(102, 46);
            btnUpdate.TabIndex = 16;
            btnUpdate.Text = "تحديث";
            btnUpdate.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnUpdate, "تحديث التفاصيل");
            btnUpdate.UseVisualStyleBackColor = false;
            btnUpdate.Click += btnUpdate_Click;
            // 
            // CourseDetailsForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1114, 644);
            Controls.Add(groupBox1);
            Name = "CourseDetailsForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "تفاصيل الدورة المتقدمة";
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numCost).EndInit();
            ((System.ComponentModel.ISupportInitialize)numCurrentParticipants).EndInit();
            ((System.ComponentModel.ISupportInitialize)numMaxParticipants).EndInit();
            ResumeLayout(false);
        }

        private GroupBox groupBox1;
        private Label lblCourse;
        private ComboBox cmbCourse;
        private Label lblStatus;
        private ComboBox cmbStatus;
        private Label lblLocation;
        private TextBox txtLocation;
        private Label lblMaxParticipants;
        private NumericUpDown numMaxParticipants;
        private Label lblCurrentParticipants;
        private NumericUpDown numCurrentParticipants;
        private Label lblDescription;
        private TextBox txtDescription;
        private Label lblPrerequisites;
        private TextBox txtPrerequisites;
        private CheckBox chkCertificateIssued;
        private Label lblCost;
        private NumericUpDown numCost;
        private Label lblPriority;
        private ComboBox cmbPriority;
        private CheckBox chkNotificationSent;
        private Label lblCreatedBy;
        private TextBox txtCreatedBy;
        private Label lblCreatedDate;
        private DateTimePicker dtCreatedDate;
        private Label lblUpdatedBy;
        private TextBox txtUpdatedBy;
        private Label lblUpdatedDate;
        private DateTimePicker dtUpdatedDate;
        private CheckBox chkIsActive;
        private Button btnUpdate;
        private Button btnClear;
        private Button btnSendNotification;
        private ToolTip toolTip1;
    }
}