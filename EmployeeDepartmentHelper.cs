using System;
using System.Data;
using System.Data.SqlClient;

namespace EmployeeManagementSystem
{
    public static class EmployeeDepartmentHelper
    {
        // دالة للحصول على موظفي قسم معين - فلترة صارمة
        public static DataTable GetEmployeesByDepartment(int departmentId)
        {
            using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
            connection.Open();

            string sql = @"
                SELECT * FROM Employees
                WHERE EmployeeCode IN (
                    SELECT UserId FROM Users
                    WHERE DepartmentId = @DepartmentId
                    AND IsActive = 1
                )
                AND EmployeeCode IS NOT NULL
                ORDER BY EmployeeCode";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DepartmentId", departmentId);

            using var adapter = new SqlDataAdapter(command);
            var table = new DataTable();
            adapter.Fill(table);

            System.Diagnostics.Debug.WriteLine($"GetEmployeesByDepartment - القسم {departmentId}: {table.Rows.Count} موظف");
            return table;
        }

        // دالة للبحث في موظفي قسم معين - فلترة صارمة
        public static DataTable SearchEmployeesInDepartment(string searchTerm, int departmentId)
        {
            using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
            connection.Open();

            string sql = @"
                SELECT * FROM Employees
                WHERE EmployeeCode IN (
                    SELECT UserId FROM Users
                    WHERE DepartmentId = @DepartmentId
                    AND IsActive = 1
                )
                AND EmployeeCode IS NOT NULL
                AND (
                    Name LIKE @SearchTerm OR
                    MotherName LIKE @SearchTerm OR
                    IdentityNumber LIKE @SearchTerm OR
                    KeyCardNumber LIKE @SearchTerm OR
                    PhoneNumber LIKE @SearchTerm OR
                    CAST(EmployeeCode AS NVARCHAR) LIKE @SearchTerm
                )
                ORDER BY EmployeeCode";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DepartmentId", departmentId);
            command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");

            using var adapter = new SqlDataAdapter(command);
            var table = new DataTable();
            adapter.Fill(table);

            System.Diagnostics.Debug.WriteLine($"SearchEmployeesInDepartment - القسم {departmentId}, البحث '{searchTerm}': {table.Rows.Count} موظف");
            return table;
        }

        // دالة لتطبيق فلترة مدير القسم على الموظفين - فلترة صارمة ودقيقة
        public static DataTable GetFilteredEmployees(User? currentUser, string searchTerm = "")
        {
            // فحص صارم لنوع المستخدم
            if (currentUser != null &&
                (currentUser.UserType?.Trim() == "مدير القسم" || currentUser.UserType?.Trim() == "مدير قسم"))
            {
                System.Diagnostics.Debug.WriteLine($"🔒 فلترة صارمة - مدير القسم: {currentUser.FullName} (ID: {currentUser.UserId})");

                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                if (managedDepartmentId.HasValue)
                {
                    System.Diagnostics.Debug.WriteLine($"🔒 عرض موظفي القسم {managedDepartmentId} فقط");
                    var result = string.IsNullOrEmpty(searchTerm) ?
                        GetEmployeesByDepartment(managedDepartmentId.Value) :
                        SearchEmployeesInDepartment(searchTerm, managedDepartmentId.Value);

                    System.Diagnostics.Debug.WriteLine($"🔒 النتيجة النهائية: {result.Rows.Count} موظف");
                    return result;
                }
                else
                {
                    // مدير قسم بدون قسم - إرجاع جدول فارغ بشكل صارم
                    System.Diagnostics.Debug.WriteLine("🔒 مدير قسم بدون قسم مُدار - جدول فارغ");
                    var emptyTable = new DataTable();
                    // إضافة أعمدة فارغة لتجنب الأخطاء
                    emptyTable.Columns.Add("EmployeeCode", typeof(int));
                    emptyTable.Columns.Add("Name", typeof(string));
                    return emptyTable;
                }
            }
            else
            {
                // مدير عام أو مستخدم عادي - عرض جميع الموظفين
                string userType = currentUser?.UserType?.Trim() ?? "غير محدد";
                System.Diagnostics.Debug.WriteLine($"🌐 عرض جميع الموظفين - نوع المستخدم: '{userType}'");
                return string.IsNullOrEmpty(searchTerm) ?
                    DatabaseHelper.GetAllEmployees() :
                    DatabaseHelper.SearchEmployees(searchTerm);
            }
        }

        // دالة لتحديث عنوان النافذة حسب نوع المستخدم
        public static string GetWindowTitle(User? currentUser, int employeeCount)
        {
            if (currentUser != null && (currentUser.UserType?.Trim() == "مدير القسم" || currentUser.UserType?.Trim() == "مدير قسم"))
            {
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                if (managedDepartmentId.HasValue)
                {
                    return $"🔒 نظام إدارة الموظفين - القسم {managedDepartmentId} ({employeeCount} موظف) - مدير القسم: {currentUser.FullName}";
                }
                else
                {
                    return $"⚠️ نظام إدارة الموظفين - لا يوجد قسم مُدار - {currentUser.FullName}";
                }
            }
            else
            {
                string userType = currentUser?.UserType?.Trim() ?? "غير محدد";
                return $"🌐 نظام إدارة الموظفين - جميع الأقسام ({employeeCount} موظف) - {userType}";
            }
        }

        // دالة للتحقق من صحة الفلترة
        public static bool ValidateUserAccess(User? currentUser, int employeeCode)
        {
            if (currentUser == null) return false;

            // المدير العام يمكنه الوصول لكل شيء
            if (currentUser.UserType?.Trim() == "مدير") return true;

            // مدير القسم يمكنه الوصول فقط لموظفي قسمه
            if (currentUser.UserType?.Trim() == "مدير القسم" || currentUser.UserType?.Trim() == "مدير قسم")
            {
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                if (!managedDepartmentId.HasValue) return false;

                // التحقق من أن الموظف ينتمي لنفس القسم
                using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
                connection.Open();

                string sql = "SELECT COUNT(*) FROM Users WHERE UserId = @EmployeeCode AND DepartmentId = @DepartmentId AND IsActive = 1";
                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                command.Parameters.AddWithValue("@DepartmentId", managedDepartmentId.Value);

                int count = Convert.ToInt32(command.ExecuteScalar());
                return count > 0;
            }

            return false;
        }
    }
}
