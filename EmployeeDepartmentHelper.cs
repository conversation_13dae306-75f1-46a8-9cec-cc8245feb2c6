using System;
using System.Data;
using System.Data.SqlClient;
using System.Collections.Generic;
using System.Linq;

namespace EmployeeManagementSystem
{
    /// <summary>
    /// كلاس مساعد لتطبيق فلترة الأقسام على جميع العمليات في النظام
    /// يضمن أن مدير القسم يرى فقط بيانات موظفي قسمه
    /// </summary>
    public static class EmployeeDepartmentHelper
    {
        // دالة للحصول على موظفي قسم معين - فلترة صارمة
        public static DataTable GetEmployeesByDepartment(int departmentId)
        {
            using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
            connection.Open();

            string sql = @"
                SELECT * FROM Employees
                WHERE EmployeeCode IN (
                    SELECT UserId FROM Users
                    WHERE DepartmentId = @DepartmentId
                    AND IsActive = 1
                )
                AND EmployeeCode IS NOT NULL
                ORDER BY EmployeeCode";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DepartmentId", departmentId);

            using var adapter = new SqlDataAdapter(command);
            var table = new DataTable();
            adapter.Fill(table);

            System.Diagnostics.Debug.WriteLine($"GetEmployeesByDepartment - القسم {departmentId}: {table.Rows.Count} موظف");
            return table;
        }

        // دالة للبحث في موظفي قسم معين - فلترة صارمة
        public static DataTable SearchEmployeesInDepartment(string searchTerm, int departmentId)
        {
            using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
            connection.Open();

            string sql = @"
                SELECT * FROM Employees
                WHERE EmployeeCode IN (
                    SELECT UserId FROM Users
                    WHERE DepartmentId = @DepartmentId
                    AND IsActive = 1
                )
                AND EmployeeCode IS NOT NULL
                AND (
                    Name LIKE @SearchTerm OR
                    MotherName LIKE @SearchTerm OR
                    IdentityNumber LIKE @SearchTerm OR
                    KeyCardNumber LIKE @SearchTerm OR
                    PhoneNumber LIKE @SearchTerm OR
                    CAST(EmployeeCode AS NVARCHAR) LIKE @SearchTerm
                )
                ORDER BY EmployeeCode";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DepartmentId", departmentId);
            command.Parameters.AddWithValue("@SearchTerm", $"%{searchTerm}%");

            using var adapter = new SqlDataAdapter(command);
            var table = new DataTable();
            adapter.Fill(table);

            System.Diagnostics.Debug.WriteLine($"SearchEmployeesInDepartment - القسم {departmentId}, البحث '{searchTerm}': {table.Rows.Count} موظف");
            return table;
        }

        // دالة لتطبيق فلترة مدير القسم على الموظفين - فلترة صارمة ودقيقة
        public static DataTable GetFilteredEmployees(User? currentUser, string searchTerm = "")
        {
            // فحص صارم لنوع المستخدم
            if (currentUser != null &&
                (currentUser.UserType?.Trim() == "مدير القسم" || currentUser.UserType?.Trim() == "مدير قسم"))
            {
                System.Diagnostics.Debug.WriteLine($"🔒 فلترة صارمة - مدير القسم: {currentUser.FullName} (ID: {currentUser.UserId})");

                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                if (managedDepartmentId.HasValue)
                {
                    System.Diagnostics.Debug.WriteLine($"🔒 عرض موظفي القسم {managedDepartmentId} فقط");
                    var result = string.IsNullOrEmpty(searchTerm) ?
                        GetEmployeesByDepartment(managedDepartmentId.Value) :
                        SearchEmployeesInDepartment(searchTerm, managedDepartmentId.Value);

                    System.Diagnostics.Debug.WriteLine($"🔒 النتيجة النهائية: {result.Rows.Count} موظف");
                    return result;
                }
                else
                {
                    // مدير قسم بدون قسم - إرجاع جدول فارغ بشكل صارم
                    System.Diagnostics.Debug.WriteLine("🔒 مدير قسم بدون قسم مُدار - جدول فارغ");
                    var emptyTable = new DataTable();
                    // إضافة أعمدة فارغة لتجنب الأخطاء
                    emptyTable.Columns.Add("EmployeeCode", typeof(int));
                    emptyTable.Columns.Add("Name", typeof(string));
                    return emptyTable;
                }
            }
            else
            {
                // مدير عام أو مستخدم عادي - عرض جميع الموظفين
                string userType = currentUser?.UserType?.Trim() ?? "غير محدد";
                System.Diagnostics.Debug.WriteLine($"🌐 عرض جميع الموظفين - نوع المستخدم: '{userType}'");
                return string.IsNullOrEmpty(searchTerm) ?
                    DatabaseHelper.GetAllEmployees() :
                    DatabaseHelper.SearchEmployees(searchTerm);
            }
        }

        // دالة لتحديث عنوان النافذة حسب نوع المستخدم
        public static string GetWindowTitle(User? currentUser, int employeeCount)
        {
            if (currentUser != null && (currentUser.UserType?.Trim() == "مدير القسم" || currentUser.UserType?.Trim() == "مدير قسم"))
            {
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                if (managedDepartmentId.HasValue)
                {
                    return $"🔒 نظام إدارة الموظفين - القسم {managedDepartmentId} ({employeeCount} موظف) - مدير القسم: {currentUser.FullName}";
                }
                else
                {
                    return $"⚠️ نظام إدارة الموظفين - لا يوجد قسم مُدار - {currentUser.FullName}";
                }
            }
            else
            {
                string userType = currentUser?.UserType?.Trim() ?? "غير محدد";
                return $"🌐 نظام إدارة الموظفين - جميع الأقسام ({employeeCount} موظف) - {userType}";
            }
        }

        // دالة للتحقق من صحة الفلترة
        public static bool ValidateUserAccess(User? currentUser, int employeeCode)
        {
            if (currentUser == null) return false;

            // المدير العام يمكنه الوصول لكل شيء
            if (currentUser.UserType?.Trim() == "مدير") return true;

            // مدير القسم يمكنه الوصول فقط لموظفي قسمه
            if (currentUser.UserType?.Trim() == "مدير القسم" || currentUser.UserType?.Trim() == "مدير قسم")
            {
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                if (!managedDepartmentId.HasValue) return false;

                // التحقق من أن الموظف ينتمي لنفس القسم
                using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
                connection.Open();

                string sql = "SELECT COUNT(*) FROM Users WHERE UserId = @EmployeeCode AND DepartmentId = @DepartmentId AND IsActive = 1";
                using var command = new SqlCommand(sql, connection);
                command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                command.Parameters.AddWithValue("@DepartmentId", managedDepartmentId.Value);

                int count = Convert.ToInt32(command.ExecuteScalar());
                return count > 0;
            }

            return false;
        }

        #region دوال فلترة الحضور والغياب

        /// <summary>
        /// الحصول على قائمة الموظفين للحضور مع تطبيق فلترة القسم
        /// </summary>
        public static DataTable GetFilteredEmployeesForAttendance(User? currentUser)
        {
            if (currentUser != null && (currentUser.UserType?.Trim() == "مدير القسم" || currentUser.UserType?.Trim() == "مدير قسم"))
            {
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                if (managedDepartmentId.HasValue)
                {
                    return GetEmployeesByDepartmentForAttendance(managedDepartmentId.Value);
                }
                else
                {
                    // إرجاع جدول فارغ
                    var emptyTable = new DataTable();
                    emptyTable.Columns.Add("EmployeeCode", typeof(int));
                    emptyTable.Columns.Add("Name", typeof(string));
                    return emptyTable;
                }
            }
            else
            {
                // المدير العام - عرض جميع الموظفين
                return DatabaseHelper.GetEmployeesForAttendance();
            }
        }

        /// <summary>
        /// الحصول على موظفي قسم معين للحضور
        /// </summary>
        public static DataTable GetEmployeesByDepartmentForAttendance(int departmentId)
        {
            using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
            connection.Open();

            string sql = @"
                SELECT e.EmployeeCode, e.Name, e.Category, e.DepartmentId
                FROM Employees e
                INNER JOIN Users u ON e.EmployeeCode = u.UserId
                WHERE u.DepartmentId = @DepartmentId AND u.IsActive = 1
                ORDER BY e.Name";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DepartmentId", departmentId);

            using var adapter = new SqlDataAdapter(command);
            var table = new DataTable();
            adapter.Fill(table);

            return table;
        }

        /// <summary>
        /// الحصول على بيانات الحضور مع تطبيق فلترة القسم
        /// </summary>
        public static DataTable GetFilteredAttendanceByDateRange(User? currentUser, DateTime startDate, DateTime endDate)
        {
            if (currentUser != null && (currentUser.UserType?.Trim() == "مدير القسم" || currentUser.UserType?.Trim() == "مدير قسم"))
            {
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                if (managedDepartmentId.HasValue)
                {
                    return GetAttendanceByDateRangeAndDepartment(startDate, endDate, managedDepartmentId.Value);
                }
                else
                {
                    return new DataTable();
                }
            }
            else
            {
                return DatabaseHelper.GetAttendanceByDateRange(startDate, endDate);
            }
        }

        /// <summary>
        /// الحصول على بيانات الحضور لقسم معين في فترة زمنية
        /// </summary>
        public static DataTable GetAttendanceByDateRangeAndDepartment(DateTime startDate, DateTime endDate, int departmentId)
        {
            using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
            connection.Open();

            string sql = @"
                SELECT a.*
                FROM Attendance a
                INNER JOIN Users u ON a.EmployeeCode = u.UserId
                WHERE u.DepartmentId = @DepartmentId
                AND u.IsActive = 1
                AND a.Date BETWEEN @StartDate AND @EndDate
                ORDER BY a.Date DESC, a.EmployeeName";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DepartmentId", departmentId);
            command.Parameters.AddWithValue("@StartDate", startDate.Date);
            command.Parameters.AddWithValue("@EndDate", endDate.Date);

            using var adapter = new SqlDataAdapter(command);
            var table = new DataTable();
            adapter.Fill(table);

            return table;
        }

        #endregion

        #region دوال فلترة الإجازات

        /// <summary>
        /// الحصول على جميع الإجازات مع تطبيق فلترة القسم
        /// </summary>
        public static DataTable GetFilteredVacations(User? currentUser)
        {
            if (currentUser != null && (currentUser.UserType?.Trim() == "مدير القسم" || currentUser.UserType?.Trim() == "مدير قسم"))
            {
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                if (managedDepartmentId.HasValue)
                {
                    return GetVacationsByDepartment(managedDepartmentId.Value);
                }
                else
                {
                    return new DataTable();
                }
            }
            else
            {
                return DatabaseHelper.GetAllVacations();
            }
        }

        /// <summary>
        /// الحصول على إجازات قسم معين
        /// </summary>
        public static DataTable GetVacationsByDepartment(int departmentId)
        {
            using var connection = new SqlConnection(ConStringHelper.GetConnectionString());
            connection.Open();

            string sql = @"
                SELECT v.*
                FROM Vacations v
                INNER JOIN Employees e ON v.EmployeeName = e.Name
                INNER JOIN Users u ON e.EmployeeCode = u.UserId
                WHERE u.DepartmentId = @DepartmentId AND u.IsActive = 1
                ORDER BY v.StartDate DESC";

            using var command = new SqlCommand(sql, connection);
            command.Parameters.AddWithValue("@DepartmentId", departmentId);

            using var adapter = new SqlDataAdapter(command);
            var table = new DataTable();
            adapter.Fill(table);

            return table;
        }

        #endregion

        #region دوال فلترة المستندات

        /// <summary>
        /// الحصول على مستندات موظف مع التحقق من صلاحية الوصول
        /// </summary>
        public static DataTable GetFilteredEmployeeDocuments(User? currentUser, int employeeCode)
        {
            // التحقق من صلاحية الوصول أولاً
            if (!ValidateUserAccess(currentUser, employeeCode))
            {
                return new DataTable(); // إرجاع جدول فارغ إذا لم يكن مخولاً
            }

            return DatabaseHelper.GetEmployeeDocuments(employeeCode);
        }

        /// <summary>
        /// التحقق من إمكانية إضافة مستند لموظف
        /// </summary>
        public static bool CanAddDocumentToEmployee(User? currentUser, int employeeCode)
        {
            return ValidateUserAccess(currentUser, employeeCode);
        }

        #endregion

        #region دوال فلترة التقارير

        /// <summary>
        /// إنشاء تقرير HTML للموظفين مع تطبيق فلترة القسم
        /// </summary>
        public static string GenerateFilteredEmployeesHtmlReport(User? currentUser)
        {
            var employees = GetFilteredEmployees(currentUser);

            if (employees.Rows.Count == 0)
            {
                return GenerateEmptyReport(currentUser);
            }

            // استخدام نفس منطق DatabaseHelper.GenerateAllEmployeesHtml مع البيانات المفلترة
            return GenerateEmployeesHtmlFromDataTable(employees, currentUser);
        }

        /// <summary>
        /// إنشاء تقرير فارغ
        /// </summary>
        private static string GenerateEmptyReport(User? currentUser)
        {
            string title = "تقرير الموظفين";
            if (currentUser != null && (currentUser.UserType?.Trim() == "مدير القسم" || currentUser.UserType?.Trim() == "مدير قسم"))
            {
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                title = managedDepartmentId.HasValue ?
                    $"تقرير موظفي القسم {managedDepartmentId}" :
                    "تقرير الموظفين - لا يوجد قسم مُدار";
            }

            return $@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>{title}</title>
    <style>
        body {{ font-family: 'Arial', sans-serif; text-align: center; padding: 50px; }}
        .empty-message {{ color: #666; font-size: 18px; }}
    </style>
</head>
<body>
    <h1>{title}</h1>
    <div class=""empty-message"">لا توجد بيانات موظفين للعرض</div>
    <div style=""margin-top: 20px; color: #999; font-size: 14px;"">
        تم إصدار هذا التقرير بتاريخ {DateTime.Now:dd/MM/yyyy} الساعة {DateTime.Now:HH:mm}
    </div>
</body>
</html>";
        }

        /// <summary>
        /// إنشاء تقرير HTML من DataTable
        /// </summary>
        private static string GenerateEmployeesHtmlFromDataTable(DataTable employees, User? currentUser)
        {
            string title = "تقرير جميع الموظفين";
            if (currentUser != null && (currentUser.UserType?.Trim() == "مدير القسم" || currentUser.UserType?.Trim() == "مدير قسم"))
            {
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                title = managedDepartmentId.HasValue ?
                    $"🔒 تقرير موظفي القسم {managedDepartmentId} - مدير القسم: {currentUser.FullName}" :
                    "⚠️ تقرير الموظفين - لا يوجد قسم مُدار";
            }

            // يمكن استخدام نفس منطق HTML من DatabaseHelper مع تعديل العنوان
            // هنا سنستدعي الدالة الأصلية ونعدل العنوان
            string originalHtml = DatabaseHelper.GenerateAllEmployeesHtml();

            // تعديل العنوان في HTML
            originalHtml = originalHtml.Replace("<h1>قائمة الموظفين</h1>", $"<h1>{title}</h1>");

            return originalHtml;
        }

        #endregion

        #region دوال مساعدة عامة

        /// <summary>
        /// الحصول على قائمة أكواد الموظفين المسموح للمستخدم الحالي بالوصول إليهم
        /// </summary>
        public static List<int> GetAccessibleEmployeeCodes(User? currentUser)
        {
            var codes = new List<int>();

            if (currentUser == null) return codes;

            if (currentUser.UserType?.Trim() == "مدير")
            {
                // المدير العام يصل لجميع الموظفين
                var allEmployees = DatabaseHelper.GetAllEmployees();
                foreach (DataRow row in allEmployees.Rows)
                {
                    if (int.TryParse(row["كود الموظف"].ToString(), out int code))
                    {
                        codes.Add(code);
                    }
                }
            }
            else if (currentUser.UserType?.Trim() == "مدير القسم" || currentUser.UserType?.Trim() == "مدير قسم")
            {
                // مدير القسم يصل فقط لموظفي قسمه
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                if (managedDepartmentId.HasValue)
                {
                    var departmentEmployees = GetEmployeesByDepartment(managedDepartmentId.Value);
                    foreach (DataRow row in departmentEmployees.Rows)
                    {
                        if (int.TryParse(row["EmployeeCode"].ToString(), out int code))
                        {
                            codes.Add(code);
                        }
                    }
                }
            }

            return codes;
        }

        /// <summary>
        /// التحقق من نوع المستخدم
        /// </summary>
        public static bool IsDepartmentManager(User? currentUser)
        {
            return currentUser != null &&
                   (currentUser.UserType?.Trim() == "مدير القسم" || currentUser.UserType?.Trim() == "مدير قسم");
        }

        /// <summary>
        /// التحقق من كون المستخدم مدير عام
        /// </summary>
        public static bool IsGeneralManager(User? currentUser)
        {
            return currentUser != null && currentUser.UserType?.Trim() == "مدير";
        }

        /// <summary>
        /// الحصول على رسالة تحذيرية للمستخدم حول الفلترة
        /// </summary>
        public static string GetFilterWarningMessage(User? currentUser)
        {
            if (IsDepartmentManager(currentUser))
            {
                int? managedDepartmentId = DatabaseHelper.GetManagedDepartmentId(currentUser.UserId);
                if (managedDepartmentId.HasValue)
                {
                    return $"🔒 أنت مدير القسم {managedDepartmentId}. يمكنك رؤية بيانات موظفي قسمك فقط.";
                }
                else
                {
                    return "⚠️ أنت مدير قسم ولكن لم يتم تعيين قسم لك. لا يمكنك رؤية أي بيانات موظفين.";
                }
            }
            else if (IsGeneralManager(currentUser))
            {
                return "🌐 أنت مدير عام. يمكنك رؤية جميع بيانات الموظفين في النظام.";
            }
            else
            {
                return "ℹ️ أنت مستخدم عادي. يمكنك رؤية جميع البيانات.";
            }
        }

        #endregion
    }
}
