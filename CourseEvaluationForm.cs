using System;
using System.Data;
using System.Windows.Forms;
using System.Drawing;
using ClosedXML.Excel;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace EmployeeManagementSystem
{
    public partial class CourseEvaluationForm : Form
    {
        private int? selectedEvaluationId = null;

        public CourseEvaluationForm()
        {
            InitializeComponent();
            LoadCourses();
            LoadEmployeeNames();
            LoadEvaluationData();
            ClearForm();
            ThemeManager.ApplyThemeToForm(this);
        }

        private void LoadCourses()
        {
            try
            {
                var courses = DatabaseHelper.GetAllCourses();
                cmbCourse.Items.Clear();
                foreach (DataRow row in courses.Rows)
                {
                    cmbCourse.Items.Add($"{row["رقم الدورة"]} - {row["نوع الدورة"]}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تحميل الدورات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadEmployeeNames()
        {
            try
            {
                var employees = DatabaseHelper.GetAllEmployees();
                cmbEmployeeName.Items.Clear();
                foreach (DataRow row in employees.Rows)
                {
                    cmbEmployeeName.Items.Add(row["الاسم"].ToString());
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تحميل أسماء الموظفين: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadEvaluationData()
        {
            try
            {
                var table = DatabaseHelper.GetCourseEvaluations();
                dataGridView1.DataSource = table;
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تحميل بيانات التقييم: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearForm()
        {
            selectedEvaluationId = null;
            cmbCourse.SelectedIndex = -1;
            cmbEmployeeName.SelectedIndex = -1;
            numRating.Value = 1;
            txtComments.Clear();
            dtEvaluationDate.Value = DateTime.Today;
            btnAdd.Enabled = true;
            btnUpdate.Enabled = false;
            btnDelete.Enabled = false;
            dataGridView1.ClearSelection();
        }

        private bool ValidateForm()
        {
            if (cmbCourse.SelectedIndex == -1)
            {
                MessageBox.Show("الرجاء اختيار الدورة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(cmbEmployeeName.Text))
            {
                MessageBox.Show("الرجاء اختيار اسم الموظف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private CourseEvaluation GetFormData()
        {
            return new CourseEvaluation
            {
                EvaluationId = selectedEvaluationId ?? 0,
                CourseId = GetSelectedCourseId(),
                EmployeeName = cmbEmployeeName.Text,
                Rating = (int)numRating.Value,
                Comments = txtComments.Text,
                EvaluationDate = dtEvaluationDate.Value
            };
        }

        private int GetSelectedCourseId()
        {
            if (cmbCourse.SelectedIndex >= 0)
            {
                var courses = DatabaseHelper.GetAllCourses();
                return Convert.ToInt32(courses.Rows[cmbCourse.SelectedIndex]["المعرف"]);
            }
            return 0;
        }

        private async void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;

                var evaluation = GetFormData();
                await Task.Run(() => DatabaseHelper.AddCourseEvaluation(evaluation));

                MessageBox.Show("تم إضافة التقييم بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                LoadEvaluationData();
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء إضافة التقييم: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnUpdate_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm() || !selectedEvaluationId.HasValue) return;

                var evaluation = GetFormData();
                await Task.Run(() => DatabaseHelper.UpdateCourseEvaluation(evaluation));

                MessageBox.Show("تم تحديث التقييم بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                LoadEvaluationData();
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تحديث التقييم: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridView1.SelectedRows.Count == 0) return;

                if (MessageBox.Show("هل أنت متأكد من حذف التقييم؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    foreach (DataGridViewRow row in dataGridView1.SelectedRows)
                    {
                        int evaluationId = Convert.ToInt32(row.Cells["EvaluationId"].Value);
                        await Task.Run(() => DatabaseHelper.DeleteCourseEvaluation(evaluationId));
                    }

                    MessageBox.Show("تم حذف التقييم بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoadEvaluationData();
                    ClearForm();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء حذف التقييم: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private void dataGridView1_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                DataGridViewRow row = dataGridView1.Rows[e.RowIndex];
                selectedEvaluationId = Convert.ToInt32(row.Cells["EvaluationId"].Value);
                
                // تحديد الدورة
                int courseId = Convert.ToInt32(row.Cells["CourseId"].Value);
                var courses = DatabaseHelper.GetAllCourses();
                for (int i = 0; i < courses.Rows.Count; i++)
                {
                    if (Convert.ToInt32(courses.Rows[i]["المعرف"]) == courseId)
                    {
                        cmbCourse.SelectedIndex = i;
                        break;
                    }
                }

                cmbEmployeeName.Text = row.Cells["اسم الموظف"].Value?.ToString() ?? "";
                numRating.Value = Convert.ToInt32(row.Cells["التقييم"].Value);
                txtComments.Text = row.Cells["التعليقات"].Value?.ToString() ?? "";
                dtEvaluationDate.Value = Convert.ToDateTime(row.Cells["تاريخ التقييم"].Value);
              

                btnAdd.Enabled = false;
                btnUpdate.Enabled = true;
                btnDelete.Enabled = true;
            }
        }

        private async void btnExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                using (SaveFileDialog sfd = new SaveFileDialog())
                {
                    sfd.Filter = "Excel Workbook|*.xlsx";
                    sfd.FileName = "تقرير_تقييم_الدورات_" + DateTime.Now.ToString("yyyy-MM-dd") + ".xlsx";

                    if (sfd.ShowDialog() == DialogResult.OK)
                    {
                        using (XLWorkbook workbook = new XLWorkbook())
                        {
                            var worksheet = workbook.Worksheets.Add("تقييم الدورات");

                            // Add headers
                            for (int i = 0; i < dataGridView1.Columns.Count; i++)
                            {
                                worksheet.Cell(1, i + 1).Value = dataGridView1.Columns[i].HeaderText;
                            }

                            // Add data
                            for (int i = 0; i < dataGridView1.Rows.Count; i++)
                            {
                                for (int j = 0; j < dataGridView1.Columns.Count; j++)
                                {
                                    worksheet.Cell(i + 2, j + 1).Value = dataGridView1.Rows[i].Cells[j].Value?.ToString() ?? "";
                                }
                            }

                            // Format as table
                            var range = worksheet.Range(1, 1, dataGridView1.Rows.Count + 1, dataGridView1.Columns.Count);
                            range.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                            range.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                            worksheet.Row(1).Style.Fill.BackgroundColor = XLColor.LightGray;
                            worksheet.Row(1).Style.Font.Bold = true;

                            worksheet.Columns().AdjustToContents();
                            workbook.SaveAs(sfd.FileName);
                        }

                        MessageBox.Show("تم تصدير البيانات بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء تصدير البيانات: " + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}