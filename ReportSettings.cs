using System;

namespace EmployeeManagementSystem
{
    public class ReportSettings
    {
        public int Id { get; set; }
        public string ArabicHeaderText { get; set; } = string.Empty;
        public string EnglishHeaderText { get; set; } = string.Empty;
        public string CompanyLogo { get; set; } = string.Empty; // مسار الصورة
        public string ReportTitle { get; set; } = string.Empty;
        public string ManagerName { get; set; } = string.Empty;
        public string SecurityOfficerName { get; set; } = string.Empty;
        public string CommanderName { get; set; } = string.Empty;
        public DateTime LastModified { get; set; }
        public string ModifiedBy { get; set; } = string.Empty;
        public string DirectorateName { get; set; } = string.Empty;
    }
}