namespace EmployeeManagementSystem
{
    partial class DailyDetailsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            lblTitle = new Label();
            lblPeriod = new Label();
            dataGridViewDetails = new DataGridView();
            lblStats = new Label();
            btnCreateRecords = new Button();
            btnPrint = new Button();
            btnClose = new Button();
            ((System.ComponentModel.ISupportInitialize)dataGridViewDetails).BeginInit();
            SuspendLayout();
            // 
            // lblTitle
            // 
            lblTitle.AutoSize = true;
            lblTitle.Font = new Font("Cairo", 14F, FontStyle.Bold);
            lblTitle.ForeColor = Color.FromArgb(64, 64, 64);
            lblTitle.Location = new Point(492, 19);
            lblTitle.Margin = new Padding(4, 0, 4, 0);
            lblTitle.Name = "lblTitle";
            lblTitle.Size = new Size(239, 36);
            lblTitle.TabIndex = 0;
            lblTitle.Text = "التفاصيل اليومية للموظف";
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblPeriod
            // 
            lblPeriod.AutoSize = true;
            lblPeriod.Font = new Font("Cairo", 10F);
            lblPeriod.ForeColor = Color.FromArgb(100, 100, 100);
            lblPeriod.Location = new Point(605, 67);
            lblPeriod.Margin = new Padding(4, 0, 4, 0);
            lblPeriod.Name = "lblPeriod";
            lblPeriod.Size = new Size(92, 26);
            lblPeriod.TabIndex = 1;
            lblPeriod.Text = "الفترة الزمنية";
            // 
            // dataGridViewDetails
            // 
            dataGridViewDetails.AllowUserToAddRows = false;
            dataGridViewDetails.AllowUserToDeleteRows = false;
            dataGridViewDetails.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridViewDetails.BackgroundColor = Color.White;
            dataGridViewDetails.BorderStyle = BorderStyle.Fixed3D;
            dataGridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(52, 152, 219);
            dataGridViewCellStyle1.Font = new Font("Cairo", 12F, FontStyle.Bold);
            dataGridViewCellStyle1.ForeColor = Color.White;
            dataGridViewCellStyle1.SelectionBackColor = Color.FromArgb(52, 152, 219);
            dataGridViewCellStyle1.SelectionForeColor = Color.White;
            dataGridViewCellStyle1.WrapMode = DataGridViewTriState.True;
            dataGridViewDetails.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            dataGridViewDetails.ColumnHeadersHeight = 40;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = Color.White;
            dataGridViewCellStyle2.Font = new Font("Cairo", 10F, FontStyle.Bold);
            dataGridViewCellStyle2.ForeColor = Color.Black;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(230, 230, 230);
            dataGridViewCellStyle2.SelectionForeColor = Color.Black;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.False;
            dataGridViewDetails.DefaultCellStyle = dataGridViewCellStyle2;
            dataGridViewDetails.EnableHeadersVisualStyles = false;
            dataGridViewDetails.GridColor = Color.FromArgb(200, 200, 200);
            dataGridViewDetails.Location = new Point(14, 104);
            dataGridViewDetails.Margin = new Padding(4, 3, 4, 3);
            dataGridViewDetails.MultiSelect = false;
            dataGridViewDetails.Name = "dataGridViewDetails";
            dataGridViewDetails.ReadOnly = true;
            dataGridViewDetails.RightToLeft = RightToLeft.Yes;
            dataGridViewDetails.RowHeadersVisible = false;
            dataGridViewDetails.RowTemplate.Height = 35;
            dataGridViewDetails.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridViewDetails.Size = new Size(887, 369);
            dataGridViewDetails.TabIndex = 2;
            // 
            // lblStats
            // 
            lblStats.BackColor = Color.FromArgb(248, 249, 250);
            lblStats.BorderStyle = BorderStyle.FixedSingle;
            lblStats.Font = new Font("Cairo", 10F, FontStyle.Bold);
            lblStats.ForeColor = Color.FromArgb(52, 58, 64);
            lblStats.Location = new Point(14, 485);
            lblStats.Margin = new Padding(4, 0, 4, 0);
            lblStats.Name = "lblStats";
            lblStats.Padding = new Padding(12);
            lblStats.RightToLeft = RightToLeft.Yes;
            lblStats.Size = new Size(886, 57);
            lblStats.TabIndex = 3;
            lblStats.Text = "الإحصائيات: سيتم تحديثها تلقائياً";
            lblStats.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // btnCreateRecords
            // 
            btnCreateRecords.BackColor = Color.FromArgb(40, 167, 69);
            btnCreateRecords.FlatAppearance.BorderSize = 0;
            btnCreateRecords.FlatStyle = FlatStyle.Flat;
            btnCreateRecords.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnCreateRecords.ForeColor = Color.White;
            btnCreateRecords.Image = Properties.Resources.create_32px;
            btnCreateRecords.ImageAlign = ContentAlignment.MiddleLeft;
            btnCreateRecords.Location = new Point(542, 554);
            btnCreateRecords.Margin = new Padding(4, 3, 4, 3);
            btnCreateRecords.Name = "btnCreateRecords";
            btnCreateRecords.Size = new Size(210, 40);
            btnCreateRecords.TabIndex = 4;
            btnCreateRecords.Text = "إنشاء السجلات اليومية";
            btnCreateRecords.UseVisualStyleBackColor = false;
            btnCreateRecords.Click += BtnCreateRecords_Click;
            // 
            // btnPrint
            // 
            btnPrint.BackColor = Color.FromArgb(0, 123, 255);
            btnPrint.FlatAppearance.BorderSize = 0;
            btnPrint.FlatStyle = FlatStyle.Flat;
            btnPrint.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnPrint.ForeColor = Color.White;
            btnPrint.Image = Properties.Resources.print_32px;
            btnPrint.ImageAlign = ContentAlignment.MiddleLeft;
            btnPrint.Location = new Point(760, 554);
            btnPrint.Margin = new Padding(4, 3, 4, 3);
            btnPrint.Name = "btnPrint";
            btnPrint.Size = new Size(140, 40);
            btnPrint.TabIndex = 5;
            btnPrint.Text = "طباعة التقرير";
            btnPrint.TextAlign = ContentAlignment.MiddleRight;
            btnPrint.UseVisualStyleBackColor = false;
            btnPrint.Click += BtnPrint_Click;
            // 
            // btnClose
            // 
            btnClose.BackColor = Color.FromArgb(220, 53, 69);
            btnClose.FlatAppearance.BorderSize = 0;
            btnClose.FlatStyle = FlatStyle.Flat;
            btnClose.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnClose.ForeColor = Color.White;
            btnClose.Image = Properties.Resources.delete_32px;
            btnClose.ImageAlign = ContentAlignment.MiddleLeft;
            btnClose.Location = new Point(14, 554);
            btnClose.Margin = new Padding(4, 3, 4, 3);
            btnClose.Name = "btnClose";
            btnClose.Size = new Size(140, 40);
            btnClose.TabIndex = 6;
            btnClose.Text = "إغلاق";
            btnClose.UseVisualStyleBackColor = false;
            btnClose.Click += BtnClose_Click;
            // 
            // DailyDetailsForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.WhiteSmoke;
            ClientSize = new Size(915, 604);
            Controls.Add(btnClose);
            Controls.Add(btnPrint);
            Controls.Add(btnCreateRecords);
            Controls.Add(lblStats);
            Controls.Add(dataGridViewDetails);
            Controls.Add(lblPeriod);
            Controls.Add(lblTitle);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Margin = new Padding(4, 3, 4, 3);
            MaximizeBox = false;
            Name = "DailyDetailsForm";
            RightToLeft = RightToLeft.No;
            StartPosition = FormStartPosition.CenterParent;
            Text = "التفاصيل اليومية";
            ((System.ComponentModel.ISupportInitialize)dataGridViewDetails).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Label lblTitle;
        private Label lblPeriod;
        private DataGridView dataGridViewDetails;
        private Label lblStats;
        private Button btnCreateRecords;
        private Button btnPrint;
        private Button btnClose;
    }
}