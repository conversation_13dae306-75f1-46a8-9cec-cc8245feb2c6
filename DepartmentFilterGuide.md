# دليل تطبيق فلترة الأقسام على النماذج

## نظرة عامة

تم إنشاء نظام فلترة شامل يضمن أن مدير القسم يرى فقط بيانات موظفي قسمه في جميع النماذج. هذا الدليل يوضح كيفية تطبيق الفلترة على النماذج المختلفة.

## الكلاسات المساعدة

### 1. EmployeeDepartmentHelper
الكلاس الرئيسي للفلترة ويحتوي على:
- `GetFilteredEmployees()` - فلترة قائمة الموظفين
- `GetFilteredEmployeesForAttendance()` - فلترة موظفين للحضور
- `GetFilteredVacations()` - فلترة الإجازات
- `GetFilteredAttendanceByDateRange()` - فلترة بيانات الحضور
- `ValidateUserAccess()` - التحقق من صلاحية الوصول

### 2. DepartmentFilterManager
مدير الفلترة المركزي ويوفر:
- `ApplyDepartmentFilter()` - تطبيق فلترة عامة
- `CanAddRecord()` - التحقق من صلاحية الإضافة
- `CanEditRecord()` - التحقق من صلاحية التعديل
- `CanDeleteRecord()` - التحقق من صلاحية الحذف

## كيفية تطبيق الفلترة على نموذج جديد

### الخطوة 1: إضافة المستخدم الحالي

```csharp
public partial class YourForm : Form
{
    private User? currentUser = null;

    public YourForm()
    {
        InitializeComponent();
    }

    // Constructor جديد يستقبل المستخدم الحالي
    public YourForm(User currentUser) : this()
    {
        this.currentUser = currentUser;
        
        // عرض رسالة تحذيرية حول الفلترة
        if (EmployeeDepartmentHelper.IsDepartmentManager(currentUser))
        {
            string warningMessage = EmployeeDepartmentHelper.GetFilterWarningMessage(currentUser);
            MessageBox.Show(warningMessage, "معلومات الفلترة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
```

### الخطوة 2: تحديث دوال تحميل البيانات

#### للموظفين:
```csharp
private void LoadEmployees()
{
    // بدلاً من: var employees = DatabaseHelper.GetAllEmployees();
    var employees = EmployeeDepartmentHelper.GetFilteredEmployees(currentUser);
    
    // باقي الكود...
}
```

#### للحضور والغياب:
```csharp
private void LoadAttendanceData()
{
    // بدلاً من: var attendance = DatabaseHelper.GetAttendanceByDateRange(start, end);
    var attendance = EmployeeDepartmentHelper.GetFilteredAttendanceByDateRange(currentUser, start, end);
    
    // باقي الكود...
}
```

#### للإجازات:
```csharp
private void LoadVacations()
{
    // بدلاً من: var vacations = DatabaseHelper.GetAllVacations();
    var vacations = EmployeeDepartmentHelper.GetFilteredVacations(currentUser);
    
    // باقي الكود...
}
```

### الخطوة 3: تحديث دوال البحث

```csharp
private void SearchData(string searchTerm)
{
    DataTable results;
    
    if (EmployeeDepartmentHelper.IsDepartmentManager(currentUser))
    {
        // تطبيق فلترة إضافية للبحث
        var allResults = DatabaseHelper.SearchYourData(searchTerm);
        var accessibleCodes = EmployeeDepartmentHelper.GetAccessibleEmployeeCodes(currentUser);
        
        results = allResults.Clone();
        foreach (DataRow row in allResults.Rows)
        {
            if (int.TryParse(row["EmployeeCode"].ToString(), out int empCode) && 
                accessibleCodes.Contains(empCode))
            {
                results.ImportRow(row);
            }
        }
    }
    else
    {
        results = DatabaseHelper.SearchYourData(searchTerm);
    }
    
    dataGridView.DataSource = results;
}
```

### الخطوة 4: تحديث دوال الإضافة والتعديل والحذف

```csharp
private void AddRecord()
{
    // التحقق من الصلاحية
    if (!DepartmentFilterManager.CanAddRecord(currentUser))
    {
        MessageBox.Show("ليس لديك صلاحية لإضافة سجلات", "غير مخول", 
            MessageBoxButtons.OK, MessageBoxIcon.Warning);
        return;
    }
    
    // باقي كود الإضافة...
}

private void EditRecord(int employeeCode)
{
    // التحقق من الصلاحية
    if (!DepartmentFilterManager.CanEditRecord(currentUser, employeeCode))
    {
        MessageBox.Show("ليس لديك صلاحية لتعديل هذا السجل", "غير مخول", 
            MessageBoxButtons.OK, MessageBoxIcon.Warning);
        return;
    }
    
    // باقي كود التعديل...
}

private void DeleteRecord(int employeeCode)
{
    // التحقق من الصلاحية
    if (!DepartmentFilterManager.CanDeleteRecord(currentUser, employeeCode))
    {
        MessageBox.Show("ليس لديك صلاحية لحذف هذا السجل", "غير مخول", 
            MessageBoxButtons.OK, MessageBoxIcon.Warning);
        return;
    }
    
    // باقي كود الحذف...
}
```

### الخطوة 5: تحديث MainForm

```csharp
// في MainForm، تحديث استدعاء النموذج
private void OpenYourForm()
{
    // بدلاً من: OpenForm(new YourForm());
    OpenForm(new YourForm(CurrentUser));
}
```

## النماذج التي تم تحديثها بالفعل

### ✅ تم التحديث:
1. **Form1** (إدارة الموظفين) - مُحدث بالكامل
2. **AttendanceForm** (الحضور والغياب) - مُحدث بالكامل
3. **VacationForm** (الإجازات) - مُحدث بالكامل
4. **UserForm** (إدارة المستخدمين) - مُحدث مسبقاً

### ⏳ يحتاج تحديث:
1. **DocumentManagementForm** (إدارة المستندات)
2. **AttendanceReportForm** (تقارير الحضور)
3. **CourseForm** (إدارة الدورات)
4. **WorkPeriodForm** (فترات العمل)
5. **DepartmentForm** (إدارة الأقسام)

## نصائح مهمة

### 1. التحقق من الصلاحيات
```csharp
// دائماً تحقق من الصلاحية قبل عرض أو تعديل البيانات
if (!EmployeeDepartmentHelper.ValidateUserAccess(currentUser, employeeCode))
{
    // منع الوصول
    return;
}
```

### 2. تحديث عناوين النوافذ
```csharp
// استخدم هذه الدالة لتحديث عنوان النافذة
string title = EmployeeDepartmentHelper.GetWindowTitle(currentUser, recordCount);
this.Text = title.Replace("نظام إدارة الموظفين", "اسم نموذجك");
```

### 3. رسائل التحذير
```csharp
// عرض رسالة تحذيرية للمستخدم حول الفلترة
string warningMessage = EmployeeDepartmentHelper.GetFilterWarningMessage(currentUser);
MessageBox.Show(warningMessage, "معلومات الفلترة", MessageBoxButtons.OK, MessageBoxIcon.Information);
```

## اختبار الفلترة

### 1. إنشاء مستخدمين للاختبار:
- مدير عام (UserType = "مدير")
- مدير قسم (UserType = "مدير القسم" أو "مدير قسم")
- مستخدم عادي

### 2. تعيين أقسام:
- تأكد من وجود أقسام في جدول Departments
- تعيين ManagerUserId لكل قسم
- ربط الموظفين بالأقسام في جدول Users

### 3. اختبار السيناريوهات:
- تسجيل دخول مدير عام ← يجب رؤية جميع البيانات
- تسجيل دخول مدير قسم ← يجب رؤية بيانات قسمه فقط
- محاولة الوصول لبيانات قسم آخر ← يجب منع الوصول

## استكشاف الأخطاء

### مشكلة: مدير القسم لا يرى أي بيانات
**الحل:**
1. تحقق من تعيين ManagerUserId في جدول Departments
2. تحقق من ربط الموظفين بالقسم في جدول Users
3. تحقق من أن IsActive = 1 للمستخدمين

### مشكلة: المدير العام لا يرى جميع البيانات
**الحل:**
1. تحقق من أن UserType = "مدير" بالضبط
2. تحقق من عدم وجود مسافات إضافية في UserType

### مشكلة: رسائل خطأ عند تحميل البيانات
**الحل:**
1. تحقق من وجود الأعمدة المطلوبة في قاعدة البيانات
2. تحقق من صحة أسماء الأعمدة في الاستعلامات
3. تحقق من صحة أنواع البيانات
