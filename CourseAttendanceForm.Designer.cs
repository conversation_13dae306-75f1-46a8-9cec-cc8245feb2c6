namespace EmployeeManagementSystem
{
    partial class CourseAttendanceForm
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            groupBox1 = new GroupBox();
            txtNotes = new TextBox();
            lblNotes = new Label();
            chkIsPresent = new CheckBox();
            dtAttendanceDate = new DateTimePicker();
            lblAttendanceDate = new Label();
            cmbEmployeeName = new ComboBox();
            lblEmployeeName = new Label();
            cmbCourse = new ComboBox();
            lblCourse = new Label();
            btnExportExcel = new Button();
            btnClear = new Button();
            btnDelete = new Button();
            btnUpdate = new Button();
            btnAdd = new Button();
            dataGridView1 = new DataGridView();
            toolTip1 = new ToolTip(components);
            groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).BeginInit();
            SuspendLayout();
            // 
            // groupBox1
            // 
            groupBox1.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBox1.Controls.Add(txtNotes);
            groupBox1.Controls.Add(lblNotes);
            groupBox1.Controls.Add(chkIsPresent);
            groupBox1.Controls.Add(dtAttendanceDate);
            groupBox1.Controls.Add(lblAttendanceDate);
            groupBox1.Controls.Add(cmbEmployeeName);
            groupBox1.Controls.Add(lblEmployeeName);
            groupBox1.Controls.Add(cmbCourse);
            groupBox1.Controls.Add(lblCourse);
            groupBox1.Controls.Add(btnExportExcel);
            groupBox1.Controls.Add(btnClear);
            groupBox1.Controls.Add(btnDelete);
            groupBox1.Controls.Add(btnUpdate);
            groupBox1.Controls.Add(btnAdd);
            groupBox1.Font = new Font("Cairo", 12F, FontStyle.Bold);
            groupBox1.Location = new Point(12, 12);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(1000, 280);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            groupBox1.Text = "بيانات حضور الدورة";
            // 
            // txtNotes
            // 
            txtNotes.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtNotes.Font = new Font("Cairo", 12F);
            txtNotes.Location = new Point(20, 120);
            txtNotes.Multiline = true;
            txtNotes.Name = "txtNotes";
            txtNotes.RightToLeft = RightToLeft.Yes;
            txtNotes.Size = new Size(400, 80);
            txtNotes.TabIndex = 4;
            // 
            // lblNotes
            // 
            lblNotes.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblNotes.AutoSize = true;
            lblNotes.Location = new Point(426, 123);
            lblNotes.Name = "lblNotes";
            lblNotes.Size = new Size(78, 30);
            lblNotes.TabIndex = 12;
            lblNotes.Text = "ملاحظات:";
            // 
            // chkIsPresent
            // 
            chkIsPresent.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            chkIsPresent.AutoSize = true;
            chkIsPresent.Font = new Font("Cairo", 12F, FontStyle.Bold);
            chkIsPresent.Location = new Point(814, 123);
            chkIsPresent.Name = "chkIsPresent";
            chkIsPresent.RightToLeft = RightToLeft.No;
            chkIsPresent.Size = new Size(66, 34);
            chkIsPresent.TabIndex = 3;
            chkIsPresent.Text = "حاضر";
            chkIsPresent.UseVisualStyleBackColor = true;
            // 
            // dtAttendanceDate
            // 
            dtAttendanceDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            dtAttendanceDate.Font = new Font("Cairo", 12F);
            dtAttendanceDate.Format = DateTimePickerFormat.Short;
            dtAttendanceDate.Location = new Point(580, 80);
            dtAttendanceDate.Name = "dtAttendanceDate";
            dtAttendanceDate.Size = new Size(300, 37);
            dtAttendanceDate.TabIndex = 2;
            // 
            // lblAttendanceDate
            // 
            lblAttendanceDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblAttendanceDate.AutoSize = true;
            lblAttendanceDate.Location = new Point(886, 83);
            lblAttendanceDate.Name = "lblAttendanceDate";
            lblAttendanceDate.Size = new Size(102, 30);
            lblAttendanceDate.TabIndex = 9;
            lblAttendanceDate.Text = "تاريخ الحضور:";
            // 
            // cmbEmployeeName
            // 
            cmbEmployeeName.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbEmployeeName.Font = new Font("Cairo", 12F);
            cmbEmployeeName.FormattingEnabled = true;
            cmbEmployeeName.Location = new Point(20, 40);
            cmbEmployeeName.Name = "cmbEmployeeName";
            cmbEmployeeName.RightToLeft = RightToLeft.No;
            cmbEmployeeName.Size = new Size(400, 38);
            cmbEmployeeName.TabIndex = 1;
            // 
            // lblEmployeeName
            // 
            lblEmployeeName.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblEmployeeName.AutoSize = true;
            lblEmployeeName.Location = new Point(426, 43);
            lblEmployeeName.Name = "lblEmployeeName";
            lblEmployeeName.Size = new Size(113, 30);
            lblEmployeeName.TabIndex = 7;
            lblEmployeeName.Text = "اسم الموظف:";
            // 
            // cmbCourse
            // 
            cmbCourse.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbCourse.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbCourse.Font = new Font("Cairo", 12F);
            cmbCourse.FormattingEnabled = true;
            cmbCourse.Location = new Point(580, 40);
            cmbCourse.Name = "cmbCourse";
            cmbCourse.RightToLeft = RightToLeft.No;
            cmbCourse.Size = new Size(300, 38);
            cmbCourse.TabIndex = 0;
            // 
            // lblCourse
            // 
            lblCourse.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblCourse.AutoSize = true;
            lblCourse.Location = new Point(886, 43);
            lblCourse.Name = "lblCourse";
            lblCourse.Size = new Size(61, 30);
            lblCourse.TabIndex = 5;
            lblCourse.Text = "الدورة:";
            // 
            // btnExportExcel
            // 
            btnExportExcel.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnExportExcel.BackColor = Color.Transparent;
            btnExportExcel.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnExportExcel.Image = Properties.Resources.microsoft_excel_2019_32px;
            btnExportExcel.ImageAlign = ContentAlignment.MiddleRight;
            btnExportExcel.Location = new Point(431, 228);
            btnExportExcel.Name = "btnExportExcel";
            btnExportExcel.Size = new Size(108, 46);
            btnExportExcel.TabIndex = 8;
            btnExportExcel.Text = "تصدير";
            btnExportExcel.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnExportExcel, "تصدير إلى Excel");
            btnExportExcel.UseVisualStyleBackColor = false;
            btnExportExcel.Click += btnExportExcel_Click;
            // 
            // btnClear
            // 
            btnClear.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnClear.BackColor = Color.Transparent;
            btnClear.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnClear.Image = Properties.Resources.clear_formatting_32px;
            btnClear.ImageAlign = ContentAlignment.MiddleRight;
            btnClear.Location = new Point(545, 228);
            btnClear.Name = "btnClear";
            btnClear.Size = new Size(90, 46);
            btnClear.TabIndex = 7;
            btnClear.Text = "إفراغ";
            btnClear.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnClear, "إفراغ الحقول");
            btnClear.UseVisualStyleBackColor = false;
            btnClear.Click += btnClear_Click;
            // 
            // btnDelete
            // 
            btnDelete.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnDelete.BackColor = Color.Transparent;
            btnDelete.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnDelete.Image = Properties.Resources.remove_32px;
            btnDelete.ImageAlign = ContentAlignment.MiddleRight;
            btnDelete.Location = new Point(641, 228);
            btnDelete.Name = "btnDelete";
            btnDelete.Size = new Size(90, 46);
            btnDelete.TabIndex = 9;
            btnDelete.Text = "حذف";
            btnDelete.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnDelete, "حذف");
            btnDelete.UseVisualStyleBackColor = false;
            btnDelete.Click += btnDelete_Click;
            // 
            // btnUpdate
            // 
            btnUpdate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnUpdate.BackColor = Color.Transparent;
            btnUpdate.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnUpdate.Image = Properties.Resources.edit_profile_32px;
            btnUpdate.ImageAlign = ContentAlignment.MiddleRight;
            btnUpdate.Location = new Point(737, 228);
            btnUpdate.Name = "btnUpdate";
            btnUpdate.Size = new Size(102, 46);
            btnUpdate.TabIndex = 6;
            btnUpdate.Text = "تعديل";
            btnUpdate.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnUpdate, "تعديل");
            btnUpdate.UseVisualStyleBackColor = false;
            btnUpdate.Click += btnUpdate_Click;
            // 
            // btnAdd
            // 
            btnAdd.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnAdd.BackColor = Color.Transparent;
            btnAdd.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnAdd.Image = Properties.Resources.ok_32px;
            btnAdd.ImageAlign = ContentAlignment.MiddleRight;
            btnAdd.Location = new Point(845, 228);
            btnAdd.Name = "btnAdd";
            btnAdd.Size = new Size(101, 46);
            btnAdd.TabIndex = 5;
            btnAdd.Text = "إضافة";
            btnAdd.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnAdd, "إضافة");
            btnAdd.UseVisualStyleBackColor = false;
            btnAdd.Click += btnAdd_Click;
            // 
            // dataGridView1
            // 
            dataGridView1.AllowUserToAddRows = false;
            dataGridView1.AllowUserToDeleteRows = false;
            dataGridView1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView1.Location = new Point(12, 298);
            dataGridView1.Name = "dataGridView1";
            dataGridView1.ReadOnly = true;
            dataGridView1.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView1.Size = new Size(1000, 350);
            dataGridView1.TabIndex = 10;
            dataGridView1.CellClick += dataGridView1_CellClick;
            // 
            // CourseAttendanceForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1024, 660);
            Controls.Add(dataGridView1);
            Controls.Add(groupBox1);
            Name = "CourseAttendanceForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "إدارة حضور الدورات التدريبية";
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).EndInit();
            ResumeLayout(false);
        }

        private GroupBox groupBox1;
        private Label lblCourse;
        private ComboBox cmbCourse;
        private Label lblEmployeeName;
        private ComboBox cmbEmployeeName;
        private Label lblAttendanceDate;
        private DateTimePicker dtAttendanceDate;
        private CheckBox chkIsPresent;
        private Label lblNotes;
        private TextBox txtNotes;
        private Button btnAdd;
        private Button btnUpdate;
        private Button btnDelete;
        private Button btnClear;
        private Button btnExportExcel;
        private DataGridView dataGridView1;
        private ToolTip toolTip1;
    }
}